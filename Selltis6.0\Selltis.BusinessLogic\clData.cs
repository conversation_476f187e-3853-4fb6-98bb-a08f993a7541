﻿using System;

//Owner: MI since 1/1/10, originally RH
//MI 4/3/09 Moved GenerateSQL, RemoveRedundantConditions to clSQL, added inheriting from clSQL

using System.Web;
using System.Text.RegularExpressions;
using System.Text;
using System.Collections;
using System.Data;
using System.Diagnostics;

namespace Selltis.BusinessLogic
{
	public class clData : clSQL
	{


		//Commented objects are declared in clSQL
		//Dim goP As clProject
		//Dim goMeta As clMetaData
		//Dim goTR As clTransform
		//Dim goData As clData
		//Dim goErr As clError
		//Dim goLog As clLog
		//Dim goDef As clDefaults
		private clScrMngRowSet goScr;
		//Dim goPerm As clPerm
		private ClUI goUI;

		public Hashtable dFields = new Hashtable();
		public Stopwatch sw = new Stopwatch();

		public struct LastSelectedStructure
		{

			public DateTime StartDate;
			public DateTime EndDate;

			public string ViewName;
			public string ViewDescription;
			public string ViewFile;
			public string ViewPageID;
			public string ViewSection; //*** MI 8/25/06
			public string ViewMetadataString; //*** MI 8/25/06
			public string ViewCreatorID; //*** MI 8/25/06

			public string FormName;
			public string FormDescription;
			public string FormFile;
			public string FormPageID;

			public string DesktopName;
			public string DesktopDescription;
			public string DesktopPageID;
			public string DesktopSection; //*** MI 8/25/06
			public string DesktopMetadataString; //*** MI 8/25/06
			public string DesktopCreatorID; //*** MI 8/25/06

			public string SelectedRecordID;
			public string SelectedRecordFile;



		}

		//Public sw As New System.Diagnostics.Stopwatch
		public LastSelectedStructure LastSelected = new LastSelectedStructure();
		//From clData declarations
		public string sCurrentDBPath; //Path of ---------------
		public bool bDBOpen; //True if a DB is open

		public DataTable dtLinks = new DataTable();
		public DataTable dtTables = new DataTable();
		public DataTable dtFields = new DataTable();
		public DataTable dtLists = new DataTable();
		public DataTable dtListsExtendedInfo = new DataTable();
		public clList goList = new clList();
		private Microsoft.VisualBasic.Collection collSelectedItem = new Microsoft.VisualBasic.Collection();
		public string GetFileVersion(string FName, string sPath)
		{


			string lpath = "";
			try
			{


				if (sPath.ToLower() == "temp")
				{
					lpath = goP.sPath + "\\Temp\\";
				}
				else if (sPath.ToLower() == "templates")
				{
					lpath = goP.sPath + "\\Templates\\";
				}
				else
				{
					lpath = sPath;
				}
				lpath = goTR.Replace(lpath, "\\\\", "\\");

				System.IO.FileInfo fi = new System.IO.FileInfo(lpath + FName);


				return fi.LastWriteTime.ToString();


			}
			catch (Exception ex)
			{

				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, lpath, , lpath)
				//End If

				return "";

			}

		}
		public bool AddNNLink(string sTable, string sFields, string sValues)
		{
			System.Data.SqlClient.SqlConnection tempVar = null;
			return AddNNLink(sTable, sFields, sValues, ref tempVar);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Function AddNNLink(ByVal sTable As String, ByVal sFields As String, ByVal sValues As String, Optional ByRef connection As SqlClient.SqlConnection = Nothing) As Boolean
		public bool AddNNLink(string sTable, string sFields, string sValues, ref System.Data.SqlClient.SqlConnection connection)
		{
			//MI 3/3/07 Added call to GenSQLForAddingNNLinks.

			//PURPOSE:
			//		Adds a NN link record if it does not already exist
			//PARAMETERS:
			//		sTable: Link table name
			//       sFields: comma delimited list of fields
			//       sValues: single quoted comma delimited list of values
			//RETURNS:
			//		True if successful, false or error if not.
			//AUTHOR: RH


			string sProc = "clData::AddNNLink";

			// Try
			System.Data.SqlClient.SqlConnection sqlConnection1 = null;
				if (connection == null)
				{
					sqlConnection1 = goData.GetConnection();
				}
				else
				{
					sqlConnection1 = connection;
				}

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

				cmd.CommandText = GenSQLForAddingNNLinks(sTable, sFields, sValues); //*** MI 3/3/07

				cmd.CommandType = System.Data.CommandType.Text;
				cmd.Connection = sqlConnection1;
				cmd.CommandTimeout = 300;

				if (cmd.ExecuteNonQuery() == 1)
				{
					if (connection == null)
					{
						sqlConnection1.Close();
					}
					return true;
				}
				else
				{
					if (connection == null)
					{
						sqlConnection1.Close();
					}
					return false;
				}


			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If

			//End Try

		}

		public bool DatasetToTextFile(DataSet par_oDataset, int par_iMaxCharsPerCell = 800, string sExcelFileName = "")
		{
			//PURPOSE:        
			//   given a dataset, converts one or more datatables to text file for opening in ms excel.
			//   creates a send job for pclink to open files as excel spreadsheet(s)
			//PARAMETERS:
			//  oDataset:    a dataset with one or more datatables, each representing a different spreadsheet in excel.  "view" names given as datatable names.
			//RETURNS:
			//  true if successful, false if not
			string sProc = "clData::DatasetToTextFile";
			//   Try
			int i = 0;
				int j = 0;
				int x = 0;

				string sFiles = "";
				string sViewNames = "";

				int iViewCount = par_oDataset.Tables.Count;

				for (x = 0; x < iViewCount; x++)
				{

					string sPath = HttpContext.Current.Server.MapPath("~/Temp/");
					string sFilename = "XLS_" + goP.GetUserCode() + "_" + goData.GenGuid() + ".txt";

					DataTable oTable = par_oDataset.Tables[x];

					//info counters
					int iColumnCount = oTable.Columns.Count;
					int iRowcount = oTable.Rows.Count;
					int iCharCount = par_iMaxCharsPerCell;
					if (iCharCount > 800)
					{
						iCharCount = 800;
					}
					if (iCharCount < 0)
					{
						iCharCount = 0;
					}

					//let's test for directory
					if (System.IO.Directory.Exists(HttpContext.Current.Server.MapPath("~\\Temp\\")))
					{
						//do nothing
					}
					else
					{
					try
					{
						//try to create directory
						System.IO.Directory.CreateDirectory(HttpContext.Current.Server.MapPath("..") + "\\Temp");
					}
					catch (Exception ex)
					{
					//goErr.SetError(ex, 45105, sProc)
					return Convert.ToBoolean("");
					}
				}

					string sHeader = "";
					string sText = "";
					string sField = "";
					string sFirstChar = "";

					System.IO.StreamWriter oStream;
					oStream = System.IO.File.AppendText(sPath + sFilename);

					//excel doesn't accept more than 256 columns in a spreadsheet
					if (iColumnCount > 256)
					{
						oStream.WriteLine("Microsoft Excel does not support spreadsheets with more than 256 columns.");
						goto DONOTREADDATA;
					}

					//excel doesn't support more than 65536 rows
					if (iRowcount > 65535)
					{
						oStream.WriteLine("Microsoft Excel does not support spreadsheets with more than 65535 rows.");
						goto DONOTREADDATA;
					}

					//get column headers and add to text string
					for (i = 0; i < iColumnCount; i++)
					{
						sHeader = oTable.Columns[i].Caption;
						if (i == iColumnCount - 1)
						{
							sText = sText + sHeader;
						}
						else
						{
							sText = sText + sHeader + '\t';
						}
					}
					oStream.WriteLine(sText);

					//get row data
					for (i = 0; i < iRowcount; i++)
					{
						sText = "";
						DataRow oDataRow = oTable.Rows[i];
						for (j = 0; j < iColumnCount; j++)
						{
							sField = Convert.ToString(oDataRow[j]);
							if (sField.Length > iCharCount)
							{
								//Excel allows 32,767 characters in a cell, only 1024 visible on the screen or printout
								//Limitations of the Replace function = will only handle a string up to approximately 850 characters
								sField = goTR.Replace(sField, "\t", "     ");
								sField = sField.Substring(0, iCharCount) + " <<more>>";
							}
							//escape first character is string could be construed as possible excel function
							sFirstChar = sField.Substring(0, 1);
							switch (sFirstChar)
							{
								case "*":
								case "+":
								case "-":
								case "&":
								case "#":
								case "'":
								case "!":
								case "=":
								case "/":
								case "%":
									sField = "'" + sField;
									break;
								default:
									sField = sField;
									break;
							}
							//Excel breaks records on hard returns (and tabs due to the method we are using) so lets replace in data
							sField = goTR.Replace(sField, "\r\n", "<CR>");
							sField = goTR.Replace(sField, "\n", "<CR>");
							sField = goTR.Replace(sField, "\r", "<CR>");
							//concatenate
							if (j == iColumnCount - 1)
							{
								sText = sText + sField;
							}
							else
							{
								sText = sText + sField + '\t';
							}
						}
						oStream.WriteLine(sText);
					}

					oStream.Close();

	DONOTREADDATA:

					if (sFiles == "")
					{
						sFiles = sFilename;
					}
					else
					{
						sFiles = sFiles + "|~{" + sFilename;
					}

					if (sViewNames == "")
					{
						sViewNames = oTable.TableName;
						//sViewNames = goTR.StripIllegalChars(par_oDataset.DataSetName)
					}
					else
					{
						sViewNames = sViewNames + "|~{" + oTable.TableName;
					}

				}

				//DESKTOPNAME = Activities___My
				//FILE=XLS_WGT_558100e3-34cf-4582-b16e-b68dce7e9b73.txt|~{XLS_WGT_8c0eadcd-b9fb-4607-b411-542600985ab4.txt
				//MODE = EXCEL
				//US_NAME=Send to Excel Queue Item
				//OBJECTTOSEND = Desktop
				//SENDNAME=Desktop to Excel 'Activities___My'
				//VIEWNAME=My_Open_Activities|~{My_Closed_Activities

				string[] aFiles = Microsoft.VisualBasic.Strings.Split(sFiles, "|~{");
				string[] aViews = Microsoft.VisualBasic.Strings.Split(sViewNames, "|~{");
				if (aFiles.Length == aViews.Length)
				{

					//create send job
					string sSendJob = "";
					goTR.StrWrite(ref sSendJob, "US_NAME", "Send to Excel Queue Item");
					goTR.StrWrite(ref sSendJob, "MODE", "EXCEL");
					goTR.StrWrite(ref sSendJob, "OBJECTTOSEND", "Dataset");
					goTR.StrWrite(ref sSendJob, "SENDNAME", "Dataset to Excel '" + goTR.StripIllegalChars(par_oDataset.DataSetName) + "'");
					goTR.StrWrite(ref sSendJob, "DESKTOPNAME", goTR.StripIllegalChars(par_oDataset.DataSetName));
					goTR.StrWrite(ref sSendJob, "FILE", sFiles);
					goTR.StrWrite(ref sSendJob, "VIEWNAME", sViewNames);
					goTR.StrWrite(ref sSendJob, "OBJECTTYPE", "Dataset");
					goTR.StrWrite(ref sSendJob, "MISCINFO", sExcelFileName);

					string sPageID = "SNQ_" + goData.GenSUID("XX");
					if (!goMeta.PageWrite(goP.GetUserTID(), sPageID, sSendJob))
					{

						goErr.SetWarning(0, sProc, "Error creating send job for datatable.");
						return false;
					}
					else
					{
						//goUI.ExecuteSendPopup = True
					}

				}
				else
				{
					goErr.SetWarning(0, sProc, "aViews has a different number of elements from aFiles.");
					return false;

				}

				return true;

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, , sProc)
			//    End If
			//End Try
		}


		public string MapPath(string sFolder)
		{


			return HttpContext.Current.Server.MapPath(sFolder);

		}
		public bool SetSelectedRecord(string par_sFile, string par_sID)
		{

			//Set selected item for file to collection. Returns true on success, false on error.
			try
			{
				if (collSelectedItem.Contains(par_sFile))
				{
					collSelectedItem.Remove(par_sFile);
				}
				collSelectedItem.Add(par_sID, par_sFile);

				LastSelected.SelectedRecordID = par_sID;
				LastSelected.SelectedRecordFile = par_sFile;

				return true;
			}
			catch (Exception ex)
			{
				//==> raise error
				return false;
			}

		}
		public bool ClearSelectedRecords()
		{

			//Clears selected record collection
			try
			{
				collSelectedItem.Clear();
				LastSelected.SelectedRecordID = "";
				LastSelected.SelectedRecordFile = "";
				return true;
			}
			catch (Exception ex)
			{
				//==> raise error
				return false;
			}



		}

		public bool CopyRecord(ref clRowSet par_oRSSource, ref clRowSet par_pRSTarget)
		{
			//AUTHOR: RH 4/9/07
			//PURPOSE:
			//       Copies one record from a rowset to another
			//PARAMETERS:
			//       par_oRSSource:                 Source rowset
			//       par_pRSTarget:                  Target rowset    
			//RETURNS:
			//       True on success, or false on error.

			string sProc = "clDate::CopyRecord";

			try
			{

				if (par_oRSSource.iRSType == 3) //Read only: must be add or edit to ensure all fields are present
				{
					return false;
				}
				if (par_pRSTarget.iRSType == 3) //Read only: must be editable
				{
					return false;
				}
				if (par_pRSTarget.sRSFile != par_oRSSource.sRSFile) //different files
				{
					return false;
				}


				int i = 0;
				clArray aFields = null;
				clArray aLinks = null;
				clArray aLinkVals = new clArray();

				aFields = GetFields(par_oRSSource.sRSFile);
				aLinks = GetLinks(par_oRSSource.sRSFile);

// INSTANT C# NOTE: There is no C# equivalent to VB's implicit 'once only' variable initialization within loops, so the following variable declaration has been placed prior to the loop:
				Guid resGuid = new Guid();
				for (i = 1; i <= aFields.GetDimension(); i++)
				{
					//Exclude non-editable fields
					if (aFields.GetItem(i).ToUpper() != "DTT_CREATIONTIME" && aFields.GetItem(i).ToUpper() != "BI__ID" && aFields.GetItem(i).ToUpper() != "GID_ID" && aFields.GetItem(i).ToUpper() != "DTE_CREATIONTIME" && aFields.GetItem(i).ToUpper() != "TME_CREATIONTIME" && aFields.GetItem(i).ToUpper() != "GID_JCIID")
					{

						if (aFields.GetItem(i).StartsWith("GID_"))
						{
	//						Dim resGuid As Guid
							bool bValid = Guid.TryParse(Convert.ToString(par_oRSSource.GetFieldVal(aFields.GetItem(i))), out resGuid);
							if (bValid)
							{
								par_pRSTarget.SetFieldVal(aFields.GetItem(i), resGuid, clC.SELL_SYSTEM);
							}
						}
						else
						{
							par_pRSTarget.SetFieldVal(aFields.GetItem(i), par_oRSSource.GetFieldVal(aFields.GetItem(i), clC.SELL_SYSTEM), clC.SELL_SYSTEM);
						}

					}
				}

				for (i = 1; i <= aLinks.GetDimension(); i++)
				{
					par_pRSTarget.SetLinkVal(aLinks.GetItem(i), par_oRSSource.GetLinkVal(aLinks.GetItem(i), ref aLinkVals));
					aLinkVals.ClearArray();
				}

				return true;
			}
			catch (Exception ex)
			{

				return false;

			}

		}

		public string DataType(string par_sVar)
		{
			////CS OK.

			return par_sVar.GetType().ToString();

			////Encapsulation to ease the passage to wd 7/8/9 where this method generate a warning
			//result(DataType(par_sVar))
		}
		public string GenerateID(string par_sFileName = "")
		{
				string tempGenerateID = null;
			//Generates a Selltis unique ID (SUID). For metadata page SUID, pass "XX"
			//as par_sFileName.

			string sProc = "clData::GenerateID";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
			System.Data.SqlClient.SqlConnection myConnection = goData.GetConnection();

			System.Data.SqlClient.SqlCommand myCommand = new System.Data.SqlClient.SqlCommand("pGenerateSUID", myConnection);
			myCommand.CommandType = System.Data.CommandType.StoredProcedure;

			System.Data.SqlClient.SqlParameter sTable = new System.Data.SqlClient.SqlParameter("@sTable", SqlDbType.Char);
			sTable.Value = par_sFileName;
			myCommand.Parameters.Add(sTable);

			//Create a SqlParameter object to hold the output parameter value
			System.Data.SqlClient.SqlParameter uResult = new System.Data.SqlClient.SqlParameter("@uResult", SqlDbType.UniqueIdentifier);

			//IMPORTANT - must set Direction as Output
			uResult.Direction = ParameterDirection.Output;

			//Finally, add the parameter to the Command's Parameters collection
			myCommand.Parameters.Add(uResult);

			//Call the sproc...
			System.Data.SqlClient.SqlDataReader reader = myCommand.ExecuteReader();

			//Now you can grab the output parameter's value...
			string ID = Convert.ToString(uResult.Value);

			tempGenerateID = ID;

			myConnection.Close();

			return tempGenerateID;
		}


		public bool RunSQLQuery(string par_sQuery)
		{
			//MI 10/24/08 Fixed bug in Left(par_sQuery, ...): arithmetic was done on the result of Left instead of inside it.
			//MI 10/15/08 Added logging query in errors.
			//MI 3/22/07 Added error 2714 to Select Case.
			//MI 2/1/07 Removed trying to capture result in case of UPDATE, INSERT, DELETE.
			//MI 1/30/07 Added exception Case 512
			//MI 11/1/06
			//PURPOSE:
			//       Execute dynamic SQL query which returns only True/False and
			//       no recordsets.
			//PARAMETERS:
			//       par_sQuery: Query string to execute.
			//RETURNS:
			//       True if successful, False otherwise.

			string sProc = "clData::RunSQLQuery";
			goLog.Log(sProc, "par_sQuery: '" + par_sQuery.Substring(0, 6000) + "'", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			try
			{
				if (par_sQuery == "")
				{
					return true;
				}

				//Execute the SQL statement here
				System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

				cmd.CommandText = par_sQuery;
				cmd.CommandType = System.Data.CommandType.Text;
				cmd.Connection = sqlConnection1;
				cmd.CommandTimeout = 86400; //24 hours

				//cmd.ExecuteNonQuery() returns no of rows affected or -1 if SS would return 
				//'The command completed successfully' (straight UPDATE, INSERT, DELETE).
				cmd.ExecuteNonQuery();
				sqlConnection1.Close();
				return true;

			}
			catch (System.Data.SqlClient.SqlException ex)
			{
				switch (ex.Number)
				{
					case 2705:
					case 4924:
					case 2714:
						//2705: Column names in each table must be unique. Column name 'xxx' in table 'yy' is specified more than once.
						//4924: ALTER TABLE DROP COLUMN failed because column 'xxx' does not exist in table 'yy'.
						//2714: There is already an object named 'xxx' in the database.
						goErr.SetWarning(45150, sProc, "", ex.Number.ToString() + " " + ex.Message + "\r\n" + "\r\n" + "Query:" + "\r\n" + par_sQuery.Substring(0, clC.SELL_METAVALUELIMIT - ex.Message.Length - 30));
						//45150: SQL Server returned the following error: [1]
						return true;
					case 512:
						//512: Subquery returned more than 1 value. This is not permitted when the subquery follows =, !=, <, <= , >, >= or when the subquery is used as an expression.
						goErr.SetError(45150, sProc, "", ex.Number.ToString() + " Subquery returned more than 1 value. This link was NOT processed. Make sure there are no duplicate values in TXT_ImportID field in the linked table, then reprocess this file (table)." + "\r\n" + "\r\n" + "Query:" + "\r\n" + par_sQuery.Substring(0, clC.SELL_METAVALUELIMIT - ex.Message.Length - 300));
						return false;
					default:
						goErr.SetError(45150, sProc, "", ex.Number.ToString() + " " + ex.Message + "\r\n" + "\r\n" + "Query:" + "\r\n" + par_sQuery.Substring(0, clC.SELL_METAVALUELIMIT - ex.Message.Length - 30));
						//45150: SQL Server returned the following error: [1]
						return false;
				}

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45100, sProc)
				//End If
			}

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}


		public string GetMapURL(string sAddress, string sCity, string sState, string sZip, string sCountryCode = "")
		{



			string sQ = "";

			//city=mandeville&state=LA&address=872+sweetbay+dr&zip=70448&country=us&zoom=5

			if (sAddress != "")
			{
				sAddress = goTR.Replace(sAddress, "%", "%%25");
				sAddress = goTR.Replace(sAddress, "&", "%%26");
				sAddress = goTR.Replace(sAddress, "+", "%%30");
				sAddress = goTR.Replace(sAddress, " ", "+");
				sQ = sAddress;
			}

			if (sCity != "")
			{
				sCity = goTR.Replace(sCity, "%", "%%25");
				sCity = goTR.Replace(sCity, "&", "%%26");
				sCity = goTR.Replace(sCity, "+", "%%30");
				sCity = goTR.Replace(sCity, " ", "+");
				sQ = sQ + ",+" + sCity;
			}

			if (sState != "")
			{
				sState = goTR.Replace(sState, "%", "%%25");
				sState = goTR.Replace(sState, "&", "%%26");
				sState = goTR.Replace(sState, "+", "%%30");
				sState = goTR.Replace(sState, " ", "+");
				sQ = sQ + ",+" + sState;
			}

			if (sZip != "")
			{
				sZip = goTR.Replace(sZip, "%", "%%25");
				sZip = goTR.Replace(sZip, "&", "%%26");
				sZip = goTR.Replace(sZip, "+", "%%30");
				sZip = goTR.Replace(sZip, " ", "+");
				sQ = sQ + ",+" + sZip;
			}

			if (sCountryCode != "")
			{
				sCountryCode = goTR.Replace(sCountryCode, "%", "%%25");
				sCountryCode = goTR.Replace(sCountryCode, "&", "%%26");
				sCountryCode = goTR.Replace(sCountryCode, "+", "%%30");
				sCountryCode = goTR.Replace(sCountryCode, " ", "+");
				sQ = sQ + ",+" + sCountryCode;
			}

			if (sQ.Substring(0, 1) == "&")
			{
				sQ = sQ.Substring(1);
			}

			return "http://www.google.com/maps?q=" + sQ;

		}


		public Microsoft.VisualBasic.Collection GetFilterSortFields(string par_sFilter, bool par_bAddSeqIDField = true, bool par_bReverseSort = false)
		{
			//MI 10/10/08 Added par_bReverseSort.
			//MI 5/13/08 Added removing outer brackets from filename.
			//MI 8/14/07 fixed duplicate key error when same field/direction specified multiple times
			//MI 9/21/06 Adding SYS_Name if sSort is blank.
			//MI 9/13/06 Added support for FILE.[FieldName] syntax
			//MI 9/11/06  Created.

			//AUTHOR: MI
			//PURPOSE:
			//       Return a collection of sort fields and their direction (asc or desc)
			//       from a SellSQL rowset filter statement or just the SORT= line.
			//       If SORT= is blank, SYS_Name becomes the sort. If par_bAddSeqIDField is
			//       true (default), the SS identity column field BI__ID is added to the sort
			//       fields by default to match GenerateSQL, which does the same.
			//       The purpose of this field is to make sorts distinctive to be able
			//       to generate a SQL statement which starts a recordset at a particular
			//       record regardless of the sort order. See the GenerateSQL documentation
			//       for more information.
			//       This method tolerates multiple commas (undefined sort fields), but
			//       not extra spaces. There must be only one space between the field and
			//       ASC or DESC.
			//PARAMETERS:
			//       par_sFilter: ini-format string containing a valid filter SORT= line
			//           or the same line in the SQL SELECT ORDER BY syntax.
			//           The line always must start with "SORT="!
			//           Example 1: SORT=CHK_ActiveField ASC, TXT_NameLast DESC, TXT_NameFirst d
			//           Example 2: SORT=CN.[CHK_ActiveField] ASC, CN.[TXT_NameLast] DESC, CN.[TXT_NameFirst] d
			//       par_bAddSeqIDField: Optional, true by default. When checked adds field
			//           BI__ID to the sort order unless this field is already in the sort
			//           definition.
			//       par_bReverseSort: True indicates that the "primary" direction of the sort is backwards,
			//           for example when the view is browsing 'pages' of data backward, i.e.
			//           the user clicked the |< or < buttons.
			//           This reverses the sort of BI__ID which is appended to ORDER BY in GenerateSQL 
			//           to make sorts on non-discriminating fields predictable for page browsing, 
			//           selecting the right record after postback, etc. (False is the default).
			//RETURNS:
			//       Collection object (1-based index!) in which each element contains:
			//           SortField & "|" & "ASC|DESC". Example:
			//           TXT_NameLast|ASC
			//EXAMPLE:
			//       Dim col As Collection
			//       Dim sSortString as string = goTr.StrRead(sFilter, "SORT")
			//       Dim i as integer
			//       Dim sField as string
			//       Dim sDirection as string
			//       'For testing, let's set the sSortString value
			//       sSortString = "SORT=CHK_ActiveField ASC, TXT_NameLast DESC, TXT_NameFirst d"
			//       col = goData.GetFilterSortFields(sSortString)
			//       For i = 1 to col.Count()
			//           sField = goTR.ExtractString(col.Item(i), 1, "|")
			//           sDirection = goTr.ExtractString(col.Item(i), 2, "|")
			//       Next
			string sProc = "clData::GetFilterSortFields";

			Microsoft.VisualBasic.Collection colResult = new Microsoft.VisualBasic.Collection();
			string sSort = goTR.StrRead(par_sFilter, "SORT").Trim(' ');
			int i = 1;
			string sField = null;
			string sTemp = null;
			string sDirection = null;
			int iSpace = 0;
			int iPos = 0;
			string sFile = "";

			// Try
			if (sSort == "")
			{
				sSort = "SYS_Name ASC";
			}

				do
				{
					sField = goTR.ExtractString(sSort, i, ",").Trim(' ');
                //if (sField[0] == clC.EOT)
                if (!string.IsNullOrEmpty(sField) && sField[0] == clC.EOT)
                {
                    // Your logic here
                }

                {
                    break;
					}
					if (sField == "")
					{
						goto SkipNext;
					}
					iSpace = sField.IndexOf(" ") + 1;
					if (iSpace < 1)
					{
						sTemp = sField;
						sDirection = "ASC";
					}
					else
					{
						iPos = sField.IndexOf(" ") + 1;
						if (iPos < 1)
						{
							goto SkipNext;
						}
						sTemp = goTR.FromTo(sField, 1, iPos - 1);
						sDirection = (goTR.FromTo(sField, iPos + 1, -1)).Trim(' ');
						if (sDirection == "")
						{
							sDirection = "ASC";
						}
					}
					//Extract the filename and fieldname
					if (sTemp.IndexOf(".") + 1 > 0)
					{
						sFile = goTR.ExtractString(sTemp, 1, ".");
						sTemp = goTR.ExtractString(sTemp, 2, ".");
					}
					sFile = goTR.RemoveOuterBrackets(sFile);
					sTemp = goTR.RemoveOuterBrackets(sTemp);
					switch (sDirection.ToUpper())
					{
						case "D":
						case "DESC":
							//Descending
							if (!colResult.Contains(sTemp + "|DESC"))
							{
								colResult.Add(sTemp + "|DESC", sTemp + "|DESC");
							}
							break;
						default:
							//Ascending
							if (!colResult.Contains(sTemp + "|ASC"))
							{
								colResult.Add(sTemp + "|ASC", sTemp + "|ASC");
							}
							break;
					}

	SkipNext:
					i = i + 1;
				} while (true);

				if (par_bAddSeqIDField)
				{
					if (colResult.Contains("BI__ID|ASC") == false && colResult.Contains("BI__ID|DESC") == false)
					{
						if (par_bReverseSort) //MI 10/10/08
						{
							colResult.Add("BI__ID|DESC"); //MI 10/10/08
						}
						else //MI 10/10/08
						{
							colResult.Add("BI__ID|ASC");
						} //MI 10/10/08
					}
				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return colResult;

		}


		public long GetCount(string par_sTable, string par_sFields, string par_sFilter, int par_iTimeout = 1800)
		{
			//Created by RH 10/5/07 based on clView.GetViewCount().
			//PURPOSE:
			//       Returns the no of records that a filter will return.
			//       Use this prior to creating large edit rowsets to find the no of records
			//       it will return.
			//RETURNS: Long integer.

			string sProc = "clData::GetCount";
			try
			{

				Stopwatch tTimer = new Stopwatch();
				tTimer.Start();

				//Since Me.Fields can contain parameters such as LENGTH=80 or ELLIPSIS=1, we are enclosing
				//each field with <% %>.
				int i = 1;
				string s = null;
				string sFields = "";
				do
				{
					s = goTR.ExtractString(par_sFields, i, ",").Trim(' ');
					if (s[0] == clC.EOT)
					{
						break;
					}
					if (s != "")
					{
						s = "<%" + s + "%>";
						//Build the fields string
						sFields += s + ", ";
					}
					i = i + 1;
				} while (true);
				//Remove the trailing comma, if any
				if (sFields.Substring(sFields.Length - 2) == ", ")
				{
					sFields = sFields.Substring(0, sFields.Length - 2);
				}

				string sSelect = GenerateSQL("", par_sTable, sFields, par_sFilter, "", -1, false, "", "COUNT");

				DataSet oDS = new DataSet();

				System.Data.SqlClient.SqlConnection connection = goData.GetConnection();

				using (connection)
				{
					string sQS = sSelect;
					System.Data.SqlClient.SqlDataAdapter oAdapt = new System.Data.SqlClient.SqlDataAdapter(sQS, connection);
					oAdapt.SelectCommand.CommandTimeout = par_iTimeout;
					if (oAdapt.Fill(oDS) == 1)
					{
						connection.Close();
						goP.SetVar("GetCountLoadTime", tTimer.ElapsedMilliseconds);
						return Convert.ToInt64(oDS.Tables[0].Rows[0][0]);
					}
					else
					{
						connection.Close();
						goP.SetVar("GetCountLoadTime", tTimer.ElapsedMilliseconds);
						return 0;
					}
				}
			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return 0;
			}

		}

		public string GetGoalValueTip(string par_sViewMetadata)
		{
			//MI 2/18/11 Not displaying '- Select any of the following under Y axis' if Goals are not tracked for the view's file.
			//MI 2/11/11 Created.
			//PURPOSE:
			//       Return a tip about what Goals are available and how to set up the view
			//       for those Goals to show as a Goal Line.
			//       Keep this code in sync with GetGoalValue().
			//PARAMETERS:
			//       sViewMetadata: A complete VIE_ metadata page. Only FILE= property is read currently.
			//RETURNS:
			//       Tip as string
			//NOTES:
			//       Goal records store values for things like Lead count, Opp Value, QL value,
			//       Activity loading count, Appt count. A Goal record can be linked to a User,
			//       Supplier (Vendor), Division, Product, or any combination of those. A chart view
			//       must be filtered in a way that's compatible with the way the Goal record(s) are
			//       filtered in order for it to be able to use the Goal record's value.
			//       The structure of the Goals file and its semantics are defined in OTH_GOALS MD.
			//       Properties that are pertinent in this context are (note that
			//       defining fields and (COUNT) requires defining the same file as two GOALFILExx= lines--see OH example):
			//        GOALFILE01=LE,Leads
			//        GOALFILE02=OP,Opps
			//        GOALFILE03=QL,Quote Lines
			//        GOALFILE04=OH,Orders
			//        GOALFILE05=AC,Activity
			//        GOALFILE06=AP,Appts
			//        GOALFILE07=OP,Opps
			//        GOALFILE08=QT,Quotes
			//        GOALFILE09=OH,Orders
			//        GOALFILECOUNT = 9
			//        GOALFILEVALUE01 = LI__GOALLE
			//        GOALFILEVALUE02 = CUR_GOALOP
			//        GOALFILEVALUE03 = CUR_GOALQL
			//        GOALFILEVALUE04 = CUR_GOALOH
			//        GOALFILEVALUE04=CUR_GROSSPROFIT|CUR_GOALOH,CUR_BOOKING|CUR_GOALOH2
			//        GOALFILEVALUE04_OLD_SYNTAX = CUR_GOALOH
			//        GOALFILEVALUE05 = LI__GOALAC
			//        GOALFILEVALUE06 = LI__GOALAP
			//        GOALFILEVALUE07 = LI__GOALOP
			//        GOALFILEVALUE08 = LI__GOALQT
			//        GOALFILEVALUE09 = LI__GOALOH
			//        GOALFILEVALUEACTUAL_NOTE = GOALFILEVALUEACTUALxx lines are not needed if GOALFILEVALUExx lines have | symbol.
			//        GOALFILEVALUEACTUAL01 = (COUNT)
			//        GOALFILEVALUEACTUAL02 = CUR_VALUE
			//        GOALFILEVALUEACTUAL03 = CUR_SUBTOTAL
			//        GOALFILEVALUEACTUAL04 = CUR_EXTPRICE
			//        GOALFILEVALUEACTUAL05 = (COUNT)
			//        GOALFILEVALUEACTUAL06 = (COUNT)
			//        GOALFILEVALUEACTUAL07 = (COUNT)
			//        GOALFILEVALUEACTUAL08 = (COUNT)
			//        GOALFILEVALUEACTUAL09 = (COUNT)
			//        GOALLINK0101=LE,LO,LNK_RELATED_LO
			//        GOALLINK0102=LE,US,LNK_CREDITEDTO_US
			//        GOALLINK0103=LE,DV,LNK_RELATED_DV
			//        GOALLINK0104=LE,VE,LNK_RELATED_VE
			//        GOALLINK0105=LE,PD,LNK_RELATED_PD
			//        GOALLINK0106=LE,CO,LNK_RELATED_CO
			//        GOALLINK0107=LE,TE,LNK_RELATED_TE
			//        GOALLINK0108=LE,GR,LNK_RELATED_GR
			//        GOALLINK0201=OP,LO,LNK_RELATED_LO
			//        GOALLINK0202=OP,US,LNK_CREDITEDTO_US
			//        GOALLINK0203=OP,DV,LNK_RELATED_DV
			//        GOALLINK0204=OP,VE,LNK_RELATED_VE
			//        GOALLINK0205=OP,PD,LNK_FOR_PD
			//        GOALLINK0206=OP,CO,LNK_FOR_CO
			//        GOALLINK0207=OP,TE,LNK_RELATED_TE
			//        GOALLINK0208=OP,GR,LNK_RELATED_GR
			//        GOALLINK0301=QL,LO,LNK_TAKENAT_LO
			//        GOALLINK0302=QL,US,LNK_CREDITEDTO_US
			//        GOALLINK0303=QL,DV,LNK_RELATED_DV
			//        GOALLINK0304=QL,VE,LNK_RELATED_VE
			//        GOALLINK0305=QL,PD,LNK_FOR_PD
			//        GOALLINK0306=QL,CO,LNK_TO_CO
			//        GOALLINK0307=QL,TE,LNK_RELATED_TE
			//        GOALLINK0308=QL,GR,LNK_RELATED_GR
			//        GOALLINK0401=OH,LO,TXT_BranchID,1,P21
			//        GOALLINK0402=OH,US,TXT_CreditedToUS,1,P21
			//        GOALLINK0403=OH,DV,TXT_DivisionID,1,P21
			//        GOALLINK0404=OH,VE,TXT_VendorID,1,P21
			//        GOALLINK0405=OH,PD,TXT_ProductID,1,P21
			//        GOALLINK0406=OH,CO,TXT_CompanyID,1,P21
			//        GOALLINK0407=OH,TE,LNK_RELATED_TE
			//        GOALLINK0408=OH,GR,LNK_RELATED_GR
			//        GOALLINK0501=AC,LO,LNK_RELATED_LO
			//        GOALLINK0502=AC,US,LNK_CREDITEDTO_US
			//        GOALLINK0503=AC,DV,LNK_RELATED_DV
			//        GOALLINK0504=AC,VE,LNK_RELATED_VE
			//        GOALLINK0505=AC,PD,LNK_RELATED_PD
			//        GOALLINK0506=AC,CO,LNK_RELATED_CO
			//        GOALLINK0507=AC,TE,LNK_RELATED_TE
			//        GOALLINK0508=AC,GR,LNK_RELATED_GR
			//        GOALLINK0601=AP,LO,LNK_RELATED_LO
			//        GOALLINK0602=AP,US,LNK_INVOLVES_US
			//        GOALLINK0603=AP,DV,LNK_RELATED_DV
			//        GOALLINK0604=AP,VE,LNK_RELATED_VE
			//        GOALLINK0605=AP,PD,LNK_FOR_PD
			//        GOALLINK0606=AP,CO,LNK_RELATED_CO
			//        GOALLINK0607=AP,TE,LNK_RELATED_TE
			//        GOALLINK0608=AP,GR,LNK_RELATED_GR
			//        GOALLINK0701=OP,LO,LNK_RELATED_LO
			//        GOALLINK0702=OP,US,LNK_CREDITEDTO_US
			//        GOALLINK0703=OP,DV,LNK_RELATED_DV
			//        GOALLINK0704=OP,VE,LNK_RELATED_VE
			//        GOALLINK0705=OP,PD,LNK_FOR_PD
			//        GOALLINK0706=OP,CO,LNK_FOR_CO
			//        GOALLINK0707=OP,TE,LNK_RELATED_TE
			//        GOALLINK0708=OP,GR,LNK_RELATED_GR
			//        GOALLINK0801=QT,LO,LNK_TAKENAT_LO
			//        GOALLINK0802=QT,US,LNK_CREDITEDTO_US
			//        GOALLINK0803=QT,DV,LNK_NOTSUPPORTED_DV
			//        GOALLINK0804=QT,VE,LNK_NOTSUPPORTED_VE
			//        GOALLINK0805=QT,PD,LNK_NOTSUPPORTED_PD
			//        GOALLINK0806=QT,CO,LNK_TO_CO
			//        GOALLINK0807=QT,TE,LNK_NOTSUPPORTED_TE
			//        GOALLINK0808=QT,GR,LNK_RELATED_GR
			//        GOALLINK0901=OH,LO,TXT_BranchID,1,P21
			//        GOALLINK0902=OH,US,TXT_CreditedToUS,1,P21
			//        GOALLINK0903=OH,DV,TXT_DivisionID,1,P21
			//        GOALLINK0904=OH,VE,TXT_VendorID,1,P21
			//        GOALLINK0905=OH,PD,TXT_ProductID,1,P21
			//        GOALLINK0906=OH,CO,TXT_CompanyID,1,P21
			//        GOALLINK0907=OH,TE,LNK_RELATED_TE
			//        GOALLINK0908=OH,GR,LNK_RELATED_GR
			//        GOALLINKCOUNT=8
			//        GOALLINKHELP=Par1=File,Par2=LinkedFile,Par3=LinkName,Par4=LinkProcessingMode(1=TN)
			//The following links that are supported on the Goal form do not exist in the Master database
			//and would therefore be ignored. IOW, Goals that use these links would never be evaluated.
			//       Activity - Division
			//       Appointment - Division
			//       Appointment - Territory
			//       Lead (Activity) - Division
			//       Order - Division
			//       Order - Group
			//       Order - Territory
			//       Quote - Division
			//       Quote - Group
			//       Quote - Product
			//       Quote - Territory
			//       Quote - Vendor
			//       Quote Line - Group

			string sProc = ""; //System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sGoalsMD = null;
			string sViewFile = null;
			string sGoalFile = ""; //EL, OP, QL, AC, AP
			string[] sGoalFiles = null;
			int iGoalFileCount = 0;
			int i = 0;
			int iFileMatches = 0;
			string sTemp = null;
			string sValueField = null;
			string sFileFieldName = null;
			string sGoalLink = null;
			int iGoalLinkCount = 0;
			int iResult = 0;

			string sResult = "";
			string[] aValueField = null;
			string sFieldPair = null;
			string sErrorText = null;
			string sValueString = "";
			string sLinkName = null;
			int iValid = 0;
			int iFirstMatchingFileNumber = 0; //Since the same file can be defined under multiple file numbers, and each number has links defined,
			//                                           'this var records the number of the first file match. That's the number for which the links will be read.

			// Try

			sResult = "<b>To display a Goal line in this view based on Goal records:</b><br />";
				Array.Resize(ref sGoalFiles, 1);
				sGoalFiles[0] = "";
				sViewFile = goTR.StrRead(par_sViewMetadata, "FILE", null, false).ToUpper();
				sGoalsMD = goMeta.PageRead("GLOBAL", "OTH_GOALS", "", true);
				iGoalFileCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sGoalsMD, "GOALFILECOUNT", "0", false), "0"));

				//----------- Summary -------------
				//X axis/Sort must be a year, year/quarter, year/month or year/month/day field. 
				//Y axis: Record count is supported.
				//Y axis: Total is supported. Select <xyz> or <xyz> or ... field in Field 1 combo. 
				//Filter:
				//   Only ANDs are supported.
				//   Date/time fields are supported.
				//   <AC, AP cases>Must include:
				//       <Case AC, LE: blurb about Lead Purpose=8 or for non Lead Purpose<>8 and Type must be xyz.> 
				//       <Case AP: blurb about  Type=xyz> 
				//   Can include:
				//       List of optional links
				//Note: A Goal line will display only if a Goal record exists for the exact combination of view's conditions (other than date/time fields). For ex, if you filter the view by User, a Goal record for that User and no other links (for ex. Product) must exist. 

				iFileMatches = 0;

				//Populate a string array with names of files
				for (i = 1; i <= iGoalFileCount; i++)
				{
					sTemp = (goTR.StrRead(sGoalsMD, "GOALFILE" + goTR.Pad(i.ToString(), 2, "0", "L"), null, false)).ToUpper();
					sGoalFile = goTR.ExtractString(sTemp, 1, ",");
					Array.Resize(ref sGoalFiles, i);
					sGoalFiles[i - 1] = sGoalFile;
				}

				//-------------- X axis ---------------
				sResult += "- X axis:<br />";
				sResult += "&nbsp; &nbsp; - Select any year, year/quarter, year/month or year/month/day</b> field<br />";
				sResult += "&nbsp; &nbsp; - Under Goal line select the 'As defined in View Properties' radio button<br />";

				//-------------- Y axis ---------------
				//Entry point for GOTO from errors below
				iFileMatches = 0;
				iFirstMatchingFileNumber = 0;
				sValueString = "";
	ProcessNextFilePosition:
				for (i = 1; i <= sGoalFiles.GetUpperBound(0) + 1; i++) //iGoalFileCount
				{
					sGoalFile = sGoalFiles[i - 1];
					if (sGoalFile == sViewFile || (sGoalFile == "LE" && sViewFile == "AC"))
					{
						//We found the file
						//Record the first matching file number. In this example above, OH has definitions 
						//as 04 and 09. This will record 04 for link evaluation further down.
						if (iFirstMatchingFileNumber == 0)
						{
							iFirstMatchingFileNumber = i;
						}
						iFileMatches = iFileMatches + 1;

						//----------- This is based on Paul's new code ----------------
						//   Format of GOALFILEVALUE is now 'GOALFILEVALUE04=CUR_GROSSPROFIT|CUR_GOALOH,CUR_BOOKING|CUR_GOALOH2'
						//   or just GOALFILEVALUE04=CUR_GROSSPROFIT (in which case GOALFILEVALUEACTUAL= is required).
						//ORIGINAL SYNTAX (still supported):
						//   GOALFILEVALUE02 = CUR_GOALOP        'GL value field name. Can be a field name or '(COUNT)'.
						//   GOALFILEVALUEACTUAL02 = CUR_VALUE   'Corresponding view file's field (only needed if field in GOALFILEVALUExx.
						//Code:
						//   sValueField = UCase(goTR.StrRead(sGoalsMD, "GOALFILEVALUE" & goTR.Pad(i.ToString, 2, "0", "L"), , False))
						//   sFileFieldName = UCase(goTR.StrRead(sGoalsMD, "GOALFILEVALUEACTUAL" & goTR.Pad(i.ToString, 2, "0", "L"), , False))
						//NEW SYNTAX:
						//   GOALFILEVALUE04=CUR_GROSSPROFIT|CUR_GOALOH,CUR_BOOKING|CUR_GOALOH2
						sValueField = (goTR.StrRead(sGoalsMD, "GOALFILEVALUE" + goTR.Pad(i.ToString(), 2, "0", "L"), null, false)).ToUpper();
						if (sValueField.Contains(","))
						{
							//There is more than one value field for this file. Split value field pairs into string array
							aValueField = sValueField.Split(',');
							//Loop thru field pairs
							for (int iGoalFieldCount = 0; iGoalFieldCount <= aValueField.GetUpperBound(0); iGoalFieldCount++)
							{
								sFieldPair = aValueField[iGoalFieldCount];
								if (sFieldPair.Contains("|"))
								{
									//Field pair contains pipe delimiter, so parse file field and goal field
									sFileFieldName = sFieldPair.Split('|')[0];
									sValueField = sFieldPair.Split('|')[1];
									//Only Value is supported in the new syntax
									sTemp = "&nbsp; &nbsp; - Total with '" + goData.GetFieldFullLabelFromName(sViewFile, sFileFieldName, ref iValid) + "' in Field 1 combo<br /> ";
									if (iValid == clC.SELL_TYPE_VALID)
									{
										if (sValueString.IndexOf(sTemp) + 1 < 1)
										{
											sValueString += sTemp;
										}
									}
									else
									{
										sValueString += "&nbsp; &nbsp; - ***Invalid field found in GOALS definition: '" + sFileFieldName + "'. Please report this to your Selltis administrator.***<br /> ";
									}
								}
								else
								{
									//sFieldPair does not contain "|", invalid definition, raise a warning and skip it
									sErrorText = "&nbsp; &nbsp; - ***Unsupported GOALFILEVALUE value found in GOALS definition: '" + sFieldPair + "'. A view file field and Goal field delimited with | must appear between the commas.***";
									sValueString += sErrorText + " Please report this to your Selltis administrator.";
									//goErr.SetWarning(35000, sProc, sErrorText)     'We are setting this warning in GetGoalValue.
								}
	NextFieldPair: ;
							}
						}
						else
						{
							//only one value field for this file. Does it contain pipe delimiter?
							if (sValueField.Contains("|"))
							{
								//Field pair contains pipe delimiter, so parse file field and goal field
								sFileFieldName = sValueField.Split('|')[0];
								sValueField = sValueField.Split('|')[1];
								switch (sFileFieldName)
								{
									case "(COUNT)":
										sTemp = "&nbsp; &nbsp; - Record count<br /> ";
										if (sValueString.IndexOf(sTemp) + 1 < 1)
										{
											sValueString += sTemp;
										}
										break;
									case "STATISTICS":
									case "(STATISTICS)":
									break;
										//Not supported, skip
									default:
										//Value
										sTemp = "&nbsp; &nbsp; - Total with '" + goData.GetFieldFullLabelFromName(sViewFile, sFileFieldName, ref iValid) + "' in Field 1 combo<br /> ";
										if (iValid == clC.SELL_TYPE_VALID)
										{
											if (sValueString.IndexOf(sTemp) + 1 < 1)
											{
												sValueString += sTemp;
											}
										}
										else
										{
											sValueString += "&nbsp; &nbsp; - ***Invalid field found in GOALS definition: '" + sFileFieldName + "'. Please report this to your Selltis administrator.***<br /> ";
										}
										break;
								}
							}
							else
							{
								//Old MD schema - GOALFILEVALUEACTUAL used in this case. GOALFILEVALUEACTUAL02 = CUR_VALUE
								sFileFieldName = (goTR.StrRead(sGoalsMD, "GOALFILEVALUEACTUAL" + goTR.Pad(i.ToString(), 2, "0", "L"), null, false)).ToUpper();
								switch (sFileFieldName)
								{
									case "(COUNT)":
										sTemp = "&nbsp; &nbsp; - Record count<br /> ";
										if (sValueString.IndexOf(sTemp) + 1 < 1)
										{
											sValueString += sTemp;
										}
										break;
									case "STATISTICS":
									case "(STATISTICS)":
									break;
										//Not supported, skip
									default:
										//Value
										sTemp = "&nbsp; &nbsp; - Total with '" + goData.GetFieldFullLabelFromName(sViewFile, sFileFieldName, ref iValid) + "' in Field 1 combo<br /> ";
										if (iValid == clC.SELL_TYPE_VALID)
										{
											if (sValueString.IndexOf(sTemp) + 1 < 1)
											{
												sValueString += sTemp;
											}
										}
										else
										{
											sValueString += "&nbsp; &nbsp; - ***Invalid field found in GOALS definition: '" + sFileFieldName + "'. Please report this to your Selltis administrator.***<br /> ";
										}
										break;
								}
							}
						}
						//----------- End Paul's new code -------------------
					}
					else
					{
						//Not the view's file, skip
					}
				}
				if (iFileMatches < 1)
				{
					//File not found
					sResult = "Goals are not tracked for this view's file, '" + goData.GetFileLabelFromName(sViewFile) + "'. To add Goal tracking contact Selltis Professional Services.<br /> ";
					return sResult;
				}
				else
				{
					sResult += "- Y axis (select any of the following):<br />";
					sResult += sValueString;
				}

				//--------------------------
				sResult += "- Filter: conditions on the following fields/links are supported (with ANDs between them):<br /> ";
				switch (sViewFile)
				{
					case "AC":
						sResult += "&nbsp; &nbsp; - For Lead Activity: Purpose Is Lead (required)<br /> ";
						sResult += "&nbsp; &nbsp; - For non-Lead Activity: Purpose Is Not Lead; Type (required)<br /> ";
						break;
					case "AP":
						sResult += "&nbsp; &nbsp; - Type (required)<br /> ";
						break;
					default:
					break;
				}
				sResult += "&nbsp; &nbsp; - Any date/time field (optional)<br /> ";
				sGoalsMD = goMeta.PageRead("GLOBAL", "OTH_GOALS", "", true);
				//Identify Goal links. Ex:
				//           GOALLINK0201=OP,LO,LNK_RELATED_LO
				//           GOALLINK0202=OP,US,LNK_CREDITEDTO_US
				//           GOALLINK0203=OP,DV,LNK_RELATED_DV
				//           GOALLINK0204=OP,VE,LNK_RELATED_VE
				//           GOALLINK0205=OP,PD,LNK_FOR_PD
				//           GOALLINK0206=OP,CO,LNK_FOR_CO
				iGoalLinkCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sGoalsMD, "GOALLINKCOUNT", "0", false), "0"));
				//Load aGoalLinks wiht values like 'LNK_RELATED_VE'
				for (i = 1; i <= iGoalLinkCount; i++)
				{
					sGoalLink = goTR.StrRead(sGoalsMD, "GOALLINK" + goTR.Pad(iFirstMatchingFileNumber.ToString(), 2, "0", "L") + goTR.Pad(i.ToString(), 2, "0", "L"), null, false);
					if (sGoalLink != "")
					{
						sLinkName = goTR.ExtractString(sGoalLink, 3, ",");
						if (goData.IsFieldValid(sViewFile, sLinkName))
						{
							sTemp = goData.GetFieldFullLabelFromName(sViewFile, sLinkName);
							if (sTemp == "")
							{
								sTemp = sLinkName;
							}
							sResult += "&nbsp; &nbsp; - " + sTemp + " (optional)<br />";
						}
					}
				}
				// sResult &= "<br />Important: A Goal line will display only if a Goal record exists for the exact combination of view's conditions (other than date/time fields). For ex, if you filter the view by User, a Goal record must have that User linked and all other links (Product, Territory, etc.) in the Goal record must remain blank.<br /><br /> "
				sResult += "<br />Important: A Goal line will display only if a Goal record matches the view's filters. For ex, if you filter the view by User, a Goal record must have that User linked and all other links (Product, Territory, etc.) in the Goal record must remain blank. Filters in the view that are not supported in the Goal record will not be considered in displaying the Goal. For ex, in an Opp view filtered to show only primary Opps, the Goal for Opps will display since a filter for primary Opps is not supported on the Goal record. <br /><br /> ";

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return sResult;

		}

		private int GetGoalValueYAxisMatch(string par_sGoalFieldName, string par_sGraphShow, string par_sViewFile, ref string par_sMessage, ref bool par_bNextFileDef, string par_sViewMetadata)
		{
			//MI 2/18/11 Returning a generic message if no match. 
			//MI 2/9/11 Mods for better 'Is Goal Line Possible' reporting.
			//MI 1/31/11 Created.
			//PURPOSE:
			//       Checks whether the Y axis type (Count or Total) matches the Goal type.
			//       This is run by GetGoalValue().
			//PARAMETERS:
			//       par_sGoalFieldName: "(COUNT)" or numeric value. sFileFieldName in GetGoalValue().
			//       par_sGraphShow: "COUNT", "TOTAL", or "STATISTICS" (STATISTICS is currently unsupported and is treated as TOTAL).
			//       par_sViewFile: View's file name (e.g. 'OP' or 'QT').
			//       par_sMessage: ByRef, returns the message to display to the user.
			//       par_bNextFileDef: ByRef, sets the bNextFileDef variable in GetGoalValue().
			//       par_sViewMetadata: VIE_ metadata.
			//RETURNS:
			//       Result value as integer: -2 is no match, 0 otherwise.

			string sProc = ""; //System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			int iResult = 0;
			string sViewField = null;
			par_bNextFileDef = false;
			par_sMessage = "";
			iResult = 0;
			try
			{
				//Does the view Y axis type (Count vs. Total) match the Goal type at all? If not, go to the next file
				switch (par_sGoalFieldName)
				{
					case "(COUNT)":
						//Count
						switch (par_sGraphShow)
						{
							case "COUNT":
							break;
								//Match
							case "STATISTICS":
								par_sMessage = "Goal records do not support " + goData.GetFileLabelFromName(par_sViewFile) + " statistics. Select Record count or Total under Y axis and try again.";
								iResult = -2;
								par_bNextFileDef = true;
								break;
							default:
								//TOTAL
								par_sMessage = "Goal records do not support " + goData.GetFileLabelFromName(par_sViewFile) + " totals. Select Record count under Y axis and try again.";
								iResult = -2;
								par_bNextFileDef = true;
								break;
						}
						break;
					case "STATISTICS":
						//Statistics are not supported
						par_sMessage = "Goal records do not support " + goData.GetFileLabelFromName(par_sViewFile) + " statistics. Select Record count or Total under Y axis and try again.";
						iResult = -2;
						par_bNextFileDef = true;
						break;
					default:
						//Total.
						switch (par_sGraphShow)
						{
							case "COUNT":
								par_sMessage = "Goal records do not support " + goData.GetFileLabelFromName(par_sViewFile) + " record count. Select Total under Y axis and try again.";
								iResult = -2;
								par_bNextFileDef = true;
								break;
							case "STATISTICS":
								par_sMessage = "Goal records do not support " + goData.GetFileLabelFromName(par_sViewFile) + " statistics. Select Record count or Total under Y axis and try again.";
								iResult = -2;
								par_bNextFileDef = true;
								break;
							default:
								//TOTAL
								//Does the file field match the Y axis field 1?
								sViewField = goTR.StrRead(par_sViewMetadata, "GRAPHYFIELD1", null, false).ToUpper();
								if (sViewField == par_sGoalFieldName.ToUpper())
								{
									//Match
								}
								else
								{
									par_sMessage = "Goal records do not support totals on the " + goData.GetFileLabelFromName(par_sViewFile) + " '" + goData.GetFieldFullLabelFromName(par_sViewFile, sViewField) + "' field. Select a supported field such as '" + goData.GetFieldFullLabelFromName(par_sViewFile, par_sGoalFieldName) + "' in the 'Field 1' combo box under Y axis or select Record count and try again.";
									iResult = -2;
									par_bNextFileDef = true;
								}
								break;
						}
						break;
				}

				//MI 2/18/11 Override the message set above because the message may be misleading. The message displays only the reasons for the 
				//last failed attempt at a match, which is misleading because the user may have other options. For ex, the message may say
				//that the view doesn't support Record count, and the Y axis is set to Total on an unsupported Field. To address this,
				//we are sending the use to the 'To display a Goal line in this view' text.
				if (iResult == -2)
				{
					par_sMessage = "Goal records do not support your Y axis selection.";
				}

			}
			catch (Exception ex)
			{
				//If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//    goErr.SetError(ex, 45105, sProc)
				//End If
				return 0;
			}

			return iResult;

		}

		public decimal GetGoalValue(string par_sViewMetadata, string par_sCurrentFilter, string par_sSortField, string par_sMode)
		{
			string tempVar = "";
			return GetGoalValue(par_sViewMetadata, par_sCurrentFilter, par_sSortField, par_sMode, ref tempVar);
		}

		public decimal GetGoalValue(string par_sViewMetadata, string par_sCurrentFilter, string par_sSortField)
		{
			string tempVar = "";
			return GetGoalValue(par_sViewMetadata, par_sCurrentFilter, par_sSortField, "VIEW", ref tempVar);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Function GetGoalValue(ByVal par_sViewMetadata As String, ByVal par_sCurrentFilter As String, ByVal par_sSortField As String, Optional ByVal par_sMode As String = "VIEW", Optional ByRef par_sMessage As String = "") As Decimal
		public decimal GetGoalValue(string par_sViewMetadata, string par_sCurrentFilter, string par_sSortField, string par_sMode, ref string par_sMessage)
		{
			//MI 2/21/11 Moved validating X axis ahead of Y axis.
			//MI 2/18/11 Genericized all misleading messages about filter conditions.
			//MI 2/2/11 Finished mods to support PJ's new multiple fields per file syntax.
			//PJ 1/24/11 Edited to support the new syntax, e.g. 'GOALFILEVALUE04=CUR_GROSSPROFIT|CUR_GOALOH,CUR_BOOKING|CUR_GOALOH2'
			//MI 5/4/10 Renamed sFile to sViewFile.
			//MI 4/30/10 Added support for multiple definitions for the same file.
			//MI 4/28/10
			//MI 4/16/10 Created.
			//PURPOSE:
			//       Return the appropriate value from a Goal (GL file) record that matches
			//       the chart view's definition (Y axis definition, filter, sort). This is
			//       called from chart view code (clView).
			//       *** The original version of this method is GetGoalValueDeprecated(). ***
			//PARAMETERS:
			//       sViewMetadata: A complete VIE_ metadata page.
			//       sCurrentFilter: The filter string that includes data range selector condition(s),
			//           conditions that reflect clicked through data points in a multi-level
			//           chart view. This string replaced the filter string sent via sViewMetadata.
			//       par_sSortField: field by which the X axis is displayed for the current chart/subchart
			//           level. If the user drills through data points 3 levels deep, and the sort is:
			//           DTY_Time DESC, DTQ_Time DESC, DTM_Time ASC, then this parameter would contain
			//           'DTM_Time'.
			//       par_sMode: VIEW (default) or TEST. VIEW returns the Goal value for a real view with
			//           all keywords like <%SelectedRecordID%> fully evaluated.
			//           TEST evaluates keywords with fake values that would cause no data to be returned,
			//           but this is good since we don't need the data but want to test whether the view 
			//           is compatible with OTH_GOALS definitions and has a chance of getting a Goal value. 
			//       par_sMessage: return parameter (byref) that returns a message with the reason why
			//           a Goal cannot be returned.
			//RETURNS:
			//       Goal value as decimal or:
			//           -1      Goals are not tracked for the view's file (no GOALFILExx= in OTH_GOALS).
			//           -2      Goal records do not support the view's Y axis definition. For ex, 
			//                   Y axis has COUNT and Goals only support Total on CUR_Value for the OP file.
			//           -3      The view's current chart level has an X-axis (sort) field other than
			//                   an aggregate date field (DTY, DTQ, DTM, DTD). 
			//           -4      There is an OR in the view's filter condition. ORs are not supported
			//                   because there is an implicit AND between the Goal record's links.
			//           -5      At least one view condition is not supported in Goal records. 
			//                   the view's conditions on DTT fields are ignored. 
			//                   There can be only a single condition on each of the 
			//                   links defined on GOALLINKnnii lines in OTH_GOALS. Any other conditions
			//                   are not supported unless required for hard-coded additional expectations in
			//                   Goal records (see -6 below).
			//           -6      The view doesn't meet the hard-coded expectations in Goal records:
			//                   - In the Case of LE count, the view filter must contain MLS_Purpose=8 (Lead)
			//                       and Y axis must be on Record count.
			//                   - In the Case of AC count, the view filter must have: 
			//                       MLS_Purpose<>8 (Lead)
			//                       and MLS_Type is defined (will be compared to <GLRecord'sMLS_ACTIVITYTYPEValue>)
			//                       and Y axis must be on Record count.
			//                   - In the Case of AP count, the view filter must have
			//                       MLS_Type is defined (will be compared to <GLRecord'sMLS_APPTTYPEValue>)
			//                       and Y axis must be on Record count.
			//           -7      VIEW mode only: Goal record wasn't found or has value defined as 0, which is interpreted
			//                   as 'do not report goal'. This case is reported as 0 in TEST mode.
			//           -8      TEST mode only: Invalid keyword in the view's CONDITION= (or par_sCurrentFilter). 
			//                   For example, FILE= may not be defined in <%SelectedRecordID FILE=xx%> keyword.
			//           -9      TEST mode only: A condition references <%SelectedRecordXxx FILE=yy%> but file yy is empty.
			//                   'Selected record' cannot be evaluated.
			//           -10     View condition is <%NODATA%> and Goal value is not relevant.
			//           -11     Goal link definitions are missing or invalid. At least one of the linked files
			//                   is not defined correctly.
			//NOTES:
			//       Goal records store values for things like Lead count, Opp Value, QL value,
			//       Activity loading count, Appt count. A Goal record can be linked to a User,
			//       Supplier (Vendor), Division, Product, or any combination of those. A chart view
			//       must be filtered in a way that's compatible with the way the Goal record(s) are
			//       filtered in order for it to be able to use the Goal record's value.
			//       The structure of the Goals file and its semantics are defined in OTH_GOALS MD.
			//       Properties that are pertinent in this context are:
			//           'Goal value Files (Goals can be defined for these files)
			//           GOALFILE01=LE,Leads
			//           GOALFILE02=OP,Opps
			//           GOALFILE03=QL,Quotes
			//           GOALFILE04=OH,Orders
			//           GOALFILE05=AC,Activity
			//           GOALFILE06=AP,Appts
			//           GOALFILECOUNT = 6
			//           'Value field names on the GL form
			//           'The original syntax is field only, requires GOALFILEVALUEACTUAL04=
			//           'New syntax includes the file field name|Goal field name and does not require GOALFILEVALUEACTUAL04=
			//           GOALFILEVALUE01 = LI__GOALLE
			//           GOALFILEVALUE02 = CUR_GOALOP
			//           GOALFILEVALUE03 = CUR_GOALQL
			//           GOALFILEVALUE04 = CUR_GOALOH        'Original syntax
			//           GOALFILEVALUE04 = CUR_GROSSPROFIT|CUR_GOALOH,CUR_BOOKING|CUR_GOALOH2    'New syntax
			//           GOALFILEVALUE05 = LI__GOALAC
			//           GOALFILEVALUE06 = LI__GOALAP
			//           'Value file field names (they are not needed if new syntax is used in GOALFILEVALUExx)
			//           GOALFILEVALUEACTUAL01 = (COUNT)
			//           GOALFILEVALUEACTUAL02 = CUR_VALUE
			//           GOALFILEVALUEACTUAL03 = CUR_SUBTOTAL
			//           GOALFILEVALUEACTUAL04 = CUR_EXTPRICE
			//           GOALFILEVALUEACTUAL05 = (COUNT)
			//           GOALFILEVALUEACTUAL06 = (COUNT)
			//           'Transaction file, Filter file, link
			//           'GOALLINKnnii where nn is GOALFILEnn and ii is 'link filter' number
			//           GOALLINK0101=LE,LO,LNK_RELATED_LO
			//           GOALLINK0102=LE,US,LNK_CREDITEDTO_US
			//           GOALLINK0103=LE,DV,LNK_RELATED_DV
			//           GOALLINK0104=LE,VE,LNK_RELATED_VE
			//           GOALLINK0105=LE,PD,LNK_RELATED_PD
			//           GOALLINK0106=LE,CO,LNK_RELATED_CO
			//           GOALLINK0201=OP,LO,LNK_RELATED_LO
			//           GOALLINK0202=OP,US,LNK_CREDITEDTO_US
			//           GOALLINK0203=OP,DV,LNK_RELATED_DV
			//           GOALLINK0204=OP,VE,LNK_RELATED_VE
			//           GOALLINK0205=OP,PD,LNK_FOR_PD
			//           GOALLINK0206=OP,CO,LNK_FOR_CO
			//           GOALLINK0301=QL,LO,LNK_TAKENAT_LO
			//           GOALLINK0302=QL,US,LNK_CREDITEDTO_US
			//           GOALLINK0303=QL,DV,LNK_RELATED_DV
			//           GOALLINK0304=QL,VE,LNK_RELATED_VE
			//           GOALLINK0305=QL,PD,LNK_FOR_PD
			//           GOALLINK0306=QL,CO,LNK_TO_CO
			//           GOALLINK0401=OH,LO,TXT_BranchID,1,P21
			//           GOALLINK0402=OH,US,TXT_CreditedToUS,1,P21
			//           GOALLINK0403=OH,DV,TXT_DivisionID,1,P21
			//           GOALLINK0404=OH,VE,TXT_VendorID,1,P21
			//           GOALLINK0405=OH,PD,TXT_ProductID,1,P21
			//           GOALLINK0406=OH,CO,TXT_CompanyID,1,P21
			//           GOALLINK0501=AC,LO,LNK_RELATED_LO
			//           GOALLINK0502=AC,US,LNK_CREDITEDTO_US
			//           GOALLINK0503=AC,DV,LNK_RELATED_DV
			//           GOALLINK0504=AC,VE,LNK_RELATED_VE
			//           GOALLINK0505=AC,PD,LNK_RELATED_PD
			//           GOALLINK0506=AC,CO,LNK_RELATED_CO
			//           GOALLINK0601=AP,LO,LNK_RELATED_LO
			//           GOALLINK0602=AP,US,LNK_INVOLVES_US
			//           GOALLINK0603=AP,DV,LNK_RELATED_DV
			//           GOALLINK0604=AP,VE,LNK_RELATED_VE
			//           GOALLINK0605=AP,PD,LNK_RELATED_PD
			//           GOALLINK0606=AP,CO,LNK_RELATED_CO
			//           GOALLINKCOUNT=6     'determines ii in GOALLINKnnii lines
			//
			//Process:
			//        If I have a view on OP file, I will look in GOALFILE lines and find OP in GOALFILE02. 
			//        Then I will determine the value field on GOALFILEVALUEACTUAL02 line, which is CUR_Value. 
			//        If the view filter is <%SelectedRecordID FILE=VE%> (and possibly some date range constraint), I will try to find a match in one of these:

			//           GOALLINK0201=OP,LO,LNK_RELATED_LO
			//           GOALLINK0202=OP,US,LNK_CREDITEDTO_US
			//           GOALLINK0203=OP,DV,LNK_RELATED_DV
			//           GOALLINK0204=OP,VE,LNK_RELATED_VE
			//           GOALLINK0205=OP,PD,LNK_FOR_PD
			//           GOALLINK0206=OP,CO,LNK_FOR_CO
			//
			//        I will find VE in GOALLINK0204=OP,VE,LNK_RELATED_VE. 
			//        Further, I will check whether at least one of the fields defined for the Y axis is CUR_Value (because GOALFILEVALUEACTUAL02=CUR_VALUE).
			//        I will create a rowset on the GL file looking for the value of the field CUR_GOALOP (defined in GOALFILEVALUE02=CUR_GOALOP) with this filter: 
			//
			//            LNK_FOR_LO is empty and LNK_FOR_US is empty and LNK_FOR_DV is empty and LNK_FOR_VE=FXMG and LNK_FOR_PD is empty and LNK_FOR_CO is empty and CHK_OP=1
			//
			//        When I get the GL record, I have to see whether there are additional criteria defined for the value on my view's file. 
			//        For ex, if my view is for AC file, in order for the LI__GOALAC value from the GL record to be 'applicable', 
			//        the view's Y axis must be for Record Count and it must contain these filter conditions:
			//
			//            MLS_Purpose<>Lead and MLS_Type=<GLRecord'sMLS_ACTIVITYTYPEValue>
			//
			//        Summary of additional filter tests:
			//        - In the Case of LE count, the view filter must contain MLS_Purpose=Lead and Y axis must be on Record count.
			//        - In the Case of AC count, the view filter must contain MLS_Purpose<>Lead and MLS_Type=<GLRecord'sMLS_ACTIVITYTYPEValue> and Y axis must be on Record count.
			//        - In the Case of AP count, the view filter must contain MLS_Type=<GLRecord'sMLS_APPTTYPEValue> and Y axis must be on Record count.
			//
			//OTH_GOALS metadata with multiple Goals per file (Opp count and Opp value) that are pertinent in this context (note that
			//       defining fields and (COUNT) requires defining the same file as two GOALFILExx= lines--see OH example):
			//        GOALFILE01=LE,Leads
			//        GOALFILE02=OP,Opps
			//        GOALFILE03=QL,Quote Lines
			//        GOALFILE04=OH,Orders
			//        GOALFILE05=AC,Activity
			//        GOALFILE06=AP,Appts
			//        GOALFILE07=OP,Opps
			//        GOALFILE08=QT,Quotes
			//        GOALFILE09=OH,Orders
			//        GOALFILECOUNT = 9
			//        GOALFILEVALUE01 = LI__GOALLE
			//        GOALFILEVALUE02 = CUR_GOALOP
			//        GOALFILEVALUE03 = CUR_GOALQL
			//        GOALFILEVALUE04 = CUR_GOALOH
			//        GOALFILEVALUE04=CUR_GROSSPROFIT|CUR_GOALOH,CUR_BOOKING|CUR_GOALOH2
			//        GOALFILEVALUE04_OLD_SYNTAX = CUR_GOALOH
			//        GOALFILEVALUE05 = LI__GOALAC
			//        GOALFILEVALUE06 = LI__GOALAP
			//        GOALFILEVALUE07 = LI__GOALOP
			//        GOALFILEVALUE08 = LI__GOALQT
			//        GOALFILEVALUE09 = LI__GOALOH
			//        GOALFILEVALUEACTUAL_NOTE = GOALFILEVALUEACTUALxx lines are not needed if GOALFILEVALUExx lines have | symbol.
			//        GOALFILEVALUEACTUAL01 = (COUNT)
			//        GOALFILEVALUEACTUAL02 = CUR_VALUE
			//        GOALFILEVALUEACTUAL03 = CUR_SUBTOTAL
			//        GOALFILEVALUEACTUAL04 = CUR_EXTPRICE
			//        GOALFILEVALUEACTUAL05 = (COUNT)
			//        GOALFILEVALUEACTUAL06 = (COUNT)
			//        GOALFILEVALUEACTUAL07 = (COUNT)
			//        GOALFILEVALUEACTUAL08 = (COUNT)
			//        GOALFILEVALUEACTUAL09 = (COUNT)
			//        GOALLINK0101=LE,LO,LNK_RELATED_LO
			//        GOALLINK0102=LE,US,LNK_CREDITEDTO_US
			//        GOALLINK0103=LE,DV,LNK_RELATED_DV
			//        GOALLINK0104=LE,VE,LNK_RELATED_VE
			//        GOALLINK0105=LE,PD,LNK_RELATED_PD
			//        GOALLINK0106=LE,CO,LNK_RELATED_CO
			//        GOALLINK0107=LE,TE,LNK_RELATED_TE
			//        GOALLINK0108=LE,GR,LNK_RELATED_GR
			//        GOALLINK0201=OP,LO,LNK_RELATED_LO
			//        GOALLINK0202=OP,US,LNK_CREDITEDTO_US
			//        GOALLINK0203=OP,DV,LNK_RELATED_DV
			//        GOALLINK0204=OP,VE,LNK_RELATED_VE
			//        GOALLINK0205=OP,PD,LNK_FOR_PD
			//        GOALLINK0206=OP,CO,LNK_FOR_CO
			//        GOALLINK0207=OP,TE,LNK_RELATED_TE
			//        GOALLINK0208=OP,GR,LNK_RELATED_GR
			//        GOALLINK0301=QL,LO,LNK_TAKENAT_LO
			//        GOALLINK0302=QL,US,LNK_CREDITEDTO_US
			//        GOALLINK0303=QL,DV,LNK_RELATED_DV
			//        GOALLINK0304=QL,VE,LNK_RELATED_VE
			//        GOALLINK0305=QL,PD,LNK_FOR_PD
			//        GOALLINK0306=QL,CO,LNK_TO_CO
			//        GOALLINK0307=QL,TE,LNK_RELATED_TE
			//        GOALLINK0308=QL,GR,LNK_RELATED_GR
			//        GOALLINK0401=OH,LO,TXT_BranchID,1,P21
			//        GOALLINK0402=OH,US,TXT_CreditedToUS,1,P21
			//        GOALLINK0403=OH,DV,TXT_DivisionID,1,P21
			//        GOALLINK0404=OH,VE,TXT_VendorID,1,P21
			//        GOALLINK0405=OH,PD,TXT_ProductID,1,P21
			//        GOALLINK0406=OH,CO,TXT_CompanyID,1,P21
			//        GOALLINK0407=OH,TE,LNK_RELATED_TE
			//        GOALLINK0408=OH,GR,LNK_RELATED_GR
			//        GOALLINK0501=AC,LO,LNK_RELATED_LO
			//        GOALLINK0502=AC,US,LNK_CREDITEDTO_US
			//        GOALLINK0503=AC,DV,LNK_RELATED_DV
			//        GOALLINK0504=AC,VE,LNK_RELATED_VE
			//        GOALLINK0505=AC,PD,LNK_RELATED_PD
			//        GOALLINK0506=AC,CO,LNK_RELATED_CO
			//        GOALLINK0507=AC,TE,LNK_RELATED_TE
			//        GOALLINK0508=AC,GR,LNK_RELATED_GR
			//        GOALLINK0601=AP,LO,LNK_RELATED_LO
			//        GOALLINK0602=AP,US,LNK_INVOLVES_US
			//        GOALLINK0603=AP,DV,LNK_RELATED_DV
			//        GOALLINK0604=AP,VE,LNK_RELATED_VE
			//        GOALLINK0605=AP,PD,LNK_FOR_PD
			//        GOALLINK0606=AP,CO,LNK_RELATED_CO
			//        GOALLINK0607=AP,TE,LNK_RELATED_TE
			//        GOALLINK0608=AP,GR,LNK_RELATED_GR
			//        GOALLINK0701=OP,LO,LNK_RELATED_LO
			//        GOALLINK0702=OP,US,LNK_CREDITEDTO_US
			//        GOALLINK0703=OP,DV,LNK_RELATED_DV
			//        GOALLINK0704=OP,VE,LNK_RELATED_VE
			//        GOALLINK0705=OP,PD,LNK_FOR_PD
			//        GOALLINK0706=OP,CO,LNK_FOR_CO
			//        GOALLINK0707=OP,TE,LNK_RELATED_TE
			//        GOALLINK0708=OP,GR,LNK_RELATED_GR
			//        GOALLINK0801=QT,LO,LNK_TAKENAT_LO
			//        GOALLINK0802=QT,US,LNK_CREDITEDTO_US
			//        GOALLINK0803=QT,DV,LNK_NOTSUPPORTED_DV
			//        GOALLINK0804=QT,VE,LNK_NOTSUPPORTED_VE
			//        GOALLINK0805=QT,PD,LNK_NOTSUPPORTED_PD
			//        GOALLINK0806=QT,CO,LNK_TO_CO
			//        GOALLINK0807=QT,TE,LNK_NOTSUPPORTED_TE
			//        GOALLINK0808=QT,GR,LNK_RELATED_GR
			//        GOALLINK0901=OH,LO,TXT_BranchID,1,P21
			//        GOALLINK0902=OH,US,TXT_CreditedToUS,1,P21
			//        GOALLINK0903=OH,DV,TXT_DivisionID,1,P21
			//        GOALLINK0904=OH,VE,TXT_VendorID,1,P21
			//        GOALLINK0905=OH,PD,TXT_ProductID,1,P21
			//        GOALLINK0906=OH,CO,TXT_CompanyID,1,P21
			//        GOALLINK0907=OH,TE,LNK_RELATED_TE
			//        GOALLINK0908=OH,GR,LNK_RELATED_GR
			//        GOALLINKCOUNT=8
			//        GOALLINKHELP=Par1=File,Par2=LinkedFile,Par3=LinkName,Par4=LinkProcessingMode(1=TN)
			//
			//The following links that are supported on the Goal form do not exist in the Master database
			//and would therefore be ignored. IOW, Goals that use these links would never be evaluated.
			//       Activity - Division
			//       Appointment - Division
			//       Appointment - Territory
			//       Lead (Activity) - Division
			//       Order - Division
			//       Order - Group
			//       Order - Territory
			//       Quote - Division
			//       Quote - Group
			//       Quote - Product
			//       Quote - Territory
			//       Quote - Vendor
			//       Quote Line - Group



			string sProc = ""; //System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sGoalsMD = null;
			string sViewFile = null;
			string sGoalFile = ""; //EL, OP, QL, AC, AP
			string[] sGoalFiles = null;
			int iGoalFileCount = 0;
			int i = 0;
			int iFilePos = 0;
			int iFileLastPos = 0;
			string sTemp = null;
			string sValueField = null;
			string sFileFieldName = "";
			clArray aGoalLinks = null; //Array of Goal links from view's file in format 'LNK_CREDITEDTO_US'
			clArray aGoalLinksTemp = null; //Temp version of aGoalLinks from which we remove matched conditions
			string sGoalLink = null;
			int iGoalLinkCount = 0;
			decimal dResult = 0;
			string sCondition = null;
			string[] sViewConditions = null;
			int iRow = 0;
			string sGraphShow = null;
			string sGLCondition = null;
			string sSortFieldPrefix = null;
			string sFieldPart = null;
			string sOperator = null;
			string sValue = null;
			int j = 0;
			string sGoalLinkName = null;
			string sACTypeValue = "";
			string sAPTypeValue = "";
			DataTable dtViewConditions = null;
			clTable tViewConditions = null;
			int iRowCount = 0;
			int iTableRow = 0;
			string sSelectedRecordFile = null;
			int iTemp = 0;
			clRowSet doRS = null;
			bool bNextFileDef = false;
			int iResult = 0;

			//VS 07082015
			string sGoalFilterMessage = "";

			//Try

			Array.Resize(ref sGoalFiles, 1);
				sGoalFiles[0] = "";
				sViewFile = goTR.StrRead(par_sViewMetadata, "FILE", null, false).ToUpper();
				sGoalsMD = goMeta.PageRead("GLOBAL", "OTH_GOALS", "", true);
				iGoalFileCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sGoalsMD, "GOALFILECOUNT", "0", false), "0"));
				sCondition = par_sCurrentFilter.ToUpper();
				if (sCondition == "")
				{
					sCondition = goTR.StrRead(par_sViewMetadata, "CONDITION", null, false).ToUpper();
				}
				//Standardize the condition line: remove parens
				sCondition = goTR.Replace(sCondition, "(", "");
				sCondition = goTR.Replace(sCondition, ")", "");
				//Deal with <%NODATA%>
				if (sCondition == "<%NODATA%>")
				{
					par_sMessage = "The view displays no data. Goal value is not returned.";
					return -10M;
				}
				//Remove spaces around '=' and '<>' operators, we'll search for FIELD= or FIELD<> lines later
				sCondition = goTR.Replace(sCondition, " =", "=");
				sCondition = goTR.Replace(sCondition, "= ", "=");
				sCondition = goTR.Replace(sCondition, " <>", "<>");
				sCondition = goTR.Replace(sCondition, "<> ", "<>");

				//Any OR's in the condition?
				if (goTR.Position(sCondition, " OR ") > 0)
				{
					par_sMessage = "There is an OR in the view's filter. ORs are not supported.";
					return -4M;
				}

				//Is our file supported in Goals?
				//GOALFILE02=OP,Opps
				//GOALFILE07=OP,Opps
				iFilePos = 0;
				iFileLastPos = 0;
				bNextFileDef = false;

				//Populate a string array with names of files
				for (i = 1; i <= iGoalFileCount; i++)
				{
					sTemp = (goTR.StrRead(sGoalsMD, "GOALFILE" + goTR.Pad(i.ToString(), 2, "0", "L"), null, false)).ToUpper();
					sGoalFile = goTR.ExtractString(sTemp, 1, ",");
					Array.Resize(ref sGoalFiles, i);
					sGoalFiles[i - 1] = sGoalFile;
				}

				//Entry point for GOTO from errors below
	ProcessNextFilePosition:
				for (i = 1; i <= sGoalFiles.GetUpperBound(0) + 1; i++) //iGoalFileCount
				{
					sGoalFile = sGoalFiles[i - 1];
					if (sGoalFile == sViewFile)
					{
						//We found the file
						iFilePos = i;
						if (bNextFileDef)
						{
							//Found the file but need to find the same file's next position
							if (iFilePos > iFileLastPos)
							{
								//This is the file's next position, exit for and process it
								break;
							}
							else
							{
								//We processed the file in this position, continue looping
							}
						}
						else
						{
							//Found the file, not interested in its next position, exit for and process it
							break;
						}
					}
					else
					{
						//Accommodate fake file 'LE' = lead
						if (sViewFile == "AC" && goTR.Position(sCondition, "MLS_PURPOSE=8") != 0 && sGoalFile == "LE")
						{
							iFilePos = i;
							if (bNextFileDef)
							{
								if (iFilePos > iFileLastPos)
								{
									iFileLastPos = iFilePos;
									break;
								}
							}
							else
							{
								break;
							}
						}
					}
				}
				if (iFilePos < 1)
				{
					//File not found
					par_sMessage = "Goals are not tracked for the view's file '" + goData.GetFileLabelFromName(sViewFile) + "'.";
					return -1M;
				}

				if (bNextFileDef)
				{
					//We are looking for the next file's position. Did we find it?
					if (iFilePos > iFileLastPos)
					{
						//We found the file's next position, proceed processing it
						iFileLastPos = iFilePos;
					}
					else
					{
						//File's next position not found. If a message was already set, return it, otherwise treat this as file not found
						if (par_sMessage == "")
						{
							par_sMessage = "Goals are not tracked for the view's file '" + goData.GetFileLabelFromName(sViewFile) + "'.";
							iResult = -1;
						}
						return iResult;
					}
				}
				else
				{
					iFileLastPos = iFilePos;
				}

				//Check whether the view's X axis (sort order) field for the current chart level is an aggregate date field (DTY, DTQ, DTM, DTD)
				sSortFieldPrefix = goTR.GetPrefix(par_sSortField);
				switch (sSortFieldPrefix)
				{
					case "DTY_":
					case "DTQ_":
					case "DTM_":
					case "DTD_":
					break;
						//Match found, proceed
					default:
						sTemp = goData.GetFieldFullLabelFromName(sViewFile, par_sSortField);
						if (sTemp == "")
						{
							sTemp = par_sSortField;
						}
						par_sMessage = "The X-axis (sort) field '" + sTemp + "' is not supported. Select a year, year/quarter, year/month or year/month/day field.";
						iResult = -3;
						bNextFileDef = true;
						goto ProcessNextFilePosition;
				}



				//Identify the GL value field name
				//           GOALFILEVALUE02 = CUR_GOALOP
				//----------------------------------
				//'The following was replaced with PJ's mods (moved by MI from above to below), MI restored it 1/28/10
				//'Identify the GL value field name
				//'           GOALFILEVALUE02 = CUR_GOALOP
				//sValueField = UCase(goTR.StrRead(sGoalsMD, "GOALFILEVALUE" & goTR.Pad(iFilePos.ToString, 2, "0", "L"), , False))

				//'Identify the field in the view's file that the Goal value is for: can be e.g. 'CUR_Value' or '(COUNT)'. This is
				//'optional if GOALFILEVALUExx uses the new syntax GOALFILEVALUE04=CUR_GROSSPROFIT|CUR_GOALOH,CUR_BOOKING|CUR_GOALOH2
				//'           GOALFILEVALUEACTUAL02 = CUR_VALUE
				//sFileFieldName = UCase(goTR.StrRead(sGoalsMD, "GOALFILEVALUEACTUAL" & goTR.Pad(iFilePos.ToString, 2, "0", "L"), , False))
				//----------------------------------

				//Disallow mismatch between the Y axis definition (for COUNT vs TOTAL) and the Goal Value
				sGraphShow = goTR.StrRead(par_sViewMetadata, "GRAPHYSHOW", "COUNT", false).ToUpper();
				switch (sGraphShow)
				{
					case "COUNT":
						sTemp = "record count";
						break;
					case "STATISTICS":
						sTemp = "statistics";
						break;
					default:
						sTemp = "totals";
						break;
				}
				//MI 1/28/11 Moved this into PJ's block below
				//Select Case sFileFieldName
				//    Case "(COUNT)"
				//        'Count
				//        If sGraphShow <> "COUNT" Then
				//            par_sMessage = "Goal records support record count for " & goData.GetFileLabelPlurFrName(sViewFile) & ". Select Record count under Y axis. Note that totals also may be supported."
				//            iResult = -2
				//            bNextFileDef = True
				//            GoTo ProcessNextFilePosition
				//        End If
				//    Case Else
				//        'Value.
				//        If sGraphShow <> "TOTAL" Then
				//            par_sMessage = "Goal records support totals for " & goData.GetFileLabelPlurFrName(sViewFile) & ". Select Total under Y axis. Note that record count also may be supported."
				//            iResult = -2
				//            bNextFileDef = True
				//            GoTo ProcessNextFilePosition
				//        End If
				//End Select

				//Check whether Y axis is for COUNT or for TOTAL on at least one field that matches GOALFILEVALUEACTUAL02=
				//(ex: in VIE_, GRAPHYFIELD1=CUR_VALUE and in OTH_GOALS, GOALFILEVALUEACTUAL02=CUR_VALUE)

				//MI 1/28/11 This is the original line, which was under sGraphShow Case "TOTAL". It is replaced with Paul's code below.
				//If UCase(goTR.StrRead(par_sViewMetadata, "GRAPHYFIELD1", , False)) = sFileFieldName Then GoTo MatchFound

				//MI 1/28/11 Removed support for GRAPHYFIELD2, 3, and 4 because they create an ambiguity for
				//the evaluation of multiple Goal fields per file. Originally, we supported only one Count and one Value
				//field per file in the GL records and OTH_GOALS metadata. Now that we support multiple (for ex. OP Value
				//and OP Value Index), multiple GRAPHYFIELDs could create multiple matches and we would need to return
				//multiple Goal values. 
				//If UCase(goTR.StrRead(par_sViewMetadata, "GRAPHYFIELD2", , False)) = sFileFieldName Then GoTo MatchFound
				//If UCase(goTR.StrRead(par_sViewMetadata, "GRAPHYFIELD3", , False)) = sFileFieldName Then GoTo MatchFound
				//If UCase(goTR.StrRead(par_sViewMetadata, "GRAPHYFIELD4", , False)) = sFileFieldName Then GoTo MatchFound

				//----------- Begin Paul's new code 1/26/11 ---------------
				//PJ Mod for multiple values per file. 
				//MI 1/31/11 Revised and refactored.
				//   Format of GOALFILEVALUE is now 'GOALFILEVALUE04=CUR_GROSSPROFIT|CUR_GOALOH,CUR_BOOKING|CUR_GOALOH2'
				//   or just GOALFILEVALUE04=CUR_GROSSPROFIT (in which case GOALFILEVALUEACTUAL= is required).
				sValueField = (goTR.StrRead(sGoalsMD, "GOALFILEVALUE" + goTR.Pad(iFilePos.ToString(), 2, "0", "L"), null, false)).ToUpper();
				string[] aValueField = null;
				string sFieldPair = null;
				if (sValueField.Contains(","))
				{
					//There is more than one value field for this file. Split value field pairs into string array
					aValueField = sValueField.Split(',');
					//Loop thru field pairs
					for (int iGoalFieldCount = 0; iGoalFieldCount <= aValueField.GetUpperBound(0); iGoalFieldCount++)
					{
						sFieldPair = aValueField[iGoalFieldCount];
						if (sFieldPair.Contains("|"))
						{
							//Field pair contains pipe delimiter, so parse file field and goal field
							sFileFieldName = sFieldPair.Split('|')[0];
							sValueField = sFieldPair.Split('|')[1];
							//Does the view Y axis type (Count vs. Total) match the Goal type? If not, go to the next file
							iResult = GetGoalValueYAxisMatch(sFileFieldName, sGraphShow, sViewFile, ref par_sMessage, ref bNextFileDef, par_sViewMetadata);
							if (iResult == 0)
							{
								goto MatchFound;
							}
							else
							{
								if (iGoalFieldCount == aValueField.GetUpperBound(0))
								{
									//The last Goal field/value was evaluated, go to the next file
									goto ProcessNextFilePosition;
								}
								else
								{
									//There are more Goal field/values to evaluate, go to the next field/value in this loop
									goto NextFieldPair;
								}
							}
						}
						else
						{
							//sFieldPair does not contain "|", invalid definition, raise a warning and skip it
							goErr.SetWarning(35000, sProc, "Unsupported GOALFILEVALUE value: '" + sFieldPair + "'. A view file field and Goal field delimited with | must appear between the commas. The definition is skipped.");
						}
	NextFieldPair: ;
					}
				}
				else
				{
					//only one value field for this file. Does it contain pipe delimiter?
					if (sValueField.Contains("|"))
					{
						//Field pair contains pipe delimiter, so parse file field and goal field
						sFileFieldName = sValueField.Split('|')[0];
						sValueField = sValueField.Split('|')[1];
						//Does the view Y axis type (Count vs. Total) match the Goal type? If not, go to the next file
						iResult = GetGoalValueYAxisMatch(sFileFieldName, sGraphShow, sViewFile, ref par_sMessage, ref bNextFileDef, par_sViewMetadata);
						if (iResult == 0)
						{
							goto MatchFound;
						}
						else
						{
							goto ProcessNextFilePosition;
						}
					}
					else
					{
						//Old MD schema - GOALFILEVALUEACTUAL used in this case. GOALFILEVALUEACTUAL02 = CUR_VALUE
						sFileFieldName = (goTR.StrRead(sGoalsMD, "GOALFILEVALUEACTUAL" + goTR.Pad(iFilePos.ToString(), 2, "0", "L"), null, false)).ToUpper();
						//Does the view Y axis type (Count vs. Total) match the Goal type? If not, go to the next file
						iResult = GetGoalValueYAxisMatch(sFileFieldName, sGraphShow, sViewFile, ref par_sMessage, ref bNextFileDef, par_sViewMetadata);
						if (iResult == 0)
						{
							goto MatchFound;
						}
						else
						{
							goto ProcessNextFilePosition;
						}
					}
				}
				//----------- End Paul's new code -------------------

				//MI 1/31/11 Commented because this (and more) runs inside GetGoalValueYAxisMatch(), which is called in all cases above.
				//'Compare the view's Y axis type (count vs. totals) with the Goal's type.
				//Select Case sGraphShow
				//    Case "COUNT"
				//        If sFileFieldName <> "(COUNT)" Then
				//            par_sMessage = "Goal records support record count. Select Record count under Y axis. Note that record count also may be supported."
				//            iResult = -2
				//            bNextFileDef = True
				//            GoTo ProcessNextFilePosition
				//        End If
				//    Case "STATISTICS"
				//        'This option is not supported currently and should not come up. 
				//        'This is coded here if it does get supported in the future with the syntax GOALFILEVALUEACTUAL02=CUR_VALUE|AVG
				//        par_sMessage = "Goal records support statistics. Select Statistics under Y axis. Note that totals or record count also may be supported."
				//        iResult = -2
				//        bNextFileDef = True
				//        GoTo ProcessNextFilePosition
				//    Case Else
				//        '"TOTAL"
				//        par_sMessage = "Goal records support totals on '" & goData.GetFieldFullLabelFromName(sViewFile, sFileFieldName) & "' field (and possibly other fields). Select one of those fields in the 'Field 1' combo box under Y axis. Note that record count also may be supported."
				//        iResult = -2
				//        bNextFileDef = True
				//        GoTo ProcessNextFilePosition
				//End Select

	MatchFound:

				//Identify Goal links
				//           GOALLINK0201=OP,LO,LNK_RELATED_LO
				//           GOALLINK0202=OP,US,LNK_CREDITEDTO_US
				//           GOALLINK0203=OP,DV,LNK_RELATED_DV
				//           GOALLINK0204=OP,VE,LNK_RELATED_VE
				//           GOALLINK0205=OP,PD,LNK_FOR_PD
				//           GOALLINK0206=OP,CO,LNK_FOR_CO
				//If a link is invalid, it is not loaded here.
				aGoalLinks = new clArray();
				iGoalLinkCount = Convert.ToInt32(goTR.StringToNum(goTR.StrRead(sGoalsMD, "GOALLINKCOUNT", "0", false), "0"));
				//Load aGoalLinks wiht values like 'LNK_RELATED_VE'
				for (i = 1; i <= iGoalLinkCount; i++)
				{
					sGoalLink = goTR.StrRead(sGoalsMD, "GOALLINK" + goTR.Pad(iFilePos.ToString(), 2, "0", "L") + goTR.Pad(i.ToString(), 2, "0", "L"), null, false);
					//MI 4/30/10 Commented If Then to make sure all Goal links are tested below and processed as BI__ID<1 in the GL query.
					//If sGoalLink <> "" Then
					//If goData.IsFieldValid(sViewFile, sGoalLink) Then
					sTemp = goTR.ExtractString(sGoalLink, 3, ",");
					if (sTemp[0] == clC.EOT)
					{
						sTemp = "";
					}
					aGoalLinks.AddInfo(sTemp);
					//End If
				}
				aGoalLinksTemp = new clArray();
				aGoalLinks.CopyAllIn(ref aGoalLinksTemp);

				//---------------------
				//MI 2/21/11 Moved this block up before evaluating Y Axis 
				//'Check whether the view's X axis (sort order) field for the current chart level is an aggregate date field (DTY, DTQ, DTM, DTD)
				//sSortFieldPrefix = goTR.GetPrefix(par_sSortField)
				//Select Case sSortFieldPrefix
				//    Case "DTY_", "DTQ_", "DTM_", "DTD_"
				//        'Match found, proceed
				//    Case Else
				//        sTemp = goData.GetFieldFullLabelFromName(sViewFile, par_sSortField)
				//        If sTemp = "" Then
				//            sTemp = par_sSortField
				//        End If
				//        par_sMessage = "The X-axis (sort) field '" & sTemp & "' is not supported. Select a year, year/quarter, year/month or year/month/day field."
				//        iResult = -3
				//        bNextFileDef = True
				//        GoTo ProcessNextFilePosition
				//End Select
				//-----------------------

				//Check whether there is a match in OTH_GOALS for the view's filter conditions
				//Ex: if the VIE_ page has:
				//   CONDITION=LNK_Related_VE=<%SelectedRecordID FILE=VE%>
				//a match will be found in OTH_GOALS:
				//   GOALLINK0204=OP,VE,LNK_RELATED_VE
				//We accept the view as compatible with Goals only if ALL of its conditions match
				//Goal conditions. IOW, if the view has even a single condition on a field that's not
				//supported in Goals, the Goal will not be evaluated. In our example, a condition like
				//MLS_Status=2 would disqualify the view from Goal evaluation.
				//The same field must not be found in multiple conditions.
				//Only = operator is allowed in all conditions.
				//Date conditions are ignored (and therefore supported) because goals are always per year. 
				//The Goal amount is adjusted for the time period used for the X axis (only DTY, DTQ, DTM,
				// and DTD are supported on X axis).
				//First remove parens from the condition line
				sViewConditions = Microsoft.VisualBasic.Strings.Split(sCondition, " AND ");

				//Load view conditions into a clTable object for easier searching
				dtViewConditions = new DataTable("VIEWCONDITIONS");
				tViewConditions = new clTable(ref dtViewConditions, "Value", "Text");
				//Set up addl columns
				tViewConditions.AddColumn("sFieldPart");
				tViewConditions.AddColumn("sOperator");
				tViewConditions.AddColumn("sValue");
				if (sViewConditions[0] != "")
				{
					for (i = 0; i <= sViewConditions.GetUpperBound(0); i++)
					{
						tViewConditions.Add();
						iRowCount = (int)tViewConditions.Count();
						tViewConditions.SetVal("Value", iRowCount, (i + 1).ToString());
						tViewConditions.SetVal("Text", iRowCount, sViewConditions[i]);
						sFieldPart = goTR.ExtractFromCondition(sViewConditions[i], "FIELD");
						//We are interested only in the link part
						if (goTR.ExtractString(sFieldPart, 2, "%%") == "GID_ID")
						{
							sFieldPart = goTR.ExtractString(sFieldPart, 1, "%%");
						}
						tViewConditions.SetVal("sFieldPart", iRowCount, sFieldPart);
						sOperator = goTR.ExtractFromCondition(sViewConditions[i], "OPERATOR");
						tViewConditions.SetVal("sOperator", iRowCount, sOperator);
						sValue = goTR.ExtractFromCondition(sViewConditions[i], "VALUE").Trim(' ');
						if (par_sMode.ToUpper() == "TEST")
						{
							//Replace any potential keyword with some supported value. It needs to be 'real',
							//but can be anything. We are testing whether getting a Goal record is possible,
							//not getting an actual Goal record for the view's actual filter. For SelectedRecordxxx
							//keywords we pull the first record from the file defined in FILE= in the keyword.
							if (goTR.Position(sValue, "<%STARTDATE%>") > 0)
							{
								//Evaluate to first day of this month
								sTemp = goTR.GetYear(DateTime.Now) + "-";
								sTemp += goTR.Pad(goTR.GetMonth(DateTime.Now), 2, "0", "L");
								sTemp += "-01";
								sValue = goTR.Replace(sValue, "<%STARTDATE%>", goTR.DateToString(goTR.StringToDate(sTemp), "SYSTEM"));
							}
							if (goTR.Position(sValue, "<%ENDDATE%>") > 0)
							{
								//Evaluate to first day of next month
								sTemp = goTR.GetYear(DateTime.Now) + "-";
								sTemp += goTR.Pad(goTR.GetMonth(DateTime.Now.AddMonths(1)), 2, "0", "L");
								sTemp += "-01";
								sValue = goTR.Replace(sValue, "<%ENDDATE%>", goTR.DateToString(goTR.StringToDate(sTemp), "SYSTEM"));
							}
							if (goTR.Position(sValue, "<%LASTSELECTEDSTARTDATE%>") > 0)
							{
								//Evaluate to first day of this month
								sTemp = goTR.GetYear(DateTime.Now) + "-";
								sTemp += goTR.Pad(goTR.GetMonth(DateTime.Now), 2, "0", "L");
								sTemp += "-01";
								sValue = goTR.Replace(sValue, "<%LASTSELECTEDSTARTDATE%>", goTR.DateToString(goTR.StringToDate(sTemp), "SYSTEM"));
							}
							if (goTR.Position(sValue, "<%LASTSELECTEDENDDATE%>") > 0)
							{
								//Evaluate to first day of next month
								sTemp = goTR.GetYear(DateTime.Now) + "-";
								sTemp += goTR.Pad(goTR.GetMonth(DateTime.Now.AddMonths(1)), 2, "0", "L");
								sTemp += "-01";
								sValue = goTR.Replace(sValue, "<%LASTSELECTEDENDDATE%>", goTR.DateToString(goTR.StringToDate(sTemp), "SYSTEM"));
							}
							if (goTR.Position(sValue, "<%SELECTEDRECORDID") > 0)
							{
								iTemp = (int)goTR.Position(sValue, " FILE=");
								if (iTemp < 1)
								{
									par_sMessage = "Condition '" + sViewConditions[i] + "' lacks the FILE= definition in the SelectedRecordID keyphrase.";
									return -8M;
								}
								else
								{
									sSelectedRecordFile = goTR.FromTo(sValue, iTemp + 6, iTemp + 7);
								}
								doRS = new clRowSet(sSelectedRecordFile, clC.SELL_READONLY, "", "GID_ID ASC", "GID_ID", 1);
								if (doRS.Count() < 1)
								{
									par_sMessage = "Condition '" + sViewConditions[i] + "' references a file that has no records. 'SELECTEDRECORDID' cannot be evaluated.";
									return -9M;
								}
								else
								{
									sValue = Convert.ToString(doRS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY));
								}
								doRS = null;
							}
							if (goTR.Position(sValue, "<%SELECTEDVIEWRECORDID") > 0)
							{
								iTemp = (int)goTR.Position(sValue, " FILE=");
								if (iTemp < 1)
								{
									par_sMessage = "Condition '" + sViewConditions[i] + "' lacks the FILE= definition in the SelectedRecordID keyphrase.";
									return -8M;
								}
								else
								{
									sSelectedRecordFile = goTR.FromTo(sValue, iTemp + 6, iTemp + 7);
								}
								doRS = new clRowSet(sSelectedRecordFile, clC.SELL_READONLY, "", "GID_ID ASC", "GID_ID", 1);
								if (doRS.Count() < 1)
								{
									par_sMessage = "Condition '" + sViewConditions[i] + "' references a file that has no records. 'SELECTEDVIEWRECORDID' cannot be evaluated.";
									return -9M;
								}
								else
								{
									sValue = Convert.ToString(doRS.GetFieldVal("GID_ID", clC.SELL_FRIENDLY));
								}
								doRS = null;
							}
						}
						tViewConditions.SetVal("sValue", iRowCount, sValue);
					}
				}

				for (i = 1; i <= tViewConditions.Count(); i++)
				{
					sFieldPart = tViewConditions.GetVal("sFieldPart", i).ToString();
					sOperator = tViewConditions.GetVal("sOperator", i).ToString();
					sValue = tViewConditions.GetVal("sValue", i).ToString();
					//Ignore a date condition - we display the Goal for the date period used in X axis
					//and don't care about the date filter (unless it constrains data to less than
					//the X axis period, but we don't look at that).
					if (goTR.GetPrefix(sFieldPart) == "DTT_")
					{
						goto ProcessNextCondition;
					}
					//If even a single condition (other than on DTT) doesn't contain the right operator ('=' in most cases),
					//the view is ineligible for Goal evaluation
					if (sGoalFile == "AC")
					{
						if (sFieldPart == "MLS_PURPOSE")
						{
							if (sOperator != "<>")
							{
								//par_sMessage = "Modify the view's filter: change the condition for field '" & goData.GetFieldFullLabelFromName(sViewFile, sFieldPart) & "' to 'Is not Lead'."
								par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
								return -6M;
							}
						}
						else
						{
							if (sOperator != "=")
							{
								//par_sMessage = "Modify the view's filter: change the condition phrase for field '" & goData.GetFieldFullLabelFromName(sViewFile, sFieldPart) & "' to 'Is' or 'Is Equal To'."
								par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
								return -6M;
							}
						}
					}
					else
					{
						//VS 07082015
						//If sOperator <> "=" Then
						//    'par_sMessage = "Modify the view's filter: change the condition phrase for field '" & goData.GetFieldFullLabelFromName(sViewFile, sFieldPart) & "' to 'Is' or 'Is Equal To'."
						//    par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below."
						//    Return -6
						//End If
					}
					if (sFieldPart == "")
					{
						par_sMessage = "The view's filter has a blank field in one of the conditions. The view may be damaged.";
						return -6M;
					}
					sValue = goTR.RemoveSingleQuotes(sValue);
					//Is there a match in Goal links for this view condition's link?
					//aGoalLinksTemp contains values like 'LNK_RELATED_VE'
					//PJ 1/21/11: added par3 = False for case-insensitive
					iRow = (int)aGoalLinksTemp.SeekInfo(sFieldPart, true, false);
					if (iRow > 0)
					{
						//Link found, proceed. Remove the item from temp list of links supported in Goals
						//to avoid finding it more than once if there are multiple conditions on this link in the view.
						aGoalLinksTemp.DeleteInfo(iRow);
					}
					else
					{
						//Link not found, is this a condition on a hard-coded assumption (e.g. Lead purpose Activity) 
						//or additional consideration (AP Type, AC Type) in Goals?
						//Summary of additional filter tests (currently hard-coded):
						//- In the Case of LE count, the view filter must contain MLS_Purpose=Lead and Y axis must be on Record count.
						//- In the Case of AC count, the view filter must contain MLS_Purpose<>Lead and MLS_Type=<GLRecord'sMLS_ACTIVITYTYPEValue> and Y axis must be on Record count.
						//- In the Case of AP count, the view filter must contain MLS_Type=<GLRecord'sMLS_APPTTYPEValue> and Y axis must be on Record count.
						if (sGoalFile == "LE")
						{
							//Lead view must have Purpose=Lead
							if (sFieldPart == "MLS_PURPOSE" && sValue == "8")
							{
								goto ProcessNextCondition;
							}
						}
						if (sGoalFile == "AC")
						{
							//AC view must have: Purpose<>8 (Lead) and Type must be defined
							if (sFieldPart == "MLS_PURPOSE" && sOperator == "<>" && sValue == "8")
							{
								//Must not be lead, those are counted separately under 'LE'
								//Type must be specified in the view filter because the Goal requires it
								iTableRow = (int)tViewConditions.Seek("sFieldPart", "MLS_TYPE");
								if (iTableRow < 1)
								{
									//par_sMessage = "Add a filter condition '" & goData.GetFieldFullLabelFromName(sViewFile, "MLS_TYPE") & " Is' and select a type for which Goal records are defined, for example 'Sales Visit'."
									par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
									return -6M;
								}
								goto ProcessNextCondition;
							}
							if (sFieldPart == "MLS_TYPE")
							{
								//We found a Type condition, make sure Purpose is defined and is not Lead
								iTableRow = (int)tViewConditions.Seek("sFieldPart", "MLS_PURPOSE");
								if (iTableRow < 1)
								{
									//par_sMessage = "Add a filter condition '" & goData.GetFieldFullLabelFromName(sViewFile, "MLS_PURPOSE") & " is not Lead'."
									par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
									return -6M; //Purpose not defined at all
								}
								if (tViewConditions.GetVal("sOperator", iTableRow).ToString() == "<>" && tViewConditions.GetVal("sValue", iTableRow).ToString() == "8")
								{
									sACTypeValue = sValue; //remember the AC Type for filtering Goals later
								}
								else
								{
									//par_sMessage = "Change the filter condition for field '" & goData.GetFieldFullLabelFromName(sViewFile, "MLS_PURPOSE") & "' to 'Is not Lead'."
									par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
									return -6M; //Purpose=Lead
								}
								goto ProcessNextCondition;
							}
						}
						if (sGoalFile == "AP")
						{
							//AP view must have Type defined
							if (sFieldPart == "MLS_TYPE")
							{
								sAPTypeValue = sValue;
								goto ProcessNextCondition;
							}
						}

						//VS 07082015: Goal Line Change to display line ignoring other filters

						//par_sMessage = "Remove the filter condition '" & sFieldPart & sOperator & sValue & "'. Either there are multiple conditions for '" & sFieldPart & "' field or the condition for this field is not supported by Goal records."
						//iResult = -5
						//bNextFileDef = True
						//GoTo ProcessNextFilePosition

						if (aGoalLinks.SeekInfo(sFieldPart, true, false) > 0)
						{
							par_sMessage = "Remove the filter condition '" + sFieldPart + sOperator + sValue + "'. There are multiple conditions for '" + sFieldPart + "' field.";
							iResult = -5;
							bNextFileDef = true;
							goto ProcessNextFilePosition;
						}

						if (sGoalFilterMessage == "")
						{
							sGoalFilterMessage = "Below filters are ignored for goal calculation";
						}
						//Build Filter Text User Readable
						sGoalFilterMessage = sGoalFilterMessage + "\r\n" + goTR.ConvertFilterCondtoUserFriendlyText(sViewFile, sFieldPart + sOperator + sValue);

					}
	ProcessNextCondition: ;
				}

				//Read the actual Goal record 
				//First put together the filter for getting the right GL record
				//sCondition holds the whole CONDITION= line for the view
				sGLCondition = "";
				for (i = 1; i <= aGoalLinks.GetDimension(); i++)
				{
					//Did we delete this link from the temp array?
					if (aGoalLinksTemp.SeekInfo(aGoalLinks.GetInfo(i), true) < 1)
					{
						//We deleted the link, which means the view has a matching condition, add it to the filter string
						//Evaluate the value of that condition in the view
						for (j = 1; j <= tViewConditions.Count(); j++)
						{
							sFieldPart = tViewConditions.GetVal("sFieldPart", j).ToString();
							//PJ 1/21/11 Ucased this IfThen
							if (sFieldPart.ToUpper() == aGoalLinks.GetInfo(i).ToUpper())
							{
								//View condition field matches the Goal link
								sOperator = tViewConditions.GetVal("sOperator", j).ToString();
								sValue = tViewConditions.GetVal("sValue", j).ToString();
								//Rename actual link name to "LNK_FOR_<filename>" - that's how all links in GL file are named
								sGoalLinkName = "LNK_FOR_" + goTR.ExtractString(sFieldPart, 3, "_");
								sGLCondition += sGoalLinkName + sOperator + sValue + " AND ";
								//Another condition on the same link is not supported. It will be ignored if it exists (shouldn't).
								break;
							}
						}
	SkipToNextCondition: ;
					}
					else
					{
						//We didn't delete the link - there is no matching condition in the view, filter out that link
						sTemp = goTR.ExtractString(aGoalLinks.GetInfo(i), 3, "_");
						if (sTemp == "" || sTemp[0] == clC.EOT)
						{
							par_sMessage = "The link file in Goal link definition '" + i.ToString() + "' is missing or invalid.";
							goErr.SetWarning(35000, sProc, "The link file in a Goal link definition '" + i.ToString() + "' in OTH_GOALS (one of the GOALLINKxxyy= lines) is missing or invalid.");
							return -11M;
						}
						sGoalLinkName = "LNK_FOR_" + sTemp;
						sGLCondition += sGoalLinkName + "%%BI__ID<1 AND "; //This is the 'link is blank' syntax
					}
				}

				//Check whether conditions exist in the view filter for all hard-coded notions 
				//sFieldPart = tViewConditions.GetVal("sFieldPart", i).ToString
				//sOperator = tViewConditions.GetVal("sOperator", i).ToString
				//sValue = tViewConditions.GetVal("sValue", i).ToString
				switch (sGoalFile)
				{
					case "LE":
						iTemp = (int)tViewConditions.Seek("sFieldPart", "MLS_PURPOSE");
						if (iTemp < 1)
						{
							//par_sMessage = "Add filter condition '" & goData.GetFieldFullLabelFromName(sViewFile, "MLS_PURPOSE") & " Is Lead'."
							par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
							return -6M;
						}
						else
						{
							sTemp = tViewConditions.GetVal("sOperator", iTemp).ToString();
							if (sTemp != "=")
							{
								//par_sMessage = "Change filter condition for field '" & goData.GetFieldFullLabelFromName(sViewFile, "MLS_PURPOSE") & "' to 'Is Lead'."
								par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
								return -6M;
							}
							else
							{
								sTemp = tViewConditions.GetVal("sValue", iTemp).ToString();
								if (sTemp != "8") //8=Lead
								{
									//par_sMessage = "Change filter condition for field '" & goData.GetFieldFullLabelFromName(sViewFile, "MLS_PURPOSE") & "' to 'Is Lead'."
									par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
									return -6M;
								}
							}
						}
						break;
					case "AC":
						//Purpose
						iTemp = (int)tViewConditions.Seek("sFieldPart", "MLS_PURPOSE");
						if (iTemp < 1)
						{
							//par_sMessage = "Add filter condition '" & goData.GetFieldFullLabelFromName(sViewFile, "MLS_PURPOSE") & " Is not Lead'."
							par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
							return -6M;
						}
						else
						{
							sTemp = tViewConditions.GetVal("sOperator", iTemp).ToString();
							if (sTemp != "<>")
							{
								//par_sMessage = "Change filter condition for field '" & goData.GetFieldFullLabelFromName(sViewFile, "MLS_PURPOSE") & "' to 'Is not Lead'."
								par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
								return -6M;
							}
							else
							{
								sTemp = tViewConditions.GetVal("sValue", iTemp).ToString();
								if (sTemp != "8") //8=Lead
								{
									//par_sMessage = "Change filter condition for field '" & goData.GetFieldFullLabelFromName(sViewFile, "MLS_PURPOSE") & "' to 'Is not Lead'."
									par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
									return -6M;
								}
							}
						}
						//Type
						iTemp = (int)tViewConditions.Seek("sFieldPart", "MLS_TYPE");
						if (iTemp < 1)
						{
							//par_sMessage = "Add filter condition for field '" & goData.GetFieldFullLabelFromName(sViewFile, "MLS_TYPE") & "', for example '" & goData.GetFieldFullLabelFromName(sViewFile, "MLS_TYPE") & " is Sales Visit'."
							par_sMessage = "Modify the view's filter. See 'To display a Goal line in this view' below.";
							return -6M;
						}
						else
						{
							sTemp = tViewConditions.GetVal("sOperator", iTemp).ToString();
							if (sTemp != "=")
							{
								par_sMessage = "Change filter condition phrase for field '" + goData.GetFieldFullLabelFromName(sViewFile, "MLS_TYPE") + "' to 'Is', for example '" + goData.GetFieldFullLabelFromName(sViewFile, "MLS_TYPE") + " is Sales Visit'.";
								return -6M;
							}
						}
						break;
					case "AP":
						iTemp = (int)tViewConditions.Seek("sFieldPart", "MLS_TYPE");
						if (iTemp < 1)
						{
							par_sMessage = "Add filter condition for field '" + goData.GetFieldFullLabelFromName(sViewFile, "MLS_TYPE") + "', for example '" + goData.GetFieldFullLabelFromName(sViewFile, "MLS_TYPE") + " is Sales Visit'.";
							return -6M;
						}
						else
						{
							sTemp = tViewConditions.GetVal("sOperator", iTemp).ToString();
							if (sTemp != "=")
							{
								par_sMessage = "Change filter condition phrase for field '" + goData.GetFieldFullLabelFromName(sViewFile, "MLS_TYPE") + "' to 'Is', for example '" + goData.GetFieldFullLabelFromName(sViewFile, "MLS_TYPE") + " is Sales Visit'.";
								return -6M;
							}
						}
						break;
				}


				//Add to the GL filter conditions for hard-coded notions
				switch (sGoalFile)
				{
					case "AC":
						sGLCondition += "MLS_ACTIVITYTYPE=" + sACTypeValue + " AND ";
						break;
					case "AP":
						sGLCondition += "MLS_APPTTYPE=" + sAPTypeValue + " AND ";
						break;
					default:
					break;
						//No additional conditions needed
				}
				//Add condition for the value field to be > 0
				sGLCondition += sValueField + ">0";

				//Read the GL record
				doRS = new clRowSet("GL", clC.SELL_READONLY, sGLCondition, "DTT_ModTime DESC", sValueField, 1);
				if (doRS.Count() < 1)
				{
					if (par_sMode == "TEST")
					{
						par_sMessage = "";
						dResult = 0M;
					}
					else
					{
						par_sMessage = "A Goal record that matches this view wasn't found or it has the value of 0. Either enter a positive value in the Goal or create a Goal record that matches the view.";
						dResult = -7M;
					}
					goto WeAreDone;
				}
				else
				{
					par_sMessage = "";
					dResult = Convert.ToDecimal(goTR.StringToNum(doRS.GetFieldVal(sValueField, clC.SELL_SYSTEM)));
				}

				//VS 07082015
				if (par_sMessage == "" && sGoalFilterMessage != "")
				{
					par_sMessage = sGoalFilterMessage;
				}

				//Calculate the value pro-rated for the X axis aggregate date field's time period (ex, divide by 12 for DTM_)
				switch (sSortFieldPrefix)
				{
					case "DTQ_":
						dResult = dResult / 4;
						break;
					case "DTM_":
						dResult = dResult / 12;
						break;
					case "DTD_":
						dResult = dResult / 365;
						break;
					default: //DTY_
					break;
						//Year - no division is needed
				}

	WeAreDone:
			//DoNotCodeBelowThisLine

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return dResult;

		}


		public string GetDBPath()
		{
			//CS OK
			//Note: Be sure these variables are set somewhere

			//PURPOSE:
			//		Returns the path of the current DB, if any
			//PARAMETERS:
			//		N/A
			//RETURNS:
			//		The path of the current DB or "" if no DB opened
			//HOW IT WORKS:
			//		--
			//EXAMPLE:
			//		Info("Current DB Path is "+goData:GetDBPath())

			//USED A LOT... no log for speed optimisation
			//sProc is string="clData::GetDBPath"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


			if (bDBOpen)
			{
				return sCurrentDBPath;
			}
			else
			{
				return ("");
			}
		}


		public string GetDefaultSortDirection(string par_sFilename, bool par_bOneLetter = false)
		{
			//MI 1/26/07
			//*** USE THIS IN SELLSQL. ***
			//PURPOSE:
			//       Return 'ASC' or 'DESC'. If par-bOneLetter is True, return "a" or "d".
			//PARAMETERS:
			//       par_sFileName: name of the file
			//       par_bOneLetter: when true, returns just 'D' or 'A'.
			//RETURNS:
			//       String: 'ASC' or 'DESC'; if par_bOneLetter = True: 'A', 'D'

			switch (goData.GetDefaultOrder(par_sFilename))
			{
				case "DESCENDING":
					if (par_bOneLetter)
					{
						return "D";
					}
					else
					{
						return "DESC";
					}
					break;
				default:
					if (par_bOneLetter)
					{
						return "A";
					}
					else
					{
						return "ASC";
					}
					break;
			}

		}

		public string GetDefaultOrderLetter(string par_sFileName)
		{
			//MI 1/28/07
			//*** AVOID USING THIS IN SELLSQL. Use GetDefaultSortDirection instead. ***
			//PURPOSE:
			//		Retrieves the default key order (ascending/descending) for a file
			//SYNTAX (IN SCRIPT):
			//		Result = GetDefaultOrder(sFileName)
			//PARAMETERS:
			//		par_sFileName	String	System name of the file.
			//RETURNS
			//		String: "a" for ascending or "d" for descending.
			//EXAMPLE (IN SCRIPT):
			//		sKey is string = GetDefaultKey("COMPANY")
			//		sOrder is string = GetDefaultOrder("COMPANY")
			//		'sKey contains 'CompanyName'
			//		'sOrder contains 'a'

			switch (goData.GetDefaultOrder(par_sFileName))
			{
				case "DESCENDING":
					return ("d");
				default:
					return ("a");
			}

		}

		public string GetDefaultOrder(string par_sFileName, bool par_bAnswerAsBinary = false)
		{
			//MI 3/5/08 Changed this to use the direction set in GetDefaultSort (ASC or DESC)
			//       instead of being hard-coded. This is to allow customizing both the sort
			//       order and sort direction via clScripts.
			//MI 4/10/07
			//MI 1/28/07
			//PURPOSE:
			//		Send back the default sort direction on the specified file.
			//       KEEP IN SYNC WITH GetDefaultSort. See notes in clScripts.GetDefaultSort.
			//PARAMETERS:
			//		par_sFileName:			Name of the file for which we want the key
			//		par_bAnswerAsBinary:	if true, the anser is 1 for descending(reverse) and 0 for ascending(normal)
			//								(ie the REVERSE value for filters)
			//RETURNS:
			//		a String containing "ASCENDING" or "DESCENDING" or 1/0--- The 1/0 value is equivalent to reverse

			string sProc = "clData:GetDefaultOrder";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sFileName, SELL_LOGLEVEL_DETAILS)

			string sFileName = par_sFileName.Trim(' ').ToUpper();
			string sResult = "";
			string sTemp = null;
			string sDirection = null;

			//Old, hard-coded solution
			//Select Case (sFileName)
			//    Case "MD", "TN", "XL", "AC", "AP", "EX", "MS", "TD"     '"OP", "PR", "QT", "QL"
			//        If par_bAnswerAsBinary = False Then
			//            Return ("DESCENDING")
			//        Else
			//            Return 1                'REVERSE
			//        End If
			//    Case Else
			//        If par_bAnswerAsBinary = False Then
			//            Return ("ASCENDING")
			//        Else
			//            Return 0                'NORMAL
			//        End If
			//End Select

			//Base the order on the actual sort string from clScripts/cus_clScriptsCustom.
			sTemp = GetDefaultSort(sFileName).Trim(' ');
			sDirection = sTemp.Substring(sTemp.Length - 5).ToUpper();
			if (sDirection == " DESC")
			{
				if (par_bAnswerAsBinary == false)
				{
					return ("DESCENDING");
				}
				else
				{
					return "1"; //REVERSE
				}
			}
			else
			{
				if (par_bAnswerAsBinary == false)
				{
					return ("ASCENDING");
				}
				else
				{
					return "0"; //NORMAL
				}
			}

		}

		public string GetDefaultSort(string par_sFileName, bool par_bReverseDirection = false, string par_sViewType = "LIST")
		{
			//MI 1/29/10 Added par_sViewType.
			//MI 2/1/07 Added cases on DTT fields
			//MI 1/28/07 Added par_bReverseDirection.
			//MI 1/27/07 Changed SYS_Name to SYS_NAME
			//MI 12/10/06 Wrote.

			//*** USE THIS IN SELLSQL. ***
			//PURPOSE:
			//       Returns the default sort order for each file.
			//       KEEP IN SYNC WITH GetDefaultSort! See notes in clScripts.GetDefaultSort.
			//PARAMETERS:
			//       par_sFileName: name of the file.
			//       par_bReverseDirection: default sort with ASC/DESC inverted for each field
			//RETURNS:
			//       String that can be used as a SORT= property value in the filter/sort INI string.

			string sProc = "clData::GetDefaultSort";
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			string sFileName = null;
			string sResult = "";

			// Try
			sFileName = par_sFileName.Trim(' ').ToUpper();
				par_sViewType = par_sViewType.ToUpper();

				//If there is a script GetDefaultSort, it can override the "defaults" coded below
				object tempVar = null;
				object temp_sResult = sResult;
				goScr.RunScript("GetDefaultSort", ref tempVar, null, par_sFileName, goTR.CheckBoxToText(par_bReverseDirection ? -1 : 0, true), par_sViewType, "", "", ref temp_sResult);
					sResult = Convert.ToString(temp_sResult);
				if (sResult != "")
				{
					return sResult;
				}


				switch (par_sViewType)
				{
					case "CHART":
						switch (par_sFileName.ToUpper())
						{
							case "AC":
							case "AP":
								if (par_bReverseDirection)
								{
									sResult = "DTY_STARTTIME DESC"; //, DTM_STARTTIME DESC"
								}
								else
								{
									sResult = "DTY_STARTTIME ASC"; //, DTM_STARTTIME ASC"
								}
								break;
							case "TD":
								if (par_bReverseDirection)
								{
									sResult = "MLS_STATUS ASC"; //, MLS_PRIORITY DESC"
								}
								else
								{
									sResult = "MLS_STATUS DESC"; //, MLS_PRIORITY ASC"
								}
								break;
							case "EX":
							case "MS":
							case "OP":
							case "PR":
							case "QT":
							case "QL":
								//Value transaction files
								if (par_bReverseDirection)
								{
									sResult = "DTY_TIME DESC"; //, DTM_TIME DESC"
								}
								else
								{
									sResult = "DTY_TIME ASC"; //, DTM_TIME ASC"
								}
								break;
							default:
								//List and selection files like CO, CN, PD, etc. Includes system files "MD", "XL", "TN" (Metadata, Log, Translation )
								if (par_bReverseDirection)
								{
									sResult = "DTY_CREATIONTIME DESC"; //, DTM_CREATIONTIME DESC"
								}
								else
								{
									sResult = "DTY_CREATIONTIME ASC"; //, DTM_CREATIONTIME ASC"
								}
								break;
						}
						break;
					default:
						switch (sFileName)
						{
							case "AC":
							case "AP":
							case "EX":
							case "MS":
							case "TD": //, "OP", "PR", "QT", "QL"
								if (par_bReverseDirection)
								{
									sResult = "SYS_NAME ASC";
								}
								else
								{
									sResult = "SYS_NAME DESC";
								}
								break;
							case "MD":
							case "XL":
							case "TN": //Metadata, Log, Translation
								//SYS_Name is blank
								if (par_bReverseDirection)
								{
									sResult = "DTT_CREATIONTIME ASC";
								}
								else
								{
									sResult = "DTT_CREATIONTIME DESC";
								}
								break;
							default: //list and selection files like CO, CN, PD, etc
								//Bu sure this always returns at least one field for any file!
								if (par_bReverseDirection)
								{
									sResult = "SYS_NAME DESC";
								}
								else
								{
									sResult = "SYS_NAME ASC";
								}
								break;
						}
						break;
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try
			return sResult;

		}


		public DataTable GetFRFControlList()
		{
			//WT Created 11/20/14

			//PURPOSE:
			//       Build a datatable of currently registered FRF Control, taken from md, FRF_CONTROLS
			//PARAMETERS:
			//       none
			//RETURNS:
			//       datatable that can be bound to data controls

			string sProc = "clData::GetFRFControlList";
			//goLog.Log(sProc, "Start", clC.SELL_LOGLEVEL_DEBUG, True)

			DataTable oDT = new DataTable();
			oDT.Columns.Add("NAME");
			oDT.Columns.Add("VALUE");

			// Try

			string sMeta = goMeta.PageRead("GLOBAL", "FRF_CONTROLS", "", false, "", true);
				string sFRFCount = goTR.StrRead(sMeta, "FRFCOUNT");
				int iFRFCount = 0;
				if (NumericHelper.IsNumeric(sFRFCount))
				{
					iFRFCount = Convert.ToInt16(sFRFCount);
				}
				for (int i = 1; i <= iFRFCount; i++)
				{
					DataRow oRow = oDT.NewRow();
					oRow["NAME"] = goTR.StrRead(sMeta, "FRF" + i + "NAME");
					oRow["VALUE"] = goTR.StrRead(sMeta, "FRF" + i + "VALUE");
					oDT.Rows.Add(oRow);
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return oDT;

		}



		public object GetDuplicates(string par_sFile, string par_sExcludeID, string par_s1, string par_s2 = "", string par_s3 = "", string par_s4 = "", string par_s5 = "", bool par_bRowset = false)
		{
			//MI 3/18/09 Commented goErr.SetError().
			//CS Need to test. Later should be modified to return the rowset or the filter/sort statement, not filter object.
			//

			//------------------------------/
			//SETERROR						/
			//------------------------------/
			//PURPOSE:
			//		returns a rowset OR A FILTER object contiaing all duplicate (if any)
			//PARAMETERS:
			//		par_sFile:			File name
			//		par_sExcludeID		ID of the record to exclude in the search (ie we don't want to declare a 
			//								duplicate if we find the SAME record)
			//		par_s1	           Content of sParX parameter depends of the FILE:
			//		par_s2:                 CONTACT	==> Last Name/					First Name (3first letters)/	[empty]/[empty]/[empty]
			//		par_s3:                 COMPANY	==> Company Name/				City/							[empty]/[empty]/[empty]
			//		par_s4:                 META	==>	Section/					Page/							[empty]/[empty]/[empty]
			//		par_s5:                 ACTIVITY==>	Not supported
			//								APP 	==>	Not supported
			//								CONFROOM==>	ConfRoom Name/				[empty]/						[empty]/[empty]/[empty]
			//								DELIVERY==>	Delivery Method Name/		[empty]/						[empty]/[empty]/[empty]
			//								DIVISION==>	Division Name/				[empty]/						[empty]/[empty]/[empty]
			//								DOCUMENT==>	Document Name/				[empty]/						[empty]/[empty]/[empty]
			//								EMAILALI==>	Email Address/				[empty]/						[empty]/[empty]/[empty]
			//								EXPACCT	==>	Expense Account Name/		[empty]/						[empty]/[empty]/[empty]
			//								EXPCAT	==>	Expense Category Name/		[empty]/						[empty]/[empty]/[empty]
			//								EXPENSE	==>	Not supported
			//								FileList==>	File Name/					[empty]/						[empty]/[empty]/[empty]
			//								GROUP	==>	Group Name/					[empty]/						[empty]/[empty]/[empty]
			//								INDUSTRY==>	Industry Name/				[empty]/						[empty]/[empty]/[empty]
			//								JOBFUNC	==>	Job Function Name/			[empty]/						[empty]/[empty]/[empty]
			//								LOCATION==>	Location Name				[empty]/						[empty]/[empty]/[empty]
			//								MESSAGE ==>	Not supported				[empty]/						[empty]/[empty]/[empty]
			//								MODEL	==>	Model Name/					[empty]/						[empty]/[empty]/[empty]
			//								OPP 	==>	Not supported
			//								PRODUCT	==>	Product Name/				[empty]/						[empty]/[empty]/[empty]
			//								PROJECT	==>	Project Name/				[empty]/						[empty]/[empty]/[empty]
			//								PUBTERR	==>	Publishing Territory Name/	[empty]/						[empty]/[empty]/[empty]
			//								QUOTE 	==>	Not supported
			//								QUOTLINE==>	Not supported
			//								RELATION==>	Relationship Name/			[empty]/						[empty]/[empty]/[empty]
			//								REPORT	==>	Report Name/				[empty]/						[empty]/[empty]/[empty]
			//								RESOURCE==>	Resource Name/				[empty]/						[empty]/[empty]/[empty]
			//								ROUTING	==>	Routing Name/				[empty]/						[empty]/[empty]/[empty]
			//								SLSTAX	==>	Sales Tax Name/				[empty]/						[empty]/[empty]/[empty]
			//								SOURCE	==>	Source Name/				[empty]/						[empty]/[empty]/[empty]
			//								TERMS	==>	Terms Name/					[empty]/						[empty]/[empty]/[empty]
			//								TERR	==>	Territory Name/				[empty]/						[empty]/[empty]/[empty]
			//								TODO 	==>	Not supported	
			//								USER	==>	Last Name/					First Name(3 first letters)/	[empty]/[empty]/[empty]
			//								VENDOR	==>	Vendor Name/		[empty]/						[empty]/[empty]/[empty]
			//								_IMPLOG	==>	Not supported
			//								_LINK	==> Not supported (could be "Link Type/ID first Record/ID Second Record")
			//								_SESSION==> Not supported
			//								_SYNCLOG==> Not supported
			//								_TRANS	==> Not supported
			//		par_bRowset			If true returns a clRowset object (usage for scripts), if false (default - internal use), returns 
			//							a Filter number
			//RETURNS:
			//		the rowset object, if any, or NULL and a seterror
			//			- OR -
			//		The filter ID, if any, or 0 and a seterror
			//HOW IT WORKS:
			//		Extract all parameters from the INI string, verify them, create the rowset or filter
			//EXAMPLE:
			//		lRowset=goData:GetDuplicates(sFile, "", "AB CD", "EF")

			string sProc = "clData::GetDuplicates";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sFile+" "+par_sExcludeID+" "+par_s1+" "+par_s2+" "+par_s3+" "+par_s4+" "+par_s5, SELL_LOGLEVEL_DEBUG)

			//goLog.SetError()       'MI 3/18/09 commented

			//Extract and verify all information
			string sFile = par_sFile;
			//goP:SellTrace("FH__","FILE: "+sFile, sProc)
			if (!IsFileValid(sFile))
			{
				goLog.SetError(10100, sProc, "", sFile);
				// Internal error: incorrect file name '[1]'. 
				//
				//Please contact Selltis support.
				return (null);
			}

			//Verify it's an authorized file
			switch (sFile)
			{
				case "CO":
				case "CN":
				case "MD":
				case "CR":
				case "DM":
				case "DV":
				case "DO":
				case "EL":
				case "EA":
				case "EC":
				case "FI":
				case "GR":
				case "IU":
				case "JF":
				case "LO":
				case "MO":
				case "PD":
				case "PR":
				case "PT":
				case "RL":
				case "RE":
				case "RS":
				case "RO":
				case "ST":
				case "SO":
				case "TE":
				case "TR":
				case "US":
				case "VE": //OK
				break;

				default:
					goLog.SetError(10103, sProc, "", "FILE", sProc, sFile);
					// Internal error: the value of parameter '[1]' in '[2]' is not supported: '[3]'.
					//
					//Please contact Selltis support.
					return (null);
			}

			string sExcludeID = par_sExcludeID.Trim(' ');
			if (sExcludeID != "")
			{
				if (!goTR.IsTID(sExcludeID))
				{
					goLog.SetError(10129, sProc, "", "any valid ID from file '[1]'", sExcludeID); //MessTranslate 5006
					// Invalid ID. The process was waiting for an ID information and received an invalid one.
					//
					//Please contact your system administrator.
					//
					//ID expected: '[1]'
					//Value received: '[2]'
					//										----- Anglais/English (3) -----
					//										any valid ID from file '[1]'
					//										----- Francais/French (5) -----
					//										n'importe quelle ID valide du fichier '[1]'		
					//										----- Allemand/German (1) -----
					//
					//										----- Espagnol/Spanish (7) -----
					return (null);
				}
				else
				{
					if (goTR.TIDGetFileName(sExcludeID) != sFile)
					{
						goLog.SetError(10129, sProc, "", "any valid ID from file '[1]'", sExcludeID); //MessTranslate 5006
						// Invalid ID. The process was waiting for an ID information and received an invalid one.
						//
						//Please contact your system administrator.
						//
						//ID expected: '[1]'
						//Value received: '[2]'
						//											----- Anglais/English (3) -----
						//											any valid ID from file '[1]'
						//											----- Francais/French (5) -----
						//											n'importe quelle ID valide du fichier '[1]'		
						//											----- Allemand/German (1) -----
						//
						//											----- Espagnol/Spanish (7) -----
						return (null);
					}
				}
			}
			string sPar1 = par_s1;
			string sPar2 = par_s2;
			string sPar3 = par_s3;
			string sPar4 = par_s4;
			string sPar5 = par_s5;

			//Then preapre the content of the new 'shared memory' string, in order to call add rowset

			string sCondition = "";
			//Generic and already coded forIsDuplicate
			string sKeyName = "";
			//Now the parameters depending of the file
			switch (sFile)
			{
				//-----------------------------------------------------------------------------------
				case "CN": //CONTACT==> Last Name/First Name/[empty]/[empty]/[empty]
					//-----------------------------------------------------------------------------------
					sCondition = "_TUP_NAMELAST='" + goTR.UpperTxt(sPar1) + "' AND _TUP_NAMEFIRST[='" + goTR.UpperTxt(sPar2).Substring(0, 3) + "'";
					break;

					//-----------------------------------------------------------------------------------
				case "CO": //COMPANY==> Company Name/City/[empty]/[empty]/[empty]
					//-----------------------------------------------------------------------------------
					//	sCondition="_TUP_NAMELAST='"	+goTr:UpperTXT(sPar1)+"' AND _TUP_NAMEFIRST[='"		+left(goTr:UpperTxt(sPar2),3)+"'"
					sCondition = "_TUP_CompanyName='" + goTR.UpperTxt(sPar1) + "' AND _TUP_CityMailing[='" + goTR.UpperTxt(sPar2).Substring(0, goTR.UpperTxt(sPar2).Length) + "'";
					break;

					//-----------------------------------------------------------------------------------
				case "MD": //_META==> Section/Page/[empty]/[empty]/[empty]
					//-----------------------------------------------------------------------------------
					sCondition = "TXT_Section='" + sPar1.ToUpper() + "' AND TXT_Page='" + sPar2.ToUpper() + "'";
					break;

					//-----------------------------------------------------------------------------------
				case "CR": //==> ConfRoom Name/ [empty]/ [empty]/[empty]/[empty]
					//-----------------------------------------------------------------------------------
					sCondition = "_TUP_ConfRoomName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "DM": //==> Delivery Method Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_DelMethName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "DV": //==> Division Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_DivisionName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "DO": //==> Document Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_DocumentName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "EL": //==> Email Address/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_EmailAddress='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "EA": // ==> Expense Account Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_ExpAcctName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "EC": // ==> Expense Category Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_ExpCatName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "FI": //==> File Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_Filename='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "GR": // ==> Group Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_GroupName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "IU": //==> Industry Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_IndustryName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "JF": // ==> Job Function Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_JobFuncName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "LO": //==> Location Name [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_LocationName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "MO": // ==> Model Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_ModelName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "PD": // ==> Product Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_ProductName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "PR": // ==> Project Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_ProjectName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "PT": // ==> Publishing Territory Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_PubTerrName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "RL": //==> Relationship Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_RelationshipName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "RE": // ==> Report Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_ReportName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "RS": //==> Resource Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_ResourceName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "RO": // ==> Routing Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_RoutingName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "ST": // ==> Sales Tax Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_SalesTaxName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "SO": // ==> Source Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_SourceName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "TR": // ==> Terms Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_TermsName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "TE": // ==> Territory Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_TerrName='" + goTR.UpperTxt(sPar1);
					break;

					//-----------------------------------------------------------------------------------
				case "US": // ==> Last Name/ First Name(3 first letters)/ [empty]/[empty]/[empty]
					sCondition = "_TUP_NameLast='" + goTR.UpperTxt(sPar1) + "' AND _TUP_NAMEFIRST[='" + goTR.UpperTxt(sPar2).Substring(0, 3) + "'";
					break;

					//-----------------------------------------------------------------------------------
				case "VE": // ==> Vendor Name/ [empty]/ [empty]/[empty]/[empty]
					sCondition = "_TUP_VendorName='" + goTR.UpperTxt(sPar1);
					break;

			}

			if (sExcludeID != "")
			{
				sCondition += " AND SYS_ID<>'" + sExcludeID + "'";
			}

			bool bRowset = par_bRowset;
			if (bRowset)
			{
				clRowSet oRowset = new clRowSet(sFile, clC.SELL_EDIT, sCondition);
				//CS: The line below was used...I had to remove the last 2 params to make it work.
				//oRowset = New clRowSet(sFile, clC.SELL_EDIT, sCondition, sKeyName, "")
				if (goLog.GetLastError() != "E00000")
				{
					//delete(oRowset)
					oRowset = null;
					return (null);
				}

				return (oRowset);
			}
			else
			{
				string sFilterMeta;
				sFilterMeta = "NAME=Automatic filter for duplicate checking on " + sFile + "\r\n" + "FILENAME=" + sFile + "\r\n" + "REVERSE=0" + "\r\n" + "KEYNAME=" + sKeyName + "\r\n" + "CONDITION=" + sCondition + "\r\n";

				return (sFilterMeta);

			}
		}
		public string GetFieldDetails(string par_sFile, string par_sField, bool par_bGeeky = true)
		{
			//MI 1/6/10  Added support for link type, combined fields, link fields, <% %> delimiters.
			//MI 10/16/06    Enabled.

			//--------
			//SETERROR
			//--------
			//PURPOSE:
			//		Retrieves a string that contains information about a field including
			//		information stored in the FLD_ metadata. This is for displaying 
			//		field details to the user. clUI:ShowDetails() to do that.
			//PARAMETERS:
			//		par_sFile: System name of the file.
			//		par_sField: System name of the field. Can be a combined field from a view,
			//           and can be delimited with <% and %>.
			//		par_bGeeky: When True (default), includes 'geeky' information. Use this
			//			option in the script editor, for example.
			//RETURNS:
			//		String: Field details or "" if file or field are invalid or an error occurred
			//			with a SetError.

			//For debugging the order of calls.
			string sProc = "clData::GetFieldDetails";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//CS: Comment here to end 

			string sString = null;
			string sInfo = null;
			string sFile = par_sFile;
			string sField = par_sField;

			string sHFType = null;
			string sSize = null;
			string sCaption = null;
			string sFieldLabel = null;
			string sMeta = null;
			string sNotes = null;
			string sDef = null;
			string sFormat = null;
			string sType = null;
			string sLinkDirection = null;
			string sLinkID = null;
			string sLinkFile = null;
			string sLinkField = null;

			if (!IsFileValid(sFile))
			{
				return "Invalid file: '" + sFile + "'.";
			}

			if (goTR.IsFieldCombined(sField, true))
			{
				sString = "Field Label: N/A" + "\r\n" + "System Name: " + sField + "\r\n" + "File: " + sFile + " (" + goData.GetFileLabelFromName(sFile) + ")" + "\r\n" + "Type: combined field" + "\r\n" + "Length: N/A" + "\r\n" + "Format: N/A" + "\r\n" + "System label: N/A" + "\r\n" + "Current label: N/A" + "\r\n" + "Default Value: N/A" + "\r\n" + "Notes: N/A";
				return sString;
			}

			//Remove <% and %> if they are present and extract the field name before LENGTH= or ELLIPSIS= definitions
			sField = goTR.ExtractString(goTR.RemoveDelim(sField), 1, " ");

			if (!IsFieldValid(sFile, sField))
			{
				return "Invalid field: '" + sFile + "." + sField + "'";
			}

			sInfo = goData.GetFieldInfo(sFile, sField);

			//sType = goTR.ExtractString(sInfo, 2)
			//If sType = "T" Then
			//    sType = "Text"
			//Else
			//    sType = "Numeric"
			//End If

			//sFriendlyType = goData.GetFriendlyType(sFile & "." & sField, True)
			if (goTR.GetPrefix(sField) == "LNK_")
			{
				sType = goData.LK_GetType(sFile, goTR.ExtractString(sField, 1, "%%"));
				sLinkDirection = goTR.ExtractString(sType, 2, "\t");
				sLinkID = goTR.ExtractString(sType, 7, "\t");
				sHFType = goData.LKGetType(sFile, sField) + " link (direction " + sLinkDirection + ", ID '" + sLinkID + "')";
				//sHFType = goData.LKGetType(sFile, sField)
				if (sField.IndexOf("%%") + 1 > 0)
				{
					//There is a link field, get info for it
					sInfo = goData.GetFieldInfo(goTR.GetFileFromLinkName(sField), goTR.GetFieldPartFromLinkName(sField));
					sHFType += " " + goTR.ExtractString(sInfo, 4);
				}
			}
			else
			{
				sHFType = goTR.ExtractString(sInfo, 4);
			}
			if (sHFType[0] == clC.EOT)
			{
				sHFType = "";
			}
			sSize = goTR.ExtractString(sInfo, 5);
			if (sSize[0] == clC.EOT)
			{
				sSize = "";
			}
			sCaption = goTR.ExtractString(sInfo, 6);
			if (sCaption[0] == clC.EOT)
			{
				sCaption = "";
			}
			sFormat = goTR.ExtractString(sInfo, 7);
			if (sFormat[0] == clC.EOT)
			{
				sFormat = "";
			}
			if (goTR.GetPrefix(sField) == "LNK_" && (sField.IndexOf("%%") + 1) > 0)
			{
				//Link field defined
				sLinkFile = goTR.GetFileFromLinkName(sField);
				sLinkField = goTR.ExtractString(sField, 2, "%%");
				sFieldLabel = goData.GetFieldLabelFromName(sLinkFile, sLinkField);
				sMeta = goMeta.PageRead("GLOBAL", "FLD_" + sLinkFile);
				sDef = goTR.StrRead(sMeta, sLinkField + "_DEF", "", false);
				sNotes = goTR.StrRead(sMeta, sLinkField + "_NOTES");
			}
			else
			{
				//Link field not defined
				sFieldLabel = goData.GetFieldLabelFromName(sFile, goTR.ExtractString(sField, 1, "%%"));
				sMeta = goMeta.PageRead("GLOBAL", "FLD_" + sFile);
				sDef = goTR.StrRead(sMeta, sField + "_DEF", "", false);
				sNotes = goTR.StrRead(sMeta, sField + "_NOTES");
			}

			//sString=goTr:MessComplete(MessTraduit(5130),...
			//                    '5130 (in NGP):
			//                    'Field Label: [8]
			//                    'System Name: [2]
			//                    'File: [1]
			//                    'Text/numeric: [3]
			//                    'Selltis Type: [4]
			//                    'HyperFile Type: [5]
			//                    'Size: [6]
			//                    'System label: [7]
			//                    'Default Value: [10]
			//                    'Notes: [9]
			sString = "Field Label: " + sFieldLabel + "\r\n" + "System Name: " + sField + "\r\n" + "File: " + sFile + " (" + goData.GetFileLabelFromName(sFile) + ")" + "\r\n" + "Type: " + sHFType + "\r\n" + "Length: " + sSize + "\r\n" + "Format: " + sFormat + "\r\n" + "System label: " + sCaption + "\r\n" + "Current label: " + sFieldLabel + "\r\n" + "Default Value: " + sDef + "\r\n" + "Notes: " + sNotes;

			return sString;

		}
		public string GetFieldInfo(string par_sFileName, string par_sFieldName)
		{
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.
			//CS PORTED as placeholder. Return "".

			//AUTHOR: MI 5/13/03
			//PURPOSE:
			//		Retrive WinDev's tab-delimited information string on the 
			//		field in par_sFieldName.
			//PARAMETERS:
			//		par_sFileName:	Name of the file
			//		par_sFieldName:	Name of the Key
			//RETURNS:
			//		The information string about the key (tab-delimited) or
			//		"" if the key was not found.
			//HOW IT WORKS:
			//		Extract the appropriate returned by HKeyInfo().
			//EXAMPLE:
			//		goData:GetKeyInfo("Company", "CompanyName")

			string sProc = "clData::GetFieldInfo";

			DataRow[] aRowArray = null;
			DataRow[] aItemArray = null;
			DataRow dRow = null;
			int i = 0;
			string sReturn = "";

			try
			{
				//MI 12/29/09 Remove "|AVG", "|SUM", etc the field name
				if (par_sFieldName.IndexOf("|") + 1 > 0)
				{
					par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
				}

				TestSchema();

				aRowArray = goData.dtFields.Select("TableName='" + par_sFileName + "' AND FieldName='" + par_sFieldName + "'");
				dRow = aRowArray[0];
				aItemArray = (DataRow[])dRow.ItemArray;
				for (i = 0; i <= aItemArray.GetUpperBound(0); i++)
				{
					sReturn = sReturn + aItemArray[i] + "\t";
				}
				return sReturn;
			}
			catch (Exception ex)
			{
				return "";
			}


		}

		public int GetFieldDecimals(string par_sFileName, string par_sFieldName)
		{
			//MI 10/8/07 created.
			//PURPOSE:
			//       Returns the no of decimals defined for a CUR, DR_, or SR_ field in FLD_ metadata in
			//       XXX_YYYYYY_FRM= property. If _FRM property is not found, -1 is returned.
			//       If the field is not one of the above types, -1 is returned.
			//RETURNS:
			//       Integer: number of decimals.

			string sProc = "clData::GetFieldDecimals";
			string sTemp = null;
			int iDecimals = 0;

			// Try
			//Enable this block if needed
			//'MI 12/29/09 Parse out "|SUM", "|AVG", etc
			//If InStr(par_sFieldName, "|") > 0 Then
			//    par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|")
			//End If

			sTemp = goTR.ExtractString(goData.GetFieldFormat(par_sFileName, par_sFieldName), 1, "\t");
				if (sTemp == "" || sTemp[0] == clC.EOT)
				{
					iDecimals = -1;
				}
				else
				{
					iDecimals = Convert.ToInt32(goTR.StringToNum(sTemp, "0"));
				}
				//Catch ex As Exception
				//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
				//        goErr.SetError(ex, 45105, sProc)
				//    End If
				//End Try

				return iDecimals;

		}

		public string GetFieldFormat(string par_sFileName, string par_sFieldName, int iPosition = 1)
		{
			//MI 12/29/09 Added support for extended field names for GROUPBY rowset mode, e.g. "CUR_Value|AVG".
			//MI 3/20/08 Added replacing | with vbTab

			string sProc = "clData::GetFieldInfo";

			DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";

			try
			{
				//MI 12/29/09 Remove "|AVG", "|SUM", etc the field name
				if (par_sFieldName.IndexOf("|") + 1 > 0)
				{
					par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
				}

				TestSchema();

				aRowArray = goData.dtFields.Select("TableName='" + par_sFileName + "' AND FieldName='" + par_sFieldName + "'");
				if (aRowArray.GetUpperBound(0) == -1)
				{
					return "";
				}

				dRow = aRowArray[0];
				sReturn = Convert.ToString(dRow["Format"]);
				sReturn = goTR.ExtractString(sReturn, iPosition);
				sReturn = goTR.Replace(sReturn, "|", "\t"); //MI 3/20/08 | is now a supported delimiter, but internally we parse on vbTab.

				return sReturn;
			}
			catch (Exception ex)
			{
				return "";

			}

		}
		public string GetFieldLabel(string par_sFile, string par_sField)
		{
			//MI 7/19/06 Enabled.
			//MI 3/15/11 Optimized performance slightly.

			//---------
			//SET ERROR
			//---------
			//PURPOSE:
			//		Returns the label of a field.
			//PARAMETERS:
			//		par_sFile: system name of the file
			//		par_sField: system name of the field including its prefix
			//RETURNS:
			//		String: label of the field or "" if the file or field are invalid plus raised error.
			//EXAMPLE:
			//		sLabel is string = GetFieldLabel("ACTIVITY","DTE_StartTime")

			string sProc = "clData::GetFieldLabel";
			par_sField = par_sField.ToUpper();

            //if (par_sField.Substring(0, 3) == "LNK")
            if (par_sField.Length >= 3 && par_sField.Substring(0, 3) == "LNK")

            {

                //Return goTR.ExtractString(Me.LK_GetType(par_sFile, par_sField), 6)     'MI 3/15/11 commented
                return LK_GetLabel(par_sFile, par_sField); //MI 3/15/11 added

			}


			return goData.GetFieldLabelFromName(par_sFile, par_sField);
		}

		public string GetFieldFullLabelFromName(string par_sFileName, string par_sFieldName)
		{
			int tempVar = clC.SELL_TYPE_VALID;
			return GetFieldFullLabelFromName(par_sFileName, par_sFieldName, ref tempVar);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Function GetFieldFullLabelFromName(ByVal par_sFileName As String, ByVal par_sFieldName As String, Optional ByRef par_iValid As Integer = clC.SELL_TYPE_VALID) As String
		public string GetFieldFullLabelFromName(string par_sFileName, string par_sFieldName, ref int par_iValid)
		{
			//MI 1/28/10 Created.
			//PURPOSE:
			//   Returns the label of a field or link or link%%field (1st hop only). 
			//   For links with a 1st hop field (e.g. 'LNK_For_PD%%TXT_ProductName'), to get just
			//   the link label returned (e.g. 'For Product'), use GetFieldLabelFromName.
			//PARAMETERS:
			//   par_sFileName:  Name of the file.
			//   par_sFieldName: Name of the field.
			//   par_iValid:     Return parameter that conveys the validity of the field.
			//                   For links, only the validity of the link is tested
			//                   Set to clc.SELL_TYPE_VALID or clc.SELL_TYPE_INVALID.
			//RETURNS:
			//  Label of the field or "" if not found and par_iValid set to clc.SELL_TYPE_INVALID.
			//EXAMPLE:
			//  goData.GetFieldFullLabelFromName("CO","LNK_TeamLeader_US%%TXT_NameLast") returns 'Team Leader (Last Name)'

			string sProc = "clData::GetFieldLabelFromName";

			string sLinkFile = null;
			string sLinkField = null;

			//Try
			if (goTR.GetPrefix(par_sFieldName) == "LNK_")
			{
					if (par_sFieldName.IndexOf("%%") + 1 > 0)
					{
						//Link field defined
						sLinkFile = goTR.GetFileFromLinkName(par_sFieldName);
						sLinkField = goTR.ExtractString(par_sFieldName, 2, "%%");
						return goData.GetFieldLabelFromName(par_sFileName, goTR.ExtractString(par_sFieldName, 1, "%%")) + " (" + GetFieldLabelFromName(sLinkFile, sLinkField, ref par_iValid) + ")";
					}
					else
					{
						//Link field not defined
						return GetFieldLabelFromName(par_sFileName, par_sFieldName, ref par_iValid);
					}
				}
				else
				{
					return GetFieldLabelFromName(par_sFileName, par_sFieldName, ref par_iValid);
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return "";

		}

		public string GetFieldLabelFromName(string par_sFileName, string par_sFieldName)
		{
			int tempVar = clC.SELL_TYPE_VALID;
			return GetFieldLabelFromName(par_sFileName, par_sFieldName, ref tempVar);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Function GetFieldLabelFromName(ByVal par_sFileName As String, ByVal par_sFieldName As String, Optional ByRef par_iValid As Integer = clC.SELL_TYPE_VALID) As String
		public string GetFieldLabelFromName(string par_sFileName, string par_sFieldName, ref int par_iValid)
		{

            if (!string.IsNullOrEmpty(par_sFieldName) && par_sFieldName.ToLower() == "all")
            {
				par_iValid = clC.SELL_TYPE_INVALID;
				return "";
			}

			//MI 1/28/10 Revised comments.
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.
			//MI 10/22/06 Added par_iValid.

			//--------
			//SETERROR
			//--------
			//PURPOSE:
			//  Return the Label of a file field, based on the file and the field names.
			//  For links with a 1st hop field (e.g. 'LNK_For_PD%%TXT_ProductName'), only the link
			//   label will be returned (e.g. 'For Product'). To get the link and field label,
			//   use GetFieldFullLabelFromName instead.
			//PARAMETERS:
			//  par_sFileName: Name of the file
			//  par_sFieldName: Name of the field
			//      if the name of the field is in the format 
			//      FileName.FieldName, then the file name is automatically 
			//      extracted
			//       par_iValid:     Return parameter that conveys the validity of the field.
			//                       Set to clc.SELL_TYPE_VALID or clc.SELL_TYPE_INVALID.
			//RETURNS:
			//  Label of the field or "" if not found and par_iValid set to clc.SELL_TYPE_INVALID.
			//HOW IT WORKS:
			//  Loop on the internal array containing the list of fields
			//EXAMPLE:
			//  goData:GetFieldLabelFromName("COMPANY","COCLEUNIK") returns 'Identifier of COMPANY'
			//  
			string sProc = "clData::GetFieldLabelFromName";

            //MI 12/29/09 Remove "|AVG", "|SUM", etc the field name
            if (!string.IsNullOrWhiteSpace(par_sFieldName) && par_sFieldName.Contains("|"))
            {
				par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
			}
            
            //if (par_sFieldName.ToUpper().Substring(0, 3) == "LNK")
            if (!string.IsNullOrEmpty(par_sFieldName) && par_sFieldName.Length >= 3 && par_sFieldName.ToUpper().Substring(0, 3) == "LNK")

            {
                return LK_GetLabel(par_sFileName, par_sFieldName, ref par_iValid);
			}


			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sFileName+" "+par_sFieldName, SELL_LOGLEVEL_DETAILS)
			string sFileName = par_sFileName.Trim(' ').ToUpper();


			//string sFieldName = par_sFieldName.Trim(' ').ToUpper();
            string sFieldName = string.IsNullOrWhiteSpace(par_sFieldName) ? string.Empty: par_sFieldName.Trim(' ').ToUpper();

            //if (sFieldName.IndexOf(".") + 1 > 0)
            if (!string.IsNullOrWhiteSpace(sFieldName) && sFieldName.Contains("."))
            {
                    sFileName = goTR.ExtractString(sFieldName, 1, ".");
				sFieldName = goTR.ExtractString(sFieldName, 2, ".");
			}

			DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";

			try
			{
				TestSchema();

				aRowArray = goData.dtFields.Select("TableName='" + par_sFileName + "' AND FieldName='" + par_sFieldName + "'");
				if (aRowArray.GetUpperBound(0) == -1)
				{
					par_iValid = clC.SELL_TYPE_INVALID;
					return "";
				}

				dRow = aRowArray[0];

				sReturn = dRow["Label"].ToString();

				par_iValid = clC.SELL_TYPE_VALID;
				return sReturn;
			}
			catch (Exception ex)
			{
				par_iValid = clC.SELL_TYPE_INVALID;
				return "";

			}

		}

		public string GetFieldSimpleType(string par_sFileName, string par_sFieldName)
		{
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.
			//MI 3/13/07 Changed comment.

			//PURPOSE:
			//       In SellSQL this was changed to return the SQl Server type
			//       instead of the simple field type: T)ext or N)umeric, as in NGP.
			//       Use goTr.GetFieldHiLevelType instead if appropriate. 
			//PARAMETERS:
			//		par_sFileName:	Name of the file
			//		par_sFieldName:	Name of the field
			//RETURNS:
			//		SS field type such as smallint, varchar, etc.
			//EXAMPLE:
			//		goData:GetFieldSimpleType("CO","TXT_CompanyName")

			string sProc = "clData::GetFieldSimpleType";
			DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";

			try
			{
				//MI 12/29/09 Remove "|AVG", "|SUM", etc the field name
				if (par_sFieldName.IndexOf("|") + 1 > 0)
				{
					par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
				}

				TestSchema();

				aRowArray = goData.dtFields.Select("TableName='" + par_sFileName + "' AND FieldName='" + par_sFieldName + "'");
				dRow = aRowArray[0];

				sReturn = dRow["SSType"].ToString();

				return sReturn;
			}
			catch (Exception ex)
			{
				return "";

			}
		}
		public string GetFieldSize(string par_sFileName, string par_sFieldName)
		{
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.

			//PURPOSE:
			//		Give back the field size of the Field par_sFieldName in the file FileName
			//PARAMETERS:
			//		par_sFileName:	Name of the file
			//		par_sFieldName:	Name of the field
			//RETURNS:
			//		The field size, in bytes
			//HOW IT WORKS:
			//		--
			//EXAMPLE:
			//		goData:GetFieldSize("COMPANY","TXT_CompanyName")

			string sproc = "clData::GetFieldSize";
            //if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)


            DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";

			try
			{
				//MI 12/29/09 Remove "|AVG", "|SUM", etc the field name
				if (par_sFieldName.IndexOf("|") + 1 > 0)
				{
					par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
				}

				TestSchema();

				aRowArray = goData.dtFields.Select("TableName='" + par_sFileName + "' AND FieldName='" + par_sFieldName + "'");
				dRow = aRowArray[0];

				sReturn = dRow["Length"].ToString();

				return sReturn;
			}
			catch (Exception ex)
			{
				return "";

			}
		}
		public string GetFieldSortValue(string par_sFileName, string par_sFieldName, bool par_bReverse = false)
		{
			//CS PORTED as placeholder. THis shouldn't be needed?

			//PURPOSE:
			//		Returns the sort value (for custom sort order, mainly) of a field
			//PARAMETERS:
			//		par_sFielName:	Name of the file
			//		par_sFieldName:	Name of the field
			//		par_bReverse:	If true, we want the REVERSE sort order
			//RETURNS:
			//		The sort value, as a string
			//HOW IT WORKS:
			//		Depending of the type of field, does the necessary transformations
			//EXAMPLE:
			//		goData:GetFieldSortValue("COMPANY", "TXT_COMPANYNAME")

			string sProc = "clData::GetFieldSortValue";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sFileName+" "+par_sFieldName, SELL_LOGLEVEL_DEBUG)

			return ""; //CS remove

			//        Dim sFile As String = Trim(UCase(par_sFileName))
			//        Dim sField As String = Trim(upper(par_sFieldName))
			//sPrefix is string=goTr:ExtractFieldPrefix(sField)
			//sFieldLine is string
			//sResult is string

			//        switch(sPrefix)
			//case ""		'Counter (CLEUNIK)
			//	sResult=Num�riqueVersChaine({sFile+"."+sField},"018d")
			//case "BIN"	'Binary and other memos
			//        sResult = ""
			//case "CHK"	'Checkbox (internally short int)
			//	sResult=numtostring({sFile+"."+sField})

			//case "CHR"	'Checkbox reverse
			//	sResult=numtostring({sFile+"."+sField})

			//case "CMB"	'Combo box
			//	sResult=numtostring({sFile+"."+sField})

			//case "CUR"	'Currency
			//	sResult=numtostring({sFile+"."+sField},"018,3f")

			//case "CRR"	'Currency Reverse
			//	sResult=Reverse(goTr:NumTostring(-{sFile+"."+sField},"018,3f"))

			//case "DR_"	'Double Real
			//	sResult=numtostring({sFile+"."+sField},"018,3f")

			//case "DRR"	'Double Real Reverse
			//	sResult=Reverse(goTr:NumTostring(-{sFile+"."+sField},"018,3f"))

			//case "DTE"	'Date (Long int)
			//        '    goTr:UTCField_UTCToLocal(sFile, sField)
			//	sResult=complete(goTr:IntegerToDate({sFile+"."+sField}), 8)
			//        '    goTr:UTCField_LocalToUTC(sFile, sField)

			//case "DTR"	'Date Reverse (Long int)
			//	sResult=Reverse(complete(goTr:IntegerToDate(goTr:ReverseDateOrTime({sFile+"."+sField})), 8))

			//case "FIL"	'File
			//	sResult=Complete({sFile+"."+sField},80)

			//case "INT"	'Integer
			//	sResult=Num�riqueVersChaine({sFile+"."+sField},"09d")

			//case "INR", "MLR"	'integer Reverse or MLS reverse (stored in an INT)
			//	sResult=Reverse(complete(goTr:NumToString(goTr:ReverseInt({sFile+"."+sField}), "09d"), 9))

			//case "LI_"	'Long integer
			//	sResult=Num�riqueVersChaine({sFile+"."+sField},"18d")

			//case "LIR"	'Long int Reverse 
			//	sResult=Reverse(complete(goTr:NumToString(goTr:ReverseDateOrTime({sFile+"."+sField}), "18d"), 18))

			//case "LNK"	'Link
			//		if goTr:Position(sField,"%%")>0 then
			//            sFieldLine = "<%" + sField + "%>"
			//        Else
			//            sFieldLine = "<%" + sField + "%%SYS_NAME%>"
			//            End
			//		sResult=goTr:UpperTxt(complete(goTr:GetLineValue(sFieldLine, sFile),80))

			//case "LST"	'Listbox
			//	sResult=Num�riqueVersChaine({sFile+"."+sField},"03d")

			//case "MLS"	'Metadata list (combobox- or listbox-type field automatically filled from a list in _META)
			//	sResult=Num�riqueVersChaine({sFile+"."+sField},"03d")

			//case "MMO"	'Memo
			//	sResult=Complete({sFile+"."+sField},80)

			//case "MMR"	'Rich memo          'MI added 3/28/14
			//	sResult=Complete({sFile+"."+sField},80)

			//case "MUP"	'Uppercase-only, no-accent version of a MMO_ field of same name, 40 chars only
			//	sResult=Complete({sFile+"."+sField},dimension({sFile+"."+sField}))

			//case "MUR"	'Memo uppercase reverse, 40 chars only
			//	sResult=Complete({sFile+"."+sField},dimension({sFile+"."+sField}))

			//case "EML"	'Email Memo
			//	sResult=Complete({sFile+"."+sField},80)

			//case "EUP"	'Uppercase-only, no-accent version of an Email field of same name, 40 chars only
			//	sResult=Complete({sFile+"."+sField},dimension({sFile+"."+sField}))

			//case "EUR"	'Email uppercase reverse, 40 chars only
			//	sResult=Complete({sFile+"."+sField},dimension({sFile+"."+sField}))

			//case "SEL"	'Selector (radio buttons)
			//	sResult=Num�riqueVersChaine({sFile+"."+sField},"03d")

			//case "SI_"	'Short integer
			//	sResult=Num�riqueVersChaine({sFile+"."+sField},"03d")

			//case "SIR"	'Short int Reverse 
			//	sResult=Reverse(complete(goTr:NumToString(goTr:ReverseShortInt({sFile+"."+sField}), "03d"), 3))

			//case "SR_"	'Single Real
			//	sResult=numtostring({sFile+"."+sField},"018,3f")

			//case "SRR"	'Single Real Reverse
			//	sResult=Reverse(goTr:NumTostring(-{sFile+"."+sField},"018,3f"))

			//case "SYS"	'System-generated (virtual), such as 'Name'
			//            sFieldLine = "<%" + sField + "%>"
			//	sResult=complete(goTr:GetLineValue(sFieldLine, sFile),80)

			//case "TEL"	'Telephone
			//	sResult=Complete({sFile+"."+sField},dimension({sFile+"."+sField}))

			//case "TEO"	'Telephone opposite direction
			//	sResult=Complete({sFile+"."+sField},dimension({sFile+"."+sField}))

			//case "TME"	'Time (Long int)
			//            'goP:SellTrace("FH__",goTr:IntegerToTime({sFile+"."+sField}), sProc)
			//            '    goTr:UTCField_UTCToLocal(sFile, sField)
			//	sResult=complete(goTr:IntegerToTime({sFile+"."+sField}), 8)
			//            'goP:SellTrace("FH__","             --> "+sResult, sProc)
			//            '    goTr:UTCField_LocalToUTC(sFile, sField)

			//case "TML"	'Time long (Long int)
			//            '    goTr:UTCField_UTCToLocal(sFile, sField)
			//	sResult=complete(goTr:IntegerToTime({sFile+"."+sField}), 8)
			//            '    goTr:UTCField_LocalToUTC(sFile, sField)

			//case "TMR"	'Time reverse order (long int with value stored as 'max val-date val')
			//	sResult=Reverse(complete(goTr:IntegerToTime(goTr:ReverseDateOrTime({sFile+"."+sField})), 8))

			//case "TXT"	'Text (non-memo)
			//	sResult=Complete({sFile+"."+sField},dimension({sFile+"."+sField}))

			//case "TUP"	'Uppercase-only, no-accent version of a TXT_ field of same name, up to 40 chars
			//	sResult=Complete({sFile+"."+sField},dimension({sFile+"."+sField}))

			//case "TUR"	'Text uppercase-only, reverse, up to 40 chars
			//	sResult=Complete({sFile+"."+sField},dimension({sFile+"."+sField}))

			//case "URL"
			//	sResult=Complete({sFile+"."+sField},80)

			//            End

			//            If par_bReverse Then
			//                sResult = reverse(sResult)
			//                End

			//                result(sResult)
		}
		public string GetFieldType(string par_sFileName, string par_sFieldName)
		{
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.

			//PURPOSE:
			//		Give back the field type of the Field par_sFieldName in the file FileName
			//PARAMETERS:
			//		par_sFileName:	Name of the file
			//		par_sFieldName:	Name of the field
			//RETURNS:
			//		The field type, within the following values:
			//HF field type :
			//	ctText = 2 				
			//	ctInt = 3  				
			//	ctShortInt = 4 			
			//	ctLongInt = 5  			
			//	ctSimpleReal = 6   
			//	ctDoubleReal = 7   
			//	ctRecordNumber = 8 
			//	ctNonSignedInt = 9 
			//	ctDateOn6 = 10     
			//	ctHourType = 11    
			//	ctByte = 12        
			//	ctTurboReal = 13   
			//	ctDateOn8 =	14     
			//	ctTextMemo = 15    
			//	ctOldBinaryMemo = 16
			//	ctCurrency = 17     
			//	ctBinaryMemo= 18    
			//	ctPicture = 21      
			//
			//EXAMPLE:
			//		goData:GetFieldType("COMPANY","TXT_CompanyName")

			string sProc = "clData::GetFieldType";
			DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";

			try
			{
				//MI 12/29/09 Remove "|AVG", "|SUM", etc the field name
				if (par_sFieldName.IndexOf("|") + 1 > 0)
				{
					par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
				}

				TestSchema();

				aRowArray = goData.dtFields.Select("TableName='" + par_sFileName + "' AND FieldName='" + par_sFieldName + "'");
				dRow = aRowArray[0];

				sReturn = dRow["SSType"].ToString();

				return sReturn;
			}
			catch (Exception ex)
			{
				return "";

			}
		}
		public string GetFileLabel(string par_sPrefix)
		{
			//CS OK
			//Replace all code here with a call to goData:GetFileLabelFromName.
			//This method in NGP worked with a file prefix and in SellSQL that is
			//the file name itself.

			//PURPOSE:
			//		Return string containing the Label of the file, based on its prefix 
			//		(in fact the 2 last characters of the string given as a parameter, to 
			//		accomodate prefixes AND TID)
			//PARAMETERS:
			//		par_sPrefix:	Prefix of a file or TID of a record of a file
			//RETURNS:
			//		Name of the file or "" if not found
			//HOW IT WORKS:
			//		Use this method when you need to retrieve the label of a file and you 
			//		only know it's prefix or a TID of one of it's records
			//EXAMPLE:
			//		goData:GetFileLabel("CO") will return 'Company'
			string sProc = "clData::GetFileLabel";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			return GetFileLabelFromName(par_sPrefix);

			//iI is int
			//sResult is string=""

			//       'Give back the index of the file par_sPrefix in the array daFilesDesc
			//sPrefix is string=upper(nospace(right(par_sPrefix,2)))

			//iResult is int=0
			//for iI=1 to dimension(:daFilesDesc)
			//	if nospace(upper(:daFilesDesc[iI]:sFilePrefix))=sPrefix then
			//               iResult = iI
			//               break()
			//               End
			//               End

			//               'If we found the line of the file (Prefix OK)
			//if iResult<>0 then sResult=:daFilesDesc[iI]:sFileLabel

			//               result(sResult)
		}
		public string GetFileLabelFromName(string par_sFileName)
		{

			//PURPOSE:
			//		Return the Label of a file, based on the file name.
			//PARAMETERS:
			//		par_sFileName: Name of the file, e.g. 'AC', 'CO', 'PR'.
			//RETURNS:
			//		Label of the file in singular or "" if not found.
			//EXAMPLE:
			//		goData.GetFileLabelFromName("CO") returns 'Company'.

			string sProc = "clData::GetFileLabelFromName";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";

			try
			{
				TestSchema();

				aRowArray = goData.dtTables.Select("Name='" + par_sFileName + "'");
				dRow = aRowArray[0];

				sReturn = dRow["Label"].ToString();

				return sReturn;
			}
			catch (Exception ex)
			{
				return "";

			}
		}
		public string GetFileLabelPlurFrName(string par_sFileName)
		{

			//PURPOSE:
			//		Return the file label in plural, based on the file name.
			//PARAMETERS:
			//		par_sFileName: Name of the file, e.g. 'AC', 'CO', 'PR'.
			//RETURNS:
			//		Label of the file in plural or "" if not found.
			//EXAMPLE:
			//		goData.GetFileLabelPlurFrName("CO") returns 'Companies'.
			//		
			string sProc = "clData::GetFileLabelPlurFrName";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";

			try
			{
				TestSchema();

				aRowArray = goData.dtTables.Select("Name='" + par_sFileName + "'");
				dRow = aRowArray[0];

				sReturn = dRow["Plural"].ToString();

				return sReturn;
			}
			catch (Exception ex)
			{
			return "";

			}
		}

		public string GetFilePrefix(string par_sFileName)
		{
			//CS OK
			//PORT. Replace code with a simple Return par_sFileName because there are no
			//long file names anymore. File "prefix" means the 2-char abbreviation in NGP.

			//Return string containing the prefix of a file
			string sProc = "clData::GetFilePrefix";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)
			return par_sFileName;


		}

		public string GetPrefix(string par_sFileName)
		{
			//CS OK

			//Return string containing the prefix of a file
			string sProc = "clData::GetPrefix";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			return par_sFileName;
			//iI is int
			//sResult is string=""
			//       'switch :sBaseType 
			//       'case "LHF", "DHF" :' Local and distant Hyper File
			//	iI=:GetFileIndex(par_sFileName)
			//	if iI>0 then sResult=:daFilesDesc[iI]:sFilePrefix
			//       'case "SQLSRV" : 'SQL SERVEUR
			//       'case "ORACLE" : ' ORACLE
			//       'case "ACCESS" : ' ACCESS
			//       'case "SQLANY" :	' SYBASE SQL AnyWhere
			//       'OTHER CASE : 
			//       'END

			//       result(sResult)
		}

		public string GetSelectedRecordID(string par_sFile = "")
		{
				string tempGetSelectedRecordID = null;

			//Returns selected record ID for file from collection if it has been set, "" if it has not been set or on error.
			try
			{
				if (par_sFile == "")
				{
					tempGetSelectedRecordID = LastSelected.SelectedRecordID;
				}
				else
				{
					tempGetSelectedRecordID = collSelectedItem[par_sFile].ToString();
				}

			}
			catch (Exception ex)
			{
				//==> raise error
				tempGetSelectedRecordID = "";
			}

			return tempGetSelectedRecordID;
		}

		public string GetSysNameForGID(string par_sGID)
		{



			//PURPOSE:
			//		For the record with the ID passed, returns the sys_name
			//PARAMETERS:
			//		par_sID: ID of record 
			//RETURNS:
			//		sys_name value if successful, "" if not.
			//AUTHOR: RH

			try
			{

				if (par_sGID == "")
				{
					return "";
				}

				string sFile = goTR.GetFileFromSUID(par_sGID);
				System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

				cmd.CommandText = "SELECT sys_name FROM [" + sFile + "]" + "\r\n" + "WHERE [GID_ID]='" + par_sGID + "'";

				cmd.CommandType = CommandType.Text;
				cmd.Connection = sqlConnection1;
				string sResult = Convert.ToString(cmd.ExecuteScalar());

				sqlConnection1.Close();

				return sResult;


			}
			catch (Exception ex)
			{
				return "";
			}


		}



		public string GetRecordNameByID(string par_sSUID)
		{

			//==> merge with GetSysNameForGID as it is duplicate functionality
			//MI 11/27/06 Added checking file validity
			//MI 10/18/06	Created (ported from NGP)

			//PURPOSE:
			//		Send back the 'Friendly Name' of the record after reading it by its TID.
			//PARAMETERS:
			//		par_sSUID: GID_ID of the record for which we want the friendly name
			//           as string
			//RETURNS:
			//		The SYS_Name value or an empty string if record not found.
			//		Reads the record by it's TID, then call the GetCurrentRecordName function
			//EXAMPLE:
			//		goData.GetRecordNameByID("bla")     'returns ""


			if (par_sSUID == "")
			{
				return "";
			}

			string sProc = "clData::GetRecordNameByID";
			string sFile;

			sFile = goTR.GetFileFromSUID(par_sSUID);
			//*** MI 11/27/06 Added checking file validity
			if (!goData.IsFileValid(sFile))
			{
				goErr.SetError(10100, sProc, "", sFile);
				//10100: Invalid file name '[1]'. [2]
			}

			clRowSet doRS = new clRowSet(sFile, 3, "GID_ID='" + par_sSUID + "'", "", "GID_ID", 1);
			if (doRS.Count() < 1)
			{
				return "";
			}
			else
			{
				return Convert.ToString(doRS.GetFieldVal("SYS_Name"));
			}




		}

		public clArray GetFiles(bool par_bIncludeSysFiles = true)
		{
			//MI 3/18/09 Added par_bIncludeSysFiles.
			//PURPOSE:
			//       Returns a clArray collection of File Names.
			//PARAMETERS:
			//		par_bIncludeSysFiles:   If True, system files like MD, TN, XL
			//           are included (default), else they are not.
			//RETURNS:
			//		Collection
			//EXAMPLE:
			//		Dim aFiles as clArray = goData.GetFiles()
			//ON ERROR:
			//       Returns empty clArray object.

			clArray cFiles = new clArray();
			int i = 0;
			string sName = null;

			//  Try
			TestSchema();

				for (i = 0; i < dtTables.Rows.Count; i++)
				{
					sName = Convert.ToString(dtTables.Rows[i]["Name"]);
					if (!par_bIncludeSysFiles)
					{
						//Skip the system file
						if (IsFileSystem(sName))
						{
							goto GetNextFile;
						}
					}
					cFiles.Add(sName);
	GetNextFile: ;
				}
			//Catch ex As Exception

			//End Try

			return cFiles;



		}

		public clArray GetFilesInSortOrder(bool par_bIncludeSysFiles = true)
		{
			//VT 9/9/15 Added GetFilesInSortOrder.
			//PURPOSE:
			//       Returns a clArray collection of File Names in Alphabetical Order.
			//PARAMETERS:
			//		par_bIncludeSysFiles:   If True, system files like MD, TN, XL
			//           are included (default), else they are not.
			//RETURNS:
			//		Collection
			//EXAMPLE:
			//		Dim aFiles as clArray = goData.GetFiles()
			//ON ERROR:
			//       Returns empty clArray object.

			clArray cFiles = new clArray();
			int i = 0;
			string sName = null;

			//  Try
			TestSchema();

				DataView dv = new DataView(dtTables, "", "[Name] asc", DataViewRowState.CurrentRows);

// INSTANT C# NOTE: The ending condition of VB 'For' loops is tested only on entry to the loop. Instant C# has created a temporary variable in order to use the initial value of dv.ToTable().Rows.Count for every iteration:
				int tempVar = dv.ToTable().Rows.Count;
				for (i = 0; i < tempVar; i++)
				{
					sName = Convert.ToString(dv.ToTable().Rows[i]["Name"]);
					if (!par_bIncludeSysFiles)
					{
						//Skip the system file
						if (IsFileSystem(sName))
						{
							goto GetNextFile;
						}
					}
					cFiles.Add(sName);
	GetNextFile: ;
				}
			//Catch ex As Exception

			//End Try

			return cFiles;



		}

		public clArray GetFields(string par_sFileName)
		{



			clArray cFields = new clArray();
			DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";
			int i = 0;

			try
			{
				TestSchema();

				object oCachedObject = goP.GetVar("GETFIELDS_" + par_sFileName);

				if (oCachedObject.GetType().ToString() == "System.String")
				{
					aRowArray = goData.dtFields.Select("TableName='" + par_sFileName + "'");

					for (i = 0; i <= aRowArray.GetUpperBound(0); i++)
					{
						dRow = aRowArray[i];
						cFields.Add(dRow["FieldName"].ToString());
					}

					goP.SetVar("GETFIELDS_" + par_sFileName, cFields);

					return cFields;
				}
				else
				{
					return (clArray)oCachedObject;

				}


			}
			catch (Exception ex)
			{
				return cFields;

			}

		}
		public clArray GetLinks(string par_sFileName)
		{



			clArray cLinks = new clArray();
			DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";
			int i = 0;

			try
			{
				TestSchema();

				object oCachedObject = goP.GetVar("GETLINKS_" + par_sFileName);

				if (oCachedObject.GetType().ToString() == "System.String")
				{

					aRowArray = goData.dtLinks.Select("From='" + par_sFileName + "'", "FQLinkName Asc");

					for (i = 0; i <= aRowArray.GetUpperBound(0); i++)
					{
						dRow = aRowArray[i];
						cLinks.Add(goTR.ExtractString(dRow["FQLinkName"].ToString(), 2, "."));
					}
					goP.SetVar("GETLINKS_" + par_sFileName, cLinks);

					return cLinks;
				}
				else
				{
					return (clArray)oCachedObject;
				}


			}
			catch (Exception ex)
			{
				return cLinks;

			}





		}

		public void Initialize()
		{

			InitializeClSQL();

			//Commented objects are initialized in clSQL
			//goP = HttpContext.Current.Session("goP")
			//goTR = HttpContext.Current.Session("goTr")
			//goMeta = HttpContext.Current.Session("goMeta")
			//goData = HttpContext.Current.Session("goData")
			//goErr = HttpContext.Current.Session("goErr")
			//goLog = HttpContext.Current.Session("goLog")
			//goDef = HttpContext.Current.Session("goDef")
			goScr = (Selltis.BusinessLogic.clScrMngRowSet)HttpContext.Current.Session["goScr"];
			goUI = (Selltis.BusinessLogic.ClUI)HttpContext.Current.Session["goUI"];
			goList.Initialize();
			//goPerm = HttpContext.Current.Session("goPerm")

			LastSelected.EndDate = goTR.StringToDate(clC.SELL_BLANK_DATETIME);
			LastSelected.StartDate = goTR.StringToDate(clC.SELL_BLANK_DATETIME);

		}


		public bool IsValid(string par_sType, string par_sFile, string par_sString = "")
		{
			//MI 4/27/06 Added comments
			//CS OK, RH review

			//PURPOSE:
			//		Tests the validity of a file, field, and key name.
			//PARAMETERS:
			//		par_sType: Database schema element to be tested. Supported values:
			//			FILE
			//			FIELD
			//			KEY
			//		par_sFile: System name of the file.
			//		par_sString: Name of the schema element to be tested.
			//RETURNS:
			//		True if valid, false if not valid or par_sType is unsupported.
			//EXAMPLE:

			//dim sProc is string=FenEnExecution()+".IsValid"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sType = par_sType.Trim(' ').ToUpper();
			bool bResult = false;

			switch (sType)
			{
				case "FILE":
					if (goData.IsFileValid(par_sFile))
					{
						bResult = true;
					}
					break;
				case "FIELD":
					if (goData.IsFieldValid(par_sFile, par_sString))
					{
						bResult = true;
					}
					break;
				case "KEY":
				break;
					//==> Raise an error: unsupported parameter value
					//CS Commented out b/c goData.IsKeyValid does not exist
					//If goData.IsKeyValid(par_sFile, par_sString) Then bResult = True
				default:
				break;
					//==> Raise an error: unsupported parameter value
			}

			return (bResult);
		}

		public bool IsSUIDFake(string par_sSUID)
		{
			//PURPOSE:
			//       Test whether the SUID was generated with GenSUIDFake().

			string sProc = "clData::IsSUIDFake";

			bool bResult = false;

			if (par_sSUID.Substring(0, 19) == "11111111-1111-1111-" && par_sSUID.Substring(par_sSUID.Length - 13) == "-111111111111")
			{
				bResult = true;
			}

			return bResult;
		}

		public bool IsFieldValid(string par_sFileName, string par_sFieldName)
		{
			//MI 1/7/10 Added BI__GROUPBYCOUNT keyword as valid field. This is not a field, but is
			//       returned as column in GROUPBY rowsets.
			//MI 12/23/09 Added supporting GROUPBY rowset mode calculation expressions like CUR_Value|SUM.
			//MI 11/2/09 Added DTY, DTQ, DTM, DTD.
			//MI 4/27/06 Added support for DTE, TME, TML fields.
			//MI 4/17/06 Fixed a bug with test on LNK not being UCase.
			//CS PORTED as placeholder. Return True for now. 

			//Check the validity of a field and return True/False.
			//
			//PARAMETERS:
			//		par_sFileName: Name of the file
			//		par_sFieldName: Name of the field to check. Cannot be in FILE.FIELD format.

			//Optimised by not logging (used too often)
			//sProc is string="clData::IsFieldValid"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sProc = "clData::IsFieldValid";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sFileName+" "+par_sFieldName, SELL_LOGLEVEL_DETAILS)

			string sFileName = par_sFileName.Trim(' ').ToUpper();

			if (sFileName == "")
			{
				return false;
			}

			string sFieldName = par_sFieldName.Trim(' ').ToUpper();

			//MI 1/7/10 Treat BI__GROUPBYCOUNT as valid 'field' always. THis is a keyword to get
			//the count in GROUPBY and GROUPBYWITHROLLUP modes.
			if (sFieldName == "BI__GROUPBYCOUNT")
			{
				return true;
			}

			if (sFieldName.IndexOf("%%") + 1 > 0)
			{
				sFieldName = goTR.ExtractString(sFieldName, 1, "%%");
			}

			//*** MI 4/27/06: added following section to support DTE, TME, TML fields.
			//MI 11/2/09 Added DTY, DTQ, DTM, DTD.
			//Substitute DTT for DTE, TME, TML, and DTY, DTQ, DTM, DTD prefixes.
			switch (GetPrefix(sFieldName))
			{
				case "DTE_":
				case "TME_":
				case "TML_":
				case "DTY":
				case "DTQ":
				case "DTM":
				case "DTD":
					sFieldName = "DTT_" + goTR.FromTo(sFieldName, 5, -1);
					break;
				case "MMP_":
					sFieldName = "MMR_" + goTR.FromTo(sFieldName, 5, -1);
					break;
				case "ADV_":
					sFieldName = "ADR_" + goTR.FromTo(sFieldName, 5, -1);
					break;
			}

			DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";
			int i = 0;

			try
			{

				string sKey = "";

				if (sFieldName.Substring(0, 3) == "LNK")
				{
					sKey = sFileName + "." + sFieldName;
				}
				else
				{
					sKey = sFileName + "." + sFieldName;
				}

				if (dFields.ContainsKey(sKey))
				{
					return true;
				}

				//MI 12/23/09 Added this to support GROUPBY rowset mode calculation expressions like CUR_Value|SUM
				i = sFieldName.IndexOf("|") + 1;
				if (i > 0)
				{
					sFieldName = goTR.FromTo(sFieldName, 1, i - 1);
				}

				TestSchema();

				if (sFieldName.Substring(0, 3) == "LNK")
				{
					aRowArray = goData.dtLinks.Select("FQLinkName='" + sFileName + "." + sFieldName + "'");
				}
				else
				{
					aRowArray = goData.dtFields.Select("TableName='" + sFileName + "' AND FieldName='" + sFieldName + "'");
				}

				dRow = aRowArray[0];
				dFields.Add(sKey, sKey);

				return true;

				return Convert.ToBoolean(sReturn);
			}
			catch (Exception ex)
			{
				return false;

			}


		}

		public bool IsFieldGroupable(string par_sFieldName, string par_sFileName)
		{
			//MI 12/12/11 Created.
			//PURPOSE:
			//       Determine whether a field or a view column definition is sortable and 
			//       supported for grouping/totaling. This means used in a GROUPBYWITHROLLUP rowset,
			//       which uses GROUP BY ... WITH ROLLUP query, and rollup doesn't return expected
			//       results for DTT fields (SQL bug?). The issue that forced us to stop support DTT
			//       in rollup was a DTT field following one or more DTx fields. A rollup by DTT might
			//       work correctly if there were no DTx fields in the query, but is functionally not
			//       of great valuye (why would one want to roll up a query and total on millisecond
			//       resolution time values?), so we decided not to support it.
			//PARAMETERS:
			//       par_sFieldName: Name of the field or field prefix followed by underscore (for ex TXT_, MMO_, etc)
			//           or a field definition from VIE_ metadata. For ex:
			//               From <%DTE_StartTime%> to <%DTE_EndTime%> by <%LNK_CreatedBy_US%%TXT_Code%>
			//       par_sFileName: File name, ex 'AC', 'CN', or 'MS'. 

			//Dim sProc As String = "cldata::IsFieldGroupable"

			string sPrefix = null;

			//If the field is not sortable, it is not groupable
			//The following tests whether the field is combined, which makes it non-sortable
			//and whether the prefix is unsupported or blank
			if (!IsFieldSortable(par_sFieldName, par_sFileName))
			{
				return false;
			}

			//MI 12/29/09 Parse out "|SUM", "|AVG", etc
			if (par_sFieldName.IndexOf("|") + 1 > 0)
			{
				par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
			}

			//MI 8/20/10 Remove <% %> around the field, if they are first and/or last chars
			par_sFieldName = goTR.RemoveDelim(par_sFieldName);

			sPrefix = goTR.GetPrefix(par_sFieldName);

			//Exclude DTT field
			switch (sPrefix)
			{
				case "DTT_":
					return false;
			}

			return true;

		}

		public bool IsFieldSortable(string par_sFieldName, string par_sFileName)
		{
			//MI 8/21/10 Fixed bug: when field not combined, GetPrefix was failing if field was in <%%>. Added
			//       testing prefixes based on actual support prefixes, testing "" prefixes.
			//MI 5/25/10 Added testing the link field.
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.
			//MI 8/28/09 Created.
			//PURPOSE:
			//       Determine whether a field or a view column definition is sortable via
			//       View>Sort. This method is called by IsFieldGroupable because 
			//       a field must be sortable in order to be supported in a GROUPBYWITHROLLUP
			//       rowset.
			//PARAMETERS:
			//       par_sFieldName: Name of the field or field prefix followed by underscore (for ex TXT_, MMO_, etc)
			//           or a field definition from VIE_ metadata. For ex:
			//               From <%DTE_StartTime%> to <%DTE_EndTime%> by <%LNK_CreatedBy_US%%TXT_Code%>
			//       par_sFileName: File name, ex 'AC', 'CN', or 'MS'. 

			//Dim sProc As String = "cldata::IsFieldSortable"

			string sPrefix = null;
			string sLinkField = null;
			string sPrefixes = null;

			//MI 12/29/09 Parse out "|SUM", "|AVG", etc
			if (par_sFieldName.IndexOf("|") + 1 > 0)
			{
				par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
			}

			//Exclude combined fields
			if (goTR.IsFieldCombined(par_sFieldName))
			{
				return false;
			}

			//_Include_ system fields
			//If goData.IsFieldSystem(par_sFieldName) Then Return False

			//MI 8/20/10 Remove <% %> around the field, if they are first and last chars
			par_sFieldName = goTR.RemoveDelim(par_sFieldName);

			sPrefix = goTR.GetPrefix(par_sFieldName);

			//MI 8/21/10 Invalid prefix
			if (sPrefix == "")
			{
				return false;
			}

			//Exclude DTE and TME fields, memos
			switch (sPrefix)
			{
				case "DTE_":
				case "TME_":
				case "TML_":
				case "MMO_":
				case "MMR_":
				case "FIL_":
				case "BIN_":
				case "URL_":
				case "EML_":
					return false;
			}

			//MI 8/21/10 Added 
			sPrefixes = goDef.GetFieldPrefixes();

			//MI 8/21/10 Exclude invalid prefixes
			if (sPrefixes.IndexOf(goTR.FromTo(sPrefix, 1, 3)) + 1 < 1)
			{
				return false;
			}

			//Exclude n links
			if (goTR.FromTo(goData.LKGetType(par_sFileName, par_sFieldName), 2, 2) == "N")
			{
				return false;
			}

			if (sPrefix == "LNK_")
			{
				//The link is N1, test its field, if any
				if (par_sFieldName.IndexOf("%%") + 1 < 1) //MI 8/21/10
				{
					//MI 8/21/10 No link field found, link is defined by itself. This is evaluated to %%GID_ID, which is sortable.
				}
				else
				{
					sLinkField = goTR.GetFieldPartFromLinkName(par_sFieldName);
					sPrefix = goTR.GetPrefix(sLinkField);
					//MI 8/21/10 Invalid prefix - this would have to be something like "LNK_For_PD%%NotAFieldAtAll"
					//       or "LNK_FOR_PD%%"
					if (sPrefix == "")
					{
						return false;
					}
					//Exclude DTE and TME fields, memos, links
					switch (sPrefix)
					{
						case "DTE_":
						case "TME_":
						case "TML_":
						case "MMO_":
						case "MMR_":
						case "FIL_":
						case "BIN_":
						case "URL_":
						case "EML_":
						case "LNK_":
							return false;
					}
					//MI 8/21/10 Exclude invalid prefixes
					if (sPrefixes.IndexOf(goTR.FromTo(sPrefix, 1, 3)) + 1 < 1)
					{
						return false;
					}
				}
			}

			return true;

		}

		public bool IsFieldReadOnly(string par_sFieldName, string par_sFilename = "")
		{
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.
			//MI 11/5/09 Created.
			//PURPOSE:
			//       Find out whether a field is read-only (non-editable). 
			//       DTY, DTQ, DTR and DTD fields are read-only because they are derived
			//       from DTT on SS level.
			//       In calling code (for example, clROwset.SetFieldVal), raise a warning
			//       like this:
			//       goErr.SetWarning(47173, sProc, , sFieldName, sRSFile, oValue.ToString)
			//PARAMETERS:
			//       par_sFieldName: name of the field, e.g. 'LNK_CreatedBy_US'
			//		par_sFileName: *** Currently unused ***
			//           Optional: name of the file, e.g. 'AC'. This parameter is
			//           optional because several system fields are the same in all files.
			//           However, if there are additional system fields in a particular file,
			//           they will not be identified by this method unless the file name is sent
			//           via this parameter.
			//RETURNS:
			//       True/false.

			//Dim sProc As String = "cldata::IsFieldReadOnly"

			//MI 12/29/09 Parse out "|SUM", "|AVG", etc
			if (par_sFieldName.IndexOf("|") + 1 > 0)
			{
				par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
			}

			par_sFieldName = par_sFieldName.ToUpper();

			//Virtual fields are always read-only
			if (IsFieldVirtualInDB(par_sFieldName, par_sFilename))
			{
				return true;
			}

			//Logical read-only fields (not editable in clRowset.SetFieldVal, for example)
			switch (goTR.GetPrefix(par_sFieldName))
			{
				case "GID":
					if (par_sFieldName == "GID_ID")
					{
						//GID_ID is assigned by SS and must not change
						return true;
					}
					else
					{
						if (par_sFieldName.IndexOf("_", 4) + 1 > 0)
						{
							//N1 link GID_LinkName_ToFile field
							return true;
						}
					}
					break;
			}

			switch (par_sFieldName)
			{
				//Assigned by SS
				case "DTT_CREATIONTIME":
				case "BI__ID":
					return true;
			}

			return false;

		}

		public bool IsFieldVirtualInDB(string par_sFieldName, string par_sFilename = "")
		{
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.
			//MI 11/5/09 Created.
			//PURPOSE:
			//       Find out whether a field is virtual in the database (SS). 
			//       DTY, DTQ, DTR and DTD fields are derived from DTT on SS level.
			//PARAMETERS:
			//       par_sFieldName: name of the field, e.g. 'LNK_CreatedBy_US'
			//		par_sFileName: *** Currently unused ***
			//           Optional: name of the file, e.g. 'AC'. This parameter is
			//           optional because several system fields are the same in all files.
			//           However, if there are additional system fields in a particular file,
			//           they will not be identified by this method unless the file name is sent
			//           via this parameter.
			//RETURNS:
			//       True/false.

			//Dim sProc As String = "cldata::IsFieldVirtualInDB"

			//'MI 12/29/09 Parse out "|SUM", "|AVG", etc
			//If InStr(par_sFieldName, "|") > 0 Then
			//    par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|")
			//End If

			switch (goTR.GetPrefix(par_sFieldName))
			{
				case "DTY":
				case "DTQ":
				case "DTM":
				case "DTD":
					//SS-derived values from DTT fields. These fields are not in the schema.
					return true;
			}

			return false;

		}

		public bool IsFieldSystem(string par_sFieldName, string par_sFilename = "")
		{
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.
			//MI 11/2/09 Returning true for "DTY", "DTQ", "DTM", "DTD".
			//PURPOSE:
			//       Find out whether a field is a "system" (non-editable) field. System fields
			//       are managed by SellSQL "behind the scenes" and are not user-editable.
			//       Some system fields may be editable under certain circumstances, for example
			//       during import (ex: SYS_Name), but not via forms.
			//PARAMETERS:
			//       par_sFieldName: name of the field, e.g. 'LNK_CreatedBy_US'
			//		par_sFileName: *** Currently unused ***
			//           Optional: name of the file, e.g. 'AC'. This parameter is
			//           optional because several system fields are the same in all files.
			//           However, if there are additional system fields in a particular file,
			//           they will not be identified by this method unless the file name is sent
			//           via this parameter.
			//RETURNS:
			//       True/false.

			//Dim sProc As String = "cldata::IsFieldSystem"

			//'MI 12/29/09 Parse out "|SUM", "|AVG", etc
			//If InStr(par_sFieldName, "|") > 0 Then
			//    par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|")
			//End If

			switch (par_sFieldName.ToUpper())
			{
				case "GID_ID":
				case "BI__ID":
				case "SYS_NAME":
				case "TXT_MODBY":
				case "DTT_MODTIME":
				case "DTE_MODTIME":
				case "TME_MODTIME":
				case "DTT_CREATIONTIME":
				case "DTE_CREATIONTIME":
				case "TME_CREATIONTIME":
				case "LNK_CREATEDBY_US":
					return true;
			}

			//Virtual fields are considered 'system'
			if (IsFieldVirtualInDB(par_sFieldName, par_sFilename))
			{
				return true;
			}

			return false;

		}

		public bool IsFileSystem(string par_sFilename)
		{
			//CS ********: Removing AD as sys table due to issues with regular issues being able to read/write to table. Adding as semi-system.
			//V_T 5/6/15 Added "AD" as sys table
			//MI 3/16/09 Added about par_bSysFileFullPermission to notes.
			//PURPOSE:
			//       Find out whether the file (SS table) is a "system" file. System files
			//       are those SellSQL files that need unrestricted access "behind the scenes".
			//       Users are disallowed access to such files except programmatically via
			//       clRowset.New parameter BYVal par_bSysFileFullPermission as boolean = False.
			//       XU and XP are not considered 'files' because they are not returned by
			//       pGetTables. If that should ever change, we are treating them as system files
			//       here.
			//PARAMETERS:
			//		par_sFileName: Name of the file 
			//RETURNS:
			//       Boolean indicating whether a file is a system file or not.

			//Dim sProc As String = "cldata::IsFileSystem"

			switch (par_sFilename.ToUpper())
			{
				case "MD":
				case "TN":
				case "XL":
				case "XU":
				case "XP":
					return true;
				default:
					return false;
			}

		}

		public bool IsFileSemiSystem(string par_sFilename)
		{
			//MI 5/26/11 Created.
			//PURPOSE:
			//       Returns whether the file (SQL table) is a "semi-system" file. Semi-system
			//       files have open permissions (R-A-E-D or R-A-E-d) so that the system 
			//       can read/add/edit them under the user login and linkboxes/linkbox selectors can 
			//       display their records, but the records can't be viewed in views or forms
			//       and can't be edited except through special interfaces such
			//       as POP or WOP custom forms (dialogs) may expose such files (XR, XW).
			//       "Customer admins" don't have permission to change data access perms on
			//       such files in Admin>User/Group Permissions. 
			//PARAMETERS:
			//		par_sFileName: Name of the file 
			//RETURNS:
			//       Boolean indicating whether a file is a semi-system file or not.

			//If doData.IsFileSemiSystem(sFileName) Then 
			//   'restrict access
			//End If

			//CS ******** Added AD as semi-system
			//VT ******** Added MF & MP as semi-system

			switch (par_sFilename.ToUpper())
			{
				case "XR":
				case "XW":
				case "DF":
				case "EL":
				case "FI":
				case "GL":
				case "PS":
				case "RE":
				case "RO":
				case "AD":
				case "XM":
				case "XF": //"MP", "MF"
					//XR=Custom POP, XW= Custom WOP, DF=Data File, EL=E-mail Alias, FI=File, GL=Goal, PS=Phone Alias, RE=Report, RO=Routing
					return true;
				default:
					return false;
			}

		}

		public bool IsFileValid(string par_sFileName)
		{

			//PURPOSE:
			//       Check the validity of a file name return True/False.
			//PARAMETERS:
			//		par_sFileName: Name of the file 

			//No log for optimisation
			//sProc is string="clData::IsFileValid"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//Dim sProc As String = "clData::IsFileValid"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			string sFileName = par_sFileName.Trim(' ').ToUpper();

			DataRow[] aRowArray = null;
			DataRow dRow = null;
			string sReturn = "";

			try
			{
				TestSchema();

				aRowArray = goData.dtTables.Select("Name='" + par_sFileName + "'");
				dRow = aRowArray[0];

				sReturn = true.ToString();
				return Convert.ToBoolean(sReturn);
			}
			catch (Exception ex)
			{
				return false;

			}

		}
		public bool IsModifiable(string par_sFieldName, bool par_bTestCleunik = true)
		{
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.
			//MI 11/2/09 Added "DTY", "DTQ", "DTM", "DTD", added "DTT_MODDATE" to "DTE_MODDATE", "TME_MODTIME".
			//PURPOSE:
			//       Returns whether the field is editable.
			//PARAMETERS:
			//       par_sFieldName
			//       par_bTestCleunik: deprecated NGP notion.

			//USED A LOT... no log for speed optimisation
			//sProc is string="clData::IsModifiable"
			//if gbWriteLog then oLog is clLogObj(sProc, "Start", SELL_LOGLEVEL_DEBUG)

			//'MI 12/29/09 Parse out "|SUM", "|AVG", etc
			//If InStr(par_sFieldName, "|") > 0 Then
			//    par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|")
			//End If

			par_sFieldName = par_sFieldName.Trim(' ').ToUpper();
			string sPrefix = goTR.ExtractFieldPrefix(par_sFieldName);

			switch (sPrefix)
			{
				//Case "STA", "SCR", "TAB", "TBL", "CLK", "EDT", "BTN", "SYS", "CHR", "DTR", "MUP", "MUR", "EUP", "EUR", "TEO", "TMR", "TUP", "TUR", "SIR", "LIR", "INR", "SRR", "DRR", "MLR", "CRR"
				case "SYS":
				case "DTY":
				case "DTQ":
				case "DTM":
				case "DTD":
				case "STA":
				case "SCR":
				case "TAB":
				case "TBL":
				case "CLK":
				case "EDT":
				case "BTN":
					return false;
			}
			//Deprecated NGP notion
			//If par_bTestCleunik Then
			//    If Right(par_sFieldName, 7) = "CLEUNIK" Then
			//        Return (False)
			//    End If
			//End If
			//Remove file name if in the field name (e.g. AC in AC.DTT_StartTime)
			if (par_sFieldName.IndexOf(".") + 1 != 0) //And not the gotr:position for speed (but we test on <>, taking therefore care of possible negative result)
			{
				par_sFieldName = goTR.ExtractString(par_sFieldName, 2, ".");
			}
			//NGP deprecated
			//If Mid(par_sFieldName, 1, 1) = "_" Then par_sFieldName = Mid(par_sFieldName, 2)
			//MI 11/2/09 Added "DTT_MODDATE" to "DTE_MODDATE", "TME_MODTIME".
			switch (par_sFieldName)
			{
				case "TXT_MODBY":
				case "DTT_MODDATE":
				case "DTE_MODDATE":
				case "TME_MODTIME": //NGP: "TXT_IDCREATEDBY", "DTE_IDDATE", "TML_IDTIME", "SI__IDCOUNTER", "INT_IDUAID",
					return false;
			}

			return true;

		}

		public bool IsRecordValid(string par_sID)
		{
			//MI 5/16/08 Added [ ] delimiters.

			//PURPOSE:
			//		Tests whether a record exists in the database
			//PARAMETERS:
			//		par_sID: ID of record to test
			//RETURNS:
			//		True if successful, false if not.
			//AUTHOR: RH

			if (par_sID == "")
			{
				return false;
			}


			string sProc = "clData::IsRecordValid";

			//Try

			string sFile = goTR.GetFileFromSUID(par_sID);
				if (IsFileValid(sFile) == false)
				{
					return false;
				}

				System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

				if (par_sID != "")
				{
					cmd.CommandText = "SELECT COUNT(*)" + "\r\n" + "FROM [" + sFile + "]" + "\r\n" + "WHERE [GID_ID]='" + par_sID + "'";
				}
				else
				{
					//==>raise error 
				}

				cmd.CommandType = System.Data.CommandType.Text;
				cmd.Connection = sqlConnection1;

				if (Convert.ToInt32(cmd.ExecuteScalar()) == 1)
				{
					sqlConnection1.Close();
					return true;
				}
				else
				{
					sqlConnection1.Close();
					//==> set error

					return false;
				}

			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If

			//End Try

		}
		public bool GetRecordPermission(string par_sID, string par_sPermType = "E")
		{

			//PURPOSE:
			//       Find out whether the record is editable or deletable by testing permission for current user
			//PARAMETERS:
			//  par_sID: ID of record
			//  par_sPermType: 'E' for edit or 'D' for delete permission

			string sProc = "cldata::GetRecordPermission";

			// Try

			int iPermission = 0;
				string sFile = goTR.GetFileFromSUID(par_sID);
				iPermission = goPerm.GetSelectivePermission(sFile, par_sPermType);

				switch (iPermission) //permission level: 0 (none), 1 (selective), 2 (full)
				{

					case 0:
						return false;

					case 1:

						int iVal = 0;

						System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

						System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
						Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();


						cmd.CommandText = GenerateSQL("", sFile, "GID_ID", "GID_ID='" + par_sID + "'", "", 1, true, "", "COUNT", "", false, par_sPermType);
						cmd.CommandType = CommandType.Text;
						cmd.Connection = sqlConnection1;

						iVal = Convert.ToInt32(cmd.ExecuteScalar());

						sqlConnection1.Close();

						//MI 2/3/10 Changing If iVal = 1 Then to If iVal >= 1 because a query may return > 1 rec
						//This is because currently the COUNT mode in GenerateSQL counts the rows that the query would return
						//and the same record may be returned in multiple rows due to LEFT JOINs.
						if (iVal >= 1)
						{
							return true;
						}
						else
						{
							return false;
						}
						break;

					case 2:
						return true;

				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}
		public bool GetAddPermission(string par_sFile)
		{

			//PURPOSE:
			//       Find out whether the user has permission to add to the provided file
			//PARAMETERS:
			//  par_sFile: File Name

			string sProc = "cldata::GetAddPermission";

			// Try

			int iPermission;
				iPermission = goPerm.GetSelectivePermission(par_sFile, "A");

				switch (iPermission) //permission level: 0 (none), 1 (selective), 2 (full)
				{

					case 0:
						return false;
					case 1:
						//Selective add is not supported - treat it like 0
						return false;
					case 2:
						return true;

				}


			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}


		public string GetDefaultCalDateTimeFields(string par_sFileName)
		{
			//MI 2/2/07. Renamed to GetDefaultCalDateTimeFields from GetDefaultCalKey.

			//AUTHOR: MI 10/8/03
			//PURPOSE:
			//		Return the default start datetime and, optionally, end datetime fields
			//       by which to display records in a calendar view. If the end datetime field
			//       is not defined, the calendar will show records as having 0 duration, i.e.
			//       the end datetime will be = start datetime.
			//		File name is not validated. if invalid, "DTT_CreationTime" is returned.
			//PARAMETERS:
			//		par_sFileName: name of the view's file .
			//RETURNS:
			//		One field name or two pipe-delimited field names.
			//       If the end datetime field is not defined,
			//       pipe is not returned. Use If goTr.ExtractString(s, 2, "|") = clc.EOT
			//       to test whether the end field is defined.
			//IMPORTANT:
			//		This method is called from goDef.InitViewDefaults(). Keep them in sync.

			string sProc = "clData::GetDefaultCalDateTimeFields";

			string sFileName = par_sFileName.ToUpper();

			switch (sFileName)
			{
				case "AC":
				case "AP":
					return "DTT_StartTime|DTT_EndTime";
				case "PR":
				case "OP":
				case "DO":
				case "EX":
				case "MS":
				case "QT":
					return "DTT_Time";
				case "QL":
					return "DTT_QteTime";
				case "TD":
					return "DTT_DueTime";
				default:
					return "DTT_CreationTime"; //Mandatory field in SellSQL schema
			}

		}

		public bool NullifyInvalidN1Links(string par_sID)
		{
			//MI 5/16/08 Added [ ] delimiters to SQL statement.

			//PURPOSE:
			//		For the record with the ID passed, check vales of all N1 links stored as GID 
			//       fields within that record and Nullify the fields if the link ID is invalid
			//PARAMETERS:
			//		par_sID: ID of record 
			//RETURNS:
			//		True if successful, false if not.
			//AUTHOR: RH

			//Try

			string sFile = goTR.GetFileFromSUID(par_sID);
				clArray aLinks = GetLinks(sFile);
				string sLink = "";
				int i = 0;
				DataTable dt = new DataTable();
				System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				System.Data.SqlClient.SqlDataReader reader = null;
				Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();



				cmd.CommandText = "SELECT * FROM [" + sFile + "]" + "\r\n" + "WHERE [GID_ID]='" + par_sID + "'";

				cmd.CommandType = CommandType.Text;
				cmd.Connection = sqlConnection1;
				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{
					dt.Load(reader);
				}
				else
				{
					//==> raise error
				}

				reader.Close();
				sqlConnection1.Close();

				for (i = 1; i <= aLinks.GetDimension(); i++)
				{
					if (this.LKGetType(sFile, aLinks.GetItem(i)) == "N1")
					{
						sLink = goTR.Replace(aLinks.GetItem(i), "LNK_", "GID_");

						Debug.Print(sLink + "    " + dt.Rows[0][sLink].ToString());

						if (dt.Rows[0][sLink].ToString() != "")
						{
							if (!IsRecordValid(dt.Rows[0][sLink].ToString()))
							{
								UpdateN1LinkRemote(par_sID, sFile, aLinks.GetItem(i), "");
							}
						}

					}
				}


			//Catch ex As Exception



			//End Try





// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}

		public int LKGetDirection(string par_sFileName, string par_sFieldName)
		{
				int tempLKGetDirection = 0;
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.
			//--------
			//SETERROR
			//--------
			//CS PORTED as placeholder

			//PURPOSE:
			//		Send back the direction of the link (1 or 2), depending of the link file/field name.
			//PARAMETERS:
			//		par_sFileName:	Name of the file in which the link starts (the LNK field is in it).
			//		par_sFieldName:	Name of the LNK_ field, i.e. "LNK_Related_CN"
			//RETURNS:
			//		 Integer: 1 for primary direction, 2 for secondary, 0 if error.
			//EXAMPLE:
			//	if goData.LKGetDirection(sFileName, sFieldName) = 1 then
			//       'Active
			//		doForm.SetControlState(sFieldname, 0)
			//	else
			//       'Disabled (Inactive)
			//		doForm.SetControlState(sFieldname, 4)
			//	END

			string sProc = "clData::LKGetDirection";
			//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sFileName+" "+par_sFieldName, SELL_LOGLEVEL_DETAILS)

			try
			{
				string expression = null;

				//MI 12/29/09 Parse out "|SUM", "|AVG", etc
				if (par_sFieldName.IndexOf("|") + 1 > 0)
				{
					par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
				}

				if (par_sFieldName.IndexOf("%%") + 1 > 0)
				{
					par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "%%");
				}

				expression = "FQLinkName = '" + par_sFileName + "." + par_sFieldName + "'";

				TestSchema();

				DataRow[] foundRows;
				foundRows = dtLinks.Select(expression);

				if (foundRows.Length == 1)
				{
					tempLKGetDirection = Convert.ToInt32(foundRows[0]["Direction"]);
				}
				else
				{
					tempLKGetDirection = 0;
					//==>raise error
				}

			}
			catch (Exception ex)
			{
			//==> raise error
			return 0;
			}
			return tempLKGetDirection;
		}

		public string LKGetType(string par_sFileName, string par_sFieldName)
		{
				string tempLKGetType = null;
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.
			//MI 4/19/06 Added UCase in UCase(par_sFileName).
			//AUTHOR: RH     MOD BY MI 3/30/06

			//PURPOSE:
			//		Returns the type of the link: "N1", "1N" or "NN".
			//PARAMETERS:
			//		par_sFileName:	Name of the file (table) in which the link
			//           starts (the LNK field is in it). Ex: 'AC'.
			//		par_sFieldName:	Name of the LNK_ field, e.g. 'LNK_CreatedBy_US'.
			//RETURNS:
			//       String: "N1", "1N", "NN", or "" if an error was detected.
			//EXAMPLE:
			//	if goData.LKGetType(sFileName, sFieldName)="N1" then


			try
			{
				string expression = null;


				//MI 12/29/09 Parse out "|SUM", "|AVG", etc
				if (par_sFieldName.IndexOf("|") + 1 > 0)
				{
					par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
				}

				if (par_sFieldName.IndexOf("%%") + 1 > 0)
				{
					par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "%%");
				}

				expression = "FQLinkName = '" + par_sFileName.ToUpper() + "." + par_sFieldName + "'";

				TestSchema();

				//dtLinks.DefaultView.RowFilter = expression
				//dvTable = dtLinks.DefaultView.ToTable
				DataRow[] foundRows;
				foundRows = dtLinks.Select(expression);

				if (foundRows.Length == 1)
				{
					tempLKGetType = Convert.ToString(foundRows[0]["LinkType"]);
					if (tempLKGetType == "N1" && foundRows[0]["Direction"].ToString() == "2")
					{
						tempLKGetType = "1N";
					}
				}
				else
				{
					tempLKGetType = "";
					//==>raise error
				}

			}
			catch (Exception ex)
			{
			//==> raise error
			return "";
			}

			return tempLKGetType;
		}

		public string LKGetLinkingTableAlias(string par_sLinkingTable, ref dynamic par_aLinkingTableAliases)
		{
			//MI 5/5/09 Created to support aliasing linking tables (AC_INVOLVES_CN, for ex.).

			//PURPOSE:
			//       Get or generate a linking table alias for JOINs in a SQL SELECT statement.
			//       Table aliases are needed in JOINs to support multiple references to
			//       the same linking table. For ex, if a filter is on:
			//           AC.LNK_Related_CO%%LNK_Connected_AC%%GID_ID='...', the table
			//       AC_RELATED_CO will be joined twice. Each of those references must be
			//       uniquely aliased.
			//       Unlike LKGetTableAlias, which attempts to recycle aliases on same 
			//       links, this method always creates a new alias because we don't know
			//       the context in which a linking table is referenced. In some cases, 
			//       this will result in pursuing the same linking table redundantly and
			//       will adversely affect performance.
			//PARAMETERS:
			//       par_sLinkingTable: linking table name or table alias: 'AC_RELATED_CO'
			//           or 'AC_RELATED_CO00002', for ex.
			//       par_aLinkingTableAliases: clArray object that holds linking table aliases. 
			//           If an alias doesn't exist, it is added to this array. This 
			//           parameter is ByRef, therefore modified directly.
			//RETURNS:
			//       String: linking table alias in the format of CN_INVOLVES_CO00001 or "" if
			//           the linking table is invalid.
			//EXAMPLE:
			//       goData.LKGetTableAlias(sFromTableAlias, sLink, aAliases)

			string sProc = ""; //System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sLinkingTable = par_sLinkingTable.ToUpper().Trim(' ');
			string sAliasTable = null;
			string sLinkTable = null;

			//Extract the table name (CN) if sFromTable is a table alias (CN_INVOLVES_CO00002)
			if (goTR.IsNumeric(sLinkingTable.Substring(sLinkingTable.Length - 5)))
			{
				sLinkingTable = goTR.FromTo(sLinkingTable, 1, sLinkingTable.Length - 5);
			}

			if (!IsFieldValid(goTR.FromTo(sLinkingTable, 1, 2), "LNK_" + goTR.FromTo(sLinkingTable, 4, -1)))
			{
				//==> Raise error: invalid table sFromTable
				return "";
			}

			sLinkTable = goTR.ExtractString(sLinkingTable, 3, "_").ToUpper();
			if (sLinkTable == "")
			{
				//==> Raise error: invalid table sLinkTable in link par_sLink
				return "";
			}

			sAliasTable = sLinkingTable + ("00000" + (par_aLinkingTableAliases.GetDimension() + 1)).Substring(("00000" + (par_aLinkingTableAliases.GetDimension() + 1)).Length - 5); //CN_InvolvedWith_CO00001
			//Add the link to the array
			par_aLinkingTableAliases.Add(sLinkingTable.ToUpper()); //CN.LNK_InvolvedWith_CO00001

			return sAliasTable;

		}

		public string LKGetTableAlias(string par_sFromTable, string par_sLink, ref dynamic par_aAliases, bool par_bForceUnique = false)
		{
			//MI 5/6/09 Added par_bForceUnique.
			//MI 4/19/06 Added UCase().
			//MI 4/14/06 Created to be called from GenerateSQL.

			//PURPOSE:
			//       Get or generate a table alias for JOINs in a SQL SELECT statement
			//       for our LNK_ link. Table aliases are needed in JOINs to support multiple
			//       links of the same type between the same pair of tables. See the calling
			//       code in goData.GenerateSQL.
			//PARAMETERS:
			//       par_sFromTable: table name of table alias: 'AC' in 'AC.LNK_Related_CN'
			//           or 'CN00002' in 'CN00002.LNK_Related_CO'. Table aliases must be
			//           managed based on the alias of the 'from' table not to cause mixup
			//           in JOINs.
			//       par_sLink: 'LNK_Related_CN' in 'AC.LNK_Related_CN'.
			//       par_aAliases: clArray object that holds table aliases. If an
			//           alias doesn't exist, it is added to this array. This 
			//           parameter is ByRef, therefore modified directly.
			//       par_bForceUnique: If True, a new alias will be created even if
			//           an existing one is found on the same table. This is necessary
			//           in clSQL.GenerateSQL when generating JOINS for NN links, otherwise
			//           multiple multi-hop links that use the same link would cause 
			//           SQL error 'The correlation name 'US00001' is specified multiple times
			//           in a FROM clause.'
			//RETURNS:
			//       String: table alias in the format of AC00001 or CN00023 or "" if
			//           the from table or link is invalid.
			//EXAMPLE:
			//       goData.LKGetTableAlias(sFromTableAlias, sLink, aAliases)

			string sProc = ""; //System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			string sFromTable = par_sFromTable.ToUpper().Trim(' ');
			int lAlias = 0;
			string sAliasTable = null;
			string sLinkTable = null;

            //Extract the table name (CN) if sFromTable is a table alias (CN00002)
            //if (goTR.IsNumeric(sFromTable.Substring(sFromTable.Length - 5)))
            if (sFromTable.Length >= 5 && goTR.IsNumeric(sFromTable.Substring(sFromTable.Length - 5)))
            {
                sFromTable = goTR.FromTo(sFromTable, 1, sFromTable.Length - 5);
			}
			if (!IsFieldValid(sFromTable, par_sLink))
			{
				//==> Raise error: invalid table sFromTable
				return "";
			}

			sLinkTable = goTR.GetFileFromLinkName(par_sLink).ToUpper(); //*** MI 4/19/06
			if (sLinkTable == "")
			{
				//==> Raise error: invalid table sLinkTable in link par_sLink
				return "";
			}

			if (par_bForceUnique)
			{
				sAliasTable = sLinkTable + ("00000" + (par_aAliases.GetDimension() + 1)).Substring(("00000" + (par_aAliases.GetDimension() + 1)).Length - 5); //CO00001
				//Add the link to the array
				par_aAliases.Add(par_sFromTable.ToUpper() + "." + par_sLink.ToUpper()); //CN.LNK_InvolvedWith_CO
			}
			else
			{
                //lAlias = par_aAliases.Seek(par_sFromTable.ToUpper() + "." + par_sLink.ToUpper(), true); //CN.LNK_InvolvedWith_CO
                lAlias = (int)par_aAliases.Seek(par_sFromTable.ToUpper() + "." + par_sLink.ToUpper(), true);

                if (lAlias == 0)
				{
					sAliasTable = sLinkTable + ("00000" + (par_aAliases.GetDimension() + 1)).Substring(("00000" + (par_aAliases.GetDimension() + 1)).Length - 5); //CO00001
					//Add the link to the array
					par_aAliases.Add(par_sFromTable.ToUpper() + "." + par_sLink.ToUpper()); //CN.LNK_InvolvedWith_CO
				}
				else
				{
					sAliasTable = sLinkTable + ("00000" + lAlias.ToString()).Substring(("00000" + lAlias.ToString()).Length - 5); //CO00001
				}
			}

			return sAliasTable;

		}
		public string LKGetInverseName(string par_sFileName, string par_sFieldName)
		{
				string tempLKGetInverseName = null;
			//MI 12/29/09 Parsing out GROUPBY info like "|SUM" from par_sFieldName.
			//MI 4/12/06 Added UCase in a comparison below.
			//MI OK 3/30/06
			//--------
			//SETERROR
			//--------

			//PURPOSE:
			//		Send back the reverse name of the link:
			//       (Company, LNK_Connected_Activity -> LNK_Related_Company)
			//PARAMETERS:
			//		par_sFileName:	Name of the file (table) in which the link
			//           starts (the LNK field is in it). Ex: 'AC'.
			//		par_sFieldName:	Name of the LNK_ field, ex: 'LNK_CreditedTo_US'.
			//RETURNS:
			//       String: reverse name or "" on error. 
			//EXAMPLE:
			//	sInvName= goData.LKGetInverseName(sFileName, sFieldName)

			try
			{

				string sProc = "clData::LKGetInverseName";
				//if gbWriteLog then oLog is clLogObj(sProc, "Start on "+par_sFileName+" "+par_sFieldName, SELL_LOGLEVEL_DETAILS)

				//MI 12/29/09 Parse out "|SUM", "|AVG", etc
				if (par_sFieldName.IndexOf("|") + 1 > 0)
				{
					par_sFieldName = goTR.ExtractString(par_sFieldName, 1, "|");
				}

				//*** START MI 3/30/06 ***
				string sFieldName = par_sFieldName;

				if (sFieldName.IndexOf("%%") + 1 > 0)
				{
					sFieldName = goTR.ExtractString(sFieldName, 1, "%%").Trim(' ');
				}
				//*** END MI 3/30/06 ***

				string expression = null;
				string sLinkID = "";

				expression = "FQLinkName = '" + par_sFileName + "." + sFieldName + "'"; //*** MI 3/30/06

				TestSchema();

				DataRow[] foundRows;
				foundRows = dtLinks.Select(expression);


				if (foundRows.Length == 1)
				{
					sLinkID = Convert.ToString(foundRows[0]["LinkID"]);
				}
				else
				{
					tempLKGetInverseName = "";
					//==>raise error
				}

				expression = "LinkID='" + sLinkID + "'";

				foundRows = dtLinks.Select(expression);

				if (foundRows.Length == 2)
				{
					//*** MI 4/12/06 added UCase ***
					if (foundRows[0]["FQLinkName"].ToString().ToUpper() == (par_sFileName + "." + par_sFieldName).ToUpper())
					{
						tempLKGetInverseName = goTR.ExtractString(foundRows[1]["FQLinkName"].ToString(), 2, ".");
					}
					else
					{
						tempLKGetInverseName = goTR.ExtractString(foundRows[0]["FQLinkName"].ToString(), 2, ".");
					}
				}
				else
				{
					tempLKGetInverseName = "";
					//==>raise error
				}

			}
			catch (Exception ex)
			{
			//==> raise error
			return "";
			}

			return tempLKGetInverseName;
		}

		public string LK_GetFile(string sFile, string sLinkName)
		{
				string tempLK_GetFile = null;
			//Returns name of table to read links from or write links to

			string expression;
			expression = "FQLinkName = '" + sFile + "." + sLinkName + "'";

			TestSchema();

			DataRow[] foundRows;
			foundRows = dtLinks.Select(expression);


			if (foundRows.Length == 1)
			{

				if (Convert.ToInt32(foundRows[0]["Direction"]) == 1 && Convert.ToString(foundRows[0]["LinkType"]) == "NN")
				{

					tempLK_GetFile = foundRows[0]["From"].ToString() + "_" + foundRows[0]["Link"].ToString() + "_" + foundRows[0]["To"].ToString();

				}
				else if (Convert.ToInt32(foundRows[0]["Direction"]) == 2 && Convert.ToString(foundRows[0]["LinkType"]) == "NN")
				{

					tempLK_GetFile = foundRows[0]["To"].ToString() + "_" + goTR.ExtractString(foundRows[0]["LinkID"].ToString(), 3, ",") + "_" + foundRows[0]["From"].ToString();

				}
				else if (Convert.ToInt32(foundRows[0]["Direction"]) == 1 && Convert.ToString(foundRows[0]["LinkType"]) == "N1")
				{
					tempLK_GetFile = "ROOT";


				}
				else if (Convert.ToInt32(foundRows[0]["Direction"]) == 2 && Convert.ToString(foundRows[0]["LinkType"]) == "N1")
				{
					tempLK_GetFile = Convert.ToString(foundRows[0]["To"]);
				}
				else
				{
					tempLK_GetFile = "";

				}


			}
			else
			{
				tempLK_GetFile = "";
				//==>raise error
			}


			return tempLK_GetFile;
		}
		public string LK_GetType(string sFile, string sLinkName)
		{
				string tempLK_GetType = null;
			//Returns link type: Type TAB Direction

			string expression;

			expression = "FQLinkName = '" + sFile + "." + sLinkName + "'";

			TestSchema();

			DataRow[] foundRows;
			foundRows = dtLinks.Select(expression);


			if (foundRows.Length == 1)
			{
				tempLK_GetType = Convert.ToString(foundRows[0]["Type"]) + "\t" + foundRows[0]["Direction"] + "\t" + foundRows[0]["To"] + "\t" + foundRows[0]["From"] + "\t" + foundRows[0]["LinkType"] + "\t" + foundRows[0]["LinkLabel"] + "\t" + foundRows[0]["LinkID"];
			}
			else
			{
				tempLK_GetType = "";
				//==>raise error
			}

			return tempLK_GetType;
		}

		public string LK_GetLabel(string sFile, string sLinkName)
		{
			int tempVar = clC.SELL_TYPE_VALID;
			return LK_GetLabel(sFile, sLinkName, ref tempVar);
		}

// INSTANT C# NOTE: Overloaded method(s) are created above to convert the following method having optional parameters:
// ORIGINAL LINE: Public Function LK_GetLabel(ByVal sFile As String, ByVal sLinkName As String, Optional ByRef par_iValid As Integer = clC.SELL_TYPE_VALID) As String
		public string LK_GetLabel(string sFile, string sLinkName, ref int par_iValid)
		{
				string tempLK_GetLabel = null;
			//MI 1/28/10 Added par_iValid parameter.
			//PURPOSE:
			//   Returns link label or "" if invalid. Validity of the link is returned
			//   via par_iValid parameter.

			string expression;
			expression = "FQLinkName = '" + sFile + "." + sLinkName + "'";

			TestSchema();

			DataRow[] foundRows;
			foundRows = dtLinks.Select(expression);


			if (foundRows.Length == 1)
			{
				tempLK_GetLabel = Convert.ToString(foundRows[0]["LinkLabel"]);
			}
			else
			{
				par_iValid = clC.SELL_TYPE_INVALID;
				tempLK_GetLabel = "";
				//==>raise error?
			}

			return tempLK_GetLabel;
		}

		public bool LoadLinkData()
		{
				bool tempLoadLinkData = false;

			bool bIsCachable;
			bIsCachable = clCache.IsCachable("pGetLinks");

			if (bIsCachable)
			{
				var retval = clCache.GetItemFromCache("pGetLinks");
				if (retval != null)
				{
					DataTable dt = (DataTable)retval;
					dtLinks.Rows.Clear();
					dtLinks = dt.Copy();
					return true;
				}
			}


			//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

			dtLinks.Rows.Clear();
			goP.DeleteVarsByName("GETLINKS_");

			try
			{
				cmd.CommandText = "pGetLinks";
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{

					dtLinks.Load(reader);
					//dtLinks.PrimaryKey = dtLinks.Columns("FQLinkName")
					//==> set key

				}
				else
				{
					//==> raise error
				}

				if (bIsCachable)
				{
					clCache.AddtoCache("pGetLinks", dtLinks);
				}

				reader.Close();
				sqlConnection1.Close();
				tempLoadLinkData = true;
			}
			catch
			{

				//==> error handle
			}


			return tempLoadLinkData;
		}

		public DataTable GetTNID(string sExtSource, string sFile, string sInternalID = "", string sExternalID = "")
		{


			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();
			DataTable dtIDS = new DataTable();
			string sCommand = "";

			System.Data.SqlClient.SqlParameter par_sExternalSource = new System.Data.SqlClient.SqlParameter("@par_sExternalSource", SqlDbType.VarChar);
			if (sExtSource == "")
			{
				par_sExternalSource.Value = DBNull.Value;
			}
			else
			{
				par_sExternalSource.Value = sExtSource;
			}
			cmd.Parameters.Add(par_sExternalSource);

			if (sInternalID == "")
			{
				sCommand = "pGetTNInternalID";

				System.Data.SqlClient.SqlParameter par_sFile = new System.Data.SqlClient.SqlParameter("@par_sFile", SqlDbType.VarChar);
				par_sFile.Value = sFile;
				cmd.Parameters.Add(par_sFile);

				System.Data.SqlClient.SqlParameter par_sExternalID = new System.Data.SqlClient.SqlParameter("@par_sExternalID", SqlDbType.NVarChar);
				par_sExternalID.Value = sExternalID;
				cmd.Parameters.Add(par_sExternalID);
			}
			else
			{
				sCommand = "pGetTNExternalID";
				System.Data.SqlClient.SqlParameter par_sInternalID = new System.Data.SqlClient.SqlParameter("@par_uInternalID", SqlDbType.UniqueIdentifier);
				par_sInternalID.Value = StringToGuid(sInternalID);
				cmd.Parameters.Add(par_sInternalID);

			}

			// Try
			cmd.CommandText = sCommand;
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{
					dtIDS.Load(reader);
				}
				else
				{
					//==> raise error
				}

				reader.Close();
				sqlConnection1.Close();

			//Catch ex As Exception
			//    '==> error handle
			//End Try

			return dtIDS;


		}
		public int CreateTNRecord(string sTable, string sExtSource, string sInternalID, string sExternalID)
		{

			string sProc = ""; //System.Reflection.MethodBase.GetCurrentMethod().DeclaringType.Name & "::" & System.Reflection.MethodInfo.GetCurrentMethod().Name
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			//PURPOSE:
			//       Create Translation record manually. TN records are created in all
			//       Selltis files by triggers on INSERT and UPDATE.
			//       This calls pCreateTNRecord 
			//       	@par_uInternalID uniqueidentifier,
			//       	@par_sTable varchar(2),
			//       	@par_sExternalID nvarchar(120),
			//       	@par_sExternalSource varchar(10)
			//RETURNS:
			//       Integer: 0 when a TN record was created;
			//           -1 when a duplicate was found (SQL error 2601);
			//           -2 when user ID is not in SQL session info
			//           -3 when user code is not in SQL session info
			//           error number (positive integer)

			int iRet = 0;

			// Try
			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
				string sCommand = "";

				System.Data.SqlClient.SqlParameter par_sExternalSource = new System.Data.SqlClient.SqlParameter("@par_sExternalSource", SqlDbType.VarChar);
				par_sExternalSource.Value = sExtSource;
				cmd.Parameters.Add(par_sExternalSource);

				System.Data.SqlClient.SqlParameter par_uInternalID = new System.Data.SqlClient.SqlParameter("@par_uInternalID", SqlDbType.UniqueIdentifier);
				par_uInternalID.Value = StringToGuid(sInternalID);
				cmd.Parameters.Add(par_uInternalID);

				System.Data.SqlClient.SqlParameter par_sTable = new System.Data.SqlClient.SqlParameter("@par_sTable", SqlDbType.VarChar);
				par_sTable.Value = sTable;
				cmd.Parameters.Add(par_sTable);

				System.Data.SqlClient.SqlParameter par_sExternalID = new System.Data.SqlClient.SqlParameter("@par_sExternalID", SqlDbType.NVarChar);
				par_sExternalID.Value = sExternalID;
				cmd.Parameters.Add(par_sExternalID);

				//return parameter - not used
				System.Data.SqlClient.SqlParameter retValParam = new System.Data.SqlClient.SqlParameter("@RETURN_VALUE", System.Data.SqlDbType.Int);
				retValParam.Direction = System.Data.ParameterDirection.ReturnValue;
				cmd.Parameters.Add(retValParam);

				try
				{

					sCommand = "pCreateTNRecord";
					cmd.CommandText = sCommand;
					cmd.CommandType = CommandType.StoredProcedure;
					cmd.Connection = sqlConnection1;
					cmd.ExecuteNonQuery();
					sqlConnection1.Close();
					//Set the return value to what the sproc actually returns as a result
					iRet = Convert.ToInt32(goTR.StringToNum(retValParam.Value, "0"));
					return iRet;

				}
				catch (System.Data.SqlClient.SqlException ex)
				{
					if (ex.Number == 2601)
					{
						//Duplicate record found error
						sqlConnection1.Close();
						return -1;
					}
					else
					{
						//Another error
						sqlConnection1.Close();
						goErr.SetError(ex, 35000, sProc, "SQL error: " + ex.Message);
					}
				}

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If
			//End Try

			return iRet;

		}
		public bool LoadTableData()
		{
				bool tempLoadTableData = false;

			bool bIsCachable;
			bIsCachable = clCache.IsCachable("pGetTables");

			if (bIsCachable)
			{
				var retval = clCache.GetItemFromCache("pGetTables");
				if (retval != null)
				{
					DataTable dt = (DataTable)retval;
					dtTables.Rows.Clear();
					dtTables = dt.Copy();
					return true;
				}
			}

			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

			dtTables.Rows.Clear();

			try
			{
				cmd.CommandText = "pGetTables";
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{

					dtTables.Load(reader);
					//dtLinks.PrimaryKey = dtLinks.Columns("FQLinkName")
					//==> set key

				}
				else
				{
					//==> raise error
				}

				if (bIsCachable)
				{
					clCache.AddtoCache("pGetTables", dtTables);
				}

				reader.Close();
				sqlConnection1.Close();
				tempLoadTableData = true;
			}
			catch
			{

				//==> error handle
			}


			return tempLoadTableData;
		}

		public bool LoadSchemaData()
		{

			//PURPOSE:
			//		Calls the functions which load schema data into the sessions schema datatables
			//PARAMETERS:
			//	    None
			//RETURNS:
			//       True on success or false if any of the sub methods return false

			if (LoadTableData())
			{
				if (LoadFieldData())
				{
					if (LoadLinkData())
					{
						if (LoadListData())
						{
							goP.SetVar("SCHEMALOADTIME", DateTime.Now);
							return true;
						}
					}
				}
			}
			return false;
		}

		public void TestSchema()
		{


			//PURPOSE:
			//		Tests whether session schema needs to be updated by testing dtApplicationSchemaUpdateFlag
			//       Reloads schema if needed
			//PARAMETERS:
			//		None
			//RETURNS:
			//       Nothing

			return;


			//Dim dtLastSchemaUpdate As DateTime
			//Dim dtApplicationSchemaUpdateFlag As DateTime
			//Dim sRet As String = ""

			//Try
			//    dtLastSchemaUpdate = goP.GetVar("SCHEMALOADTIME")
			//    sRet = HttpContext.Current.Application.Item("dtApplicationSchemaUpdateFlag")
			//    If sRet = Nothing Then
			//        'This must be the first session.. Set to one minute ago as to not cause loop and load into application var
			//        HttpContext.Current.Application.Item("dtApplicationSchemaUpdateFlag") = Now.AddMinutes(-1)
			//        dtApplicationSchemaUpdateFlag = HttpContext.Current.Application.Item("dtApplicationSchemaUpdateFlag")
			//    Else
			//        dtApplicationSchemaUpdateFlag = sRet
			//    End If
			//Catch ex As Exception
			//    LoadSchemaData()
			//End Try

			//If dtLastSchemaUpdate < dtApplicationSchemaUpdateFlag Then LoadSchemaData()





		}

		public bool SetApplicationSchemaUpdateFlag()
		{

			//PURPOSE:
			//		Sets the Application level flag telling session to update schema. 
			//       Call when schema has been modified
			//PARAMETERS:
			//		None
			//RETURNS:
			//       True on success, false on error

			try
			{
				HttpContext.Current.Application["dtApplicationSchemaUpdateFlag"] = DateTime.Now;
				return true;
			}
			catch (Exception ex)
			{
				return false;
			}



		}
		public bool LoadFieldData()
		{
				bool tempLoadFieldData = false;

			bool bIsCachable;
			bIsCachable = clCache.IsCachable("pGetFields");

			if (bIsCachable)
			{
				var retval = clCache.GetItemFromCache("pGetFields");
				if (retval != null)
				{
					DataTable dt = (DataTable)retval;
					dtFields.Rows.Clear();
					goP.DeleteVarsByName("GETFIELDS_");
					dtFields = dt.Copy();
					return true;
				}
			}


			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

			dtFields.Rows.Clear();
			goP.DeleteVarsByName("GETFIELDS_");
			try
			{
				cmd.CommandText = "pGetFields";
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{

					dtFields.Load(reader);
					//dtLinks.PrimaryKey = dtLinks.Columns("FQLinkName")
					//==> set key

					//dtFields.Merge()

				}
				else
				{
					//==> raise error
				}

				if (bIsCachable)
				{
					clCache.AddtoCache("pGetFields", dtFields);
				}

				reader.Close();
				sqlConnection1.Close();
				tempLoadFieldData = true;
			}
			catch
			{

				//==> error handle
			}


			return tempLoadFieldData;
		}

		public string GetCurrentMDVersion()
		{

			var retval = string.Empty;
			bool bIsCachable;

			bIsCachable = clCache.IsCachable("pGetCurrentMDVersion");

			if (bIsCachable)
			{
				retval = Convert.ToString(clCache.GetItemFromCache("pGetCurrentMDVersion"));
				if (string.IsNullOrEmpty(retval) == false)
				{
					return retval;
				}
			}

			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

			DataTable _sData = new DataTable();

			try
			{
				cmd.CommandText = "pGetCurrentMDVersion";
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{
					_sData.Load(reader);
				}
				else
				{
					//==> raise error
				}

				reader.Close();
				sqlConnection1.Close();

				if (_sData.Rows.Count > 0)
				{
					retval = _sData.Rows[0][0].ToString();
					retval = goP.GetVersion("MobileService") + "_" + retval;
					if (bIsCachable)
					{
						clCache.AddtoCache("pGetCurrentMDVersion", retval);
					}
				}
			}
			catch
			{

				//==> error handle
			}

			return retval;
		}

		public DataTable GetNearBy(string pLatitude, string pLongitude, double pRange, string pRangeMeasure, string pFilterName, string pUserId, string pPageId)
		{

			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;
			DataTable dtNearBy = new DataTable();

			// Try
			cmd.CommandText = "pGetNearBy";
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Connection = sqlConnection1;

				System.Data.SqlClient.SqlParameter Param1 = new System.Data.SqlClient.SqlParameter("@Latitude", SqlDbType.NVarChar);
				Param1.Value = pLatitude;
				cmd.Parameters.Add(Param1);

				System.Data.SqlClient.SqlParameter Param2 = new System.Data.SqlClient.SqlParameter("@Longitude", SqlDbType.NVarChar);
				Param2.Value = pLongitude;
				cmd.Parameters.Add(Param2);

				System.Data.SqlClient.SqlParameter Param3 = new System.Data.SqlClient.SqlParameter("@Range", SqlDbType.Float);
				Param3.Value = pRange;
				cmd.Parameters.Add(Param3);

				System.Data.SqlClient.SqlParameter Param4 = new System.Data.SqlClient.SqlParameter("@RangeMeasure", SqlDbType.NVarChar);
				Param4.Value = pRangeMeasure;
				cmd.Parameters.Add(Param4);

				System.Data.SqlClient.SqlParameter Param5 = new System.Data.SqlClient.SqlParameter("@FilterName", SqlDbType.NVarChar);
				Param5.Value = pFilterName;
				cmd.Parameters.Add(Param5);

				System.Data.SqlClient.SqlParameter Param6 = new System.Data.SqlClient.SqlParameter("@UserId", SqlDbType.NVarChar);
				Param6.Value = pUserId;
				cmd.Parameters.Add(Param6);

				System.Data.SqlClient.SqlParameter Param7 = new System.Data.SqlClient.SqlParameter("@PageId", SqlDbType.NVarChar);
				Param7.Value = pPageId;
				cmd.Parameters.Add(Param7);

				reader = cmd.ExecuteReader();

				if (reader.HasRows)
				{
					dtNearBy.Load(reader);
				}

				reader.Close();
				sqlConnection1.Close();

				return dtNearBy;
			//Catch ex As Exception
			//    Throw ex
			//End Try

		}

		public bool LoadListData()
		{
				bool tempLoadListData = false;

			bool bIsCachable;
			bIsCachable = clCache.IsCachable("pGetLists");

			if (bIsCachable)
			{
				var retval = clCache.GetItemFromCache("pGetLists");
				if (retval != null)
				{
					DataSet ds = (DataSet)retval;
					dtLists = ds.Tables[0];
					dtListsExtendedInfo = ds.Tables[1];
					return true;
				}
			}


			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
			DataSet dataset = new DataSet();

			try
			{

				//Dim uResult As New SqlClient.SqlParameter("@par_sLang", SqlDbType.NVarChar, 2)
				//uResult.Value = "US"
				//sqlConnection1.Parameters.Add(uResult)

				System.Data.SqlClient.SqlDataAdapter adapter = new System.Data.SqlClient.SqlDataAdapter("pGetLists", sqlConnection1);

				adapter.Fill(dataset);
				dtLists = dataset.Tables[0];
				dtListsExtendedInfo = dataset.Tables[1];
				sqlConnection1.Close();
				tempLoadListData = true;

				if (bIsCachable)
				{
					clCache.AddtoCache("pGetLists", dataset);
				}

			}
			catch
			{

				//==> error handle
			}


			return tempLoadListData;
		}

		public string GenGuid()
		{
			//Generate a generic GUID. Do not use this as a record ID! Use GenSUID instead.

			Guid guidValue = Guid.NewGuid();
			return guidValue.ToString();

		}
		public string GenSUID(string Table)
		{

			//'Generate a Selltis unique ID. This ID contains the filename in the group before the last
			//'group of values, and is comprised of a portion of a standard SS guid and 6 bytes of the current
			//'datetime.

			//'*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
			//Dim myConnection As SqlClient.SqlConnection = goData.GetConnection

			//Dim myCommand As New Data.SqlClient.SqlCommand("pGenerateSUID", myConnection)
			//myCommand.CommandType = Data.CommandType.StoredProcedure

			//Dim sTable As New SqlClient.SqlParameter("@sTable", SqlDbType.Char)
			//sTable.Value = Table
			//myCommand.Parameters.Add(sTable)

			//'Create a SqlParameter object to hold the output parameter value
			//Dim uResult As New SqlClient.SqlParameter("@uResult", SqlDbType.UniqueIdentifier)

			//'IMPORTANT - must set Direction as Output
			//uResult.Direction = ParameterDirection.Output

			//'Finally, add the parameter to the Command's Parameters collection
			//myCommand.Parameters.Add(uResult)

			//'Call the sproc...
			//Dim reader As SqlClient.SqlDataReader = myCommand.ExecuteReader()

			//'Now you can grab the output parameter's value...
			//Dim GUID As String = Convert.ToString(uResult.Value)

			//GenSUID = GUID
			//myConnection.Close()

			//V_T 10/8/2015
			//to optimize the speed of the GenSUID function , removed the DB call and shifted the stored proc logic to BI
			//Try
			Table = Table.ToUpper();

				byte[] a1 = Encoding.ASCII.GetBytes(Guid.NewGuid().ToString().Substring(0, 8));
				byte[] a2 = Encoding.ASCII.GetBytes(Table.Substring(0, 2));
				byte[] a3 = Encoding.ASCII.GetBytes(DateTime.UtcNow.ToString().Substring(0, 6));

				byte[] a = new byte[(a1.Length + a2.Length + (a3.Length - 1)) + 1];
				System.Buffer.BlockCopy(a1, 0, a, 0, a1.Length);
				System.Buffer.BlockCopy(a2, 0, a, a1.Length, a2.Length);
				System.Buffer.BlockCopy(a3, 0, a, a1.Length + a2.Length, a3.Length);

				Guid sguid = new Guid(a);

				return sguid.ToString();

			//SET @sTableName = UPPER(@sTable) 
			//SET @SUID = CAST(CAST(NEWID() AS BINARY(8)) 
			//+ CAST(@sTableName AS BINARY(2))
			//+ CAST(GetUTCDate() AS BINARY(6)) AS uniqueidentifier) 
			//Catch ex As Exception
			//    Throw ex
			//End Try

		}

		public string GenSUIDAnyFake(string par_sFileName)
		{
			//MI 3/25/10 Added clc constants.
			//MI 3/24/10 Created.
			//PURPOSE:
			//       This is used to ignore a filter condition on a GID_ field. When a view is filtered by
			//       <%SelectedRecordID FILE=xx%> keyword, and the view of file 'xx' has
			//       the '(Any)' line selected (which means 'ignore conditions by selected
			//       record in this view'), this fake GUID will cause clSQL.GenerateSQL to remove
			//       the condition from the filter.
			//       The validity of par_sFileName is not checked. Only the first 2 chars of
			//       this parameter are used as filenames are limited to 2 chars in Selltis.
			string sProc = "clData::GenSUIDAnyFake";

			string sResult = null;
			string sFilename;

			sFilename = goTR.Ascii2Hex(par_sFileName.Substring(0, 2));

			sResult = clC.SELL_SUIDFakePreFile + sFilename + clC.SELL_SUIDAnyPostFile; //"11111111-1111-1111-" & sFilename & "-111111111112"
			return sResult;

		}

		public string GenSUIDFake(string par_sFileName)
		{
			//MI 3/25/10 Added clc constants.
			//PURPOSE:
			//       This is used in filter conditions by GID_ID that must not return any records.
			//       The validity of par_sFileName is not checked. Only the first 2 chars of
			//       this parameter are used as filenames are limited to 2 chars in Selltis.
			string sProc = "clData::GenSUIDFake";

			string sResult = null;
			string sFilename;

			sFilename = goTR.Ascii2Hex(par_sFileName.Substring(0, 2));

			sResult = clC.SELL_SUIDFakePreFile + sFilename + clC.SELL_SUIDFakePostFile; //"11111111-1111-1111-" & sFilename & "-111111111111"

			return sResult;

		}


		public System.Data.SqlClient.SqlConnection GetConnection(int par_iTimeout = 60)
		{
			//MI 11/6/09 Added parameter parTZOffset.
			//MI 3/3/09 Added comment: Must not run goLog.Log because that would cause a loop.
			//MI 2/5/09 Fixed strProduct.Value = line, which was coded as strPage.Value =.
			//MI 1/14/09 Added @par_sProduct parameter filled with gop.GetProduct().
			//MI 5/17/06 Replaced setting ID and usercode from ConfigurationManager.AppSettings
			//           with using goP methods.
			//Estabishes a connection to SQL server.  Calls pInitSession sp to provide user info.
			//IMPORTANT: goP.InitProject must run before this runs the first time, otherwise
			//           this method will set the wrong user information in the SQL Server 
			//           session (see pInitSession sproc in SS).

			string sProc = "goData:GetConnection";
			//MI 3/3/09: Must not run goLog.Log because that would cause a loop.

			System.Data.SqlClient.SqlConnection sqlConnection1 = null;
			try
			{


				string settings = Convert.ToString(HttpContext.Current.Session["Connection"]);
				// Dim iTimeout As Integer = InStr(LCase(settings), "timeout=")

				settings = goTR.Replace(settings, "timeout=x", "timeout=" + par_iTimeout);

				sqlConnection1 = new System.Data.SqlClient.SqlConnection(settings);
				sqlConnection1.Open();

				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

				//Try
				cmd.CommandText = "pInitSession";
					cmd.CommandType = System.Data.CommandType.StoredProcedure;
					cmd.Connection = sqlConnection1;

					System.Data.SqlClient.SqlParameter uSection = new System.Data.SqlClient.SqlParameter("@par_uUserID", SqlDbType.UniqueIdentifier);
					uSection.Value = StringToGuid(goP.GetUserTID()); //*** MI 5/17/06
					//uSection.Value = StringToGuid(goP.GetUserTID())  '*** MI 5/17/06
					cmd.Parameters.Add(uSection);

					System.Data.SqlClient.SqlParameter strPage = new System.Data.SqlClient.SqlParameter("@par_sUserCode", SqlDbType.Char);
					strPage.Value = goP.GetUserCode(); //*** MI 5/17/06
					if (strPage.Value == null)
					{
						goto ExitPoint;
					}

					cmd.Parameters.Add(strPage);

					System.Data.SqlClient.SqlParameter strProduct = new System.Data.SqlClient.SqlParameter("@par_sProduct", SqlDbType.VarChar);
					strProduct.Value = goP.GetProduct(); //*** MI 1/14/09
					cmd.Parameters.Add(strProduct);

					//Set time zone offset in the SQL session if oUserTImeZone has been established.
					//It gets set in goP.InitProject2, but this method is called before that.
					if (goTR.oUserTimeZone != null)
					{
						System.Data.SqlClient.SqlParameter parTZOffset = new System.Data.SqlClient.SqlParameter("@par_sTZOffset", SqlDbType.Char);
						parTZOffset.Value = goTR.UTC_GetUtcOffsetString(goTR.oUserTimeZone.ToLocalTime(goTR.NowUTC()), goTR.oUserTimeZone);
						cmd.Parameters.Add(parTZOffset); //MI 11/6/09
					}

					cmd.ExecuteNonQuery();
				}
				catch (Exception ex1)
				{
					//This fails early in project init as user id has not yet been pulled

				}
			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sProc)
			//    End If

			//End Try
	ExitPoint:
			return sqlConnection1;

		}

		public bool UpdateN1LinkRemote(string sIDRemote, string sFile, string sField, string sIDLinkTo)
		{


			//PURPOSE:
			//		Updates remote side of N1 link
			//PARAMETERS:
			//		sIDRemote: ID of record to update
			//       sFile: File of record to update
			//       sField: Field to update
			//       sIDLinkTo: ID of record to link to
			//RETURNS:
			//		True if successful, errors if not.
			//AUTHOR: RH


			string sProc = "clData::UpdateN1LinkRemote";

			//  Try

			if (sField.Substring(0, 3) == "LNK")
			{
					sField = goTR.Replace(sField, "LNK_", "GID_");
				}

				System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();

				if (sIDLinkTo == "")
				{

					cmd.CommandText = "UPDATE [" + sFile + "]" + "\r\n" + "SET [" + sField + "] = NULL" + "\r\n" + "WHERE [GID_ID] = '" + sIDRemote + "'";
				}
				else
				{
					cmd.CommandText = "UPDATE [" + sFile + "]" + "\r\n" + "SET [" + sField + "] = '" + sIDLinkTo + "'" + "\r\n" + "WHERE [GID_ID] = '" + sIDRemote + "'";

				}

				cmd.CommandType = System.Data.CommandType.Text;
				cmd.Connection = sqlConnection1;

				if (cmd.ExecuteNonQuery() == 1)
				{
					sqlConnection1.Close();
					return true;
				}
				else
				{
					sqlConnection1.Close();

					if (this.IsRecordValid(sIDRemote))
					{
						goErr.SetWarning(46169, sProc, "", sField, sFile, sIDRemote, sIDLinkTo);

						//Unable to update N1 link , '[1]', in file '[2]'.

						//File: '[2]'
						//Record: '[3]'
						//LNK(field) '[1]'
						//LNK(value) '[4]'
					}
					else
					{
						// do nothing allow to continue silently
					}

					return false;
				}


			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If

			//End Try

		}
		public bool DeleteRecord(string sFile, string sGID)
		{

			//PURPOSE:
			//		Deletes the specified record from SQL server.  Does not remove linked records from link tables.
			//PARAMETERS:
			//       sFile: File of record to delete
			//       sGID: ID of record to delete
			//RETURNS:
			//		True if successful, errors if not.
			//AUTHOR: RH


			string sProc = "clData::DeleteRecord";

			// Try

			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();
				System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();



				cmd.CommandText = "DELETE From [" + sFile + "]" + "\r\n" + "WHERE [GID_ID] = '" + sGID + "'";


				cmd.CommandType = System.Data.CommandType.Text;
				cmd.Connection = sqlConnection1;

				if (cmd.ExecuteNonQuery() == 1)
				{
					sqlConnection1.Close();
					return true;
				}
				else
				{
					sqlConnection1.Close();
					//goErr.SetError(46169, sProc, , sField, sFile, sIDRemote, sIDLinkTo)


					return false;
				}


			//Catch ex As Exception

			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45100, sProc)
			//    End If

			//End Try

		}



		public string GenSQLForAddingNNLinks(string sTable, string sFields, string sValues)
		{

			string sproc = "clData::GenSQLForAddingNNLinks";

			//MI 3/3/07 Created.
			//PURPOSE:
			//       Concatenate a dynamic SQL statement that adds one NN link record
			//       This is called from AddNNLink
			//PARAMETERS:
			//       sTable: SS link table in which to write the record. Ex: AC_Related_TE
			//       sFields: comma-delimited list of 3 fields' values: GID_ID, GID_<File1>, GID_<File2>
			//       sValues: comma-delimited list of single-quote-delimited values in the order
			//           of the fields in the sFields parameter.
			//RETURNS:
			//       String: SQL query statement to execute.

			string sResult = "";
			string sField = null;
			string sField1 = "";
			string sField2 = "";
			string sValue1 = "";
			string sValue2 = "";
			int i = 0;
			int iGID = 0;

			//  Try
			sFields = sFields.ToUpper();

				for (i = 1; i <= 3; i++)
				{
					sField = goTR.ExtractString(sFields, i, ",");
					if (sField == "GID_ID")
					{
						iGID = i;
						break;
					}
				}

				switch (iGID)
				{
					case 1:
						sField1 = goTR.ExtractString(sFields, 2, ",").Trim(' ');
						sValue1 = goTR.ExtractString(sValues, 2, ",").Trim(' ');
						sField2 = goTR.ExtractString(sFields, 3, ",").Trim(' ');
						sValue2 = goTR.ExtractString(sValues, 3, ",").Trim(' ');
						break;
					case 2:
						sField1 = goTR.ExtractString(sFields, 1, ",").Trim(' ');
						sValue1 = goTR.ExtractString(sValues, 1, ",").Trim(' ');
						sField2 = goTR.ExtractString(sFields, 3, ",").Trim(' ');
						sValue2 = goTR.ExtractString(sValues, 3, ",").Trim(' ');
						break;
					case 3:
						sField1 = goTR.ExtractString(sFields, 1, ",").Trim(' ');
						sValue1 = goTR.ExtractString(sValues, 1, ",").Trim(' ');
						sField2 = goTR.ExtractString(sFields, 2, ",").Trim(' ');
						sValue2 = goTR.ExtractString(sValues, 2, ",").Trim(' ');
						break;
					default:
						goErr.SetError(35000, sproc, "Field GID_ID not found or found in an unsupported position: '" + iGID + "'. It must be field 1, 2 or 3 only.");
						break;
				}

				if (sField1[0] == clC.EOT || sField1 == "")
				{
					goErr.SetError(35000, sproc, "sField1 is blank or wasn't specified.");
				}
				if (sField2[0] == clC.EOT || sField1 == "")
				{
					goErr.SetError(35000, sproc, "sField2 is blank or wasn't specified.");
				}
				if (sValue1[0] == clC.EOT || sValue1 == "")
				{
					goErr.SetError(35000, sproc, "sValue1 is blank or wasn't specified.");
				}
				if (sValue2[0] == clC.EOT || sValue2 == "")
				{
					goErr.SetError(35000, sproc, "sValue2 is blank or wasn't specified.");
				}

				sResult = "IF NOT EXISTS(SELECT * FROM [" + sTable + "]" + " WHERE [" + sField1 + "] = " + sValue1 + " AND [" + sField2 + "] = " + sValue2 + ")" + "\r\n" + "\t" + "INSERT INTO [" + sTable + "]" + "\r\n" + "\t" + "(" + sFields + ")" + "\r\n" + "\t" + "VALUES (" + sValues + ")";

			//Catch ex As Exception
			//    If Not ex.Message = clC.EX_THREAD_ABORT_MESSAGE Then
			//        goErr.SetError(ex, 45105, sproc)
			//    End If
			//End Try

			return sResult;
		}
		private System.Guid StringToGuid(string sValue)
		{
			System.Guid guidValue = Guid.TryParse(sValue, out guidValue) ? guidValue : Guid.Empty;
            return guidValue;
		}

		public string TempTable()
		{
				string tempTempTable = null;

			//MI 5/16/08 Added [ ] delimiters. 
			//*** MI 5/18/06 Replaced goP.oData.GetConnection with goData.GetConnection
			System.Data.SqlClient.SqlConnection sqlConnection1 = goData.GetConnection();

			System.Data.SqlClient.SqlCommand cmd = new System.Data.SqlClient.SqlCommand();
			System.Data.SqlClient.SqlDataReader reader = null;
			Microsoft.VisualBasic.Collection mColl = new Microsoft.VisualBasic.Collection();

			long lRet = 0;
			string TempTableName = "#" + goTR.Replace(GenGuid(), "-", "");

			string sBigString = null;
			long i = 0;

			sBigString = "";

			for (i = 1; i <= 10000; i++)
			{
				sBigString = sBigString + "BigString ";
			}


			cmd.Connection = sqlConnection1;
			cmd.CommandType = CommandType.Text;

			//cmd.CommandText = "USE tempdb if exists (select * from dbo.sysobjects where id = object_id(N'[" & TempTableName & "]') and OBJECTPROPERTY(id, N'IsUserTable') = 1) drop table [" & TempTableName & "]"
			//lRet = cmd.ExecuteNonQuery

			cmd.CommandText = "USE SalesSelltis " + "CREATE TABLE [" + TempTableName + "] ([ID] int, Text varchar(20), [MMO_Memo] ntext) " + "INSERT INTO [" + TempTableName + "]" + " SELECT 1, 'some text 1', '1 " + sBigString + "' " + "INSERT INTO [" + TempTableName + "]" + " SELECT 2, 'some text 2', '2 " + sBigString + "' " + "INSERT INTO [" + TempTableName + "]" + " SELECT 3, 'some text 3', '3 " + sBigString + "' ";
			lRet = cmd.ExecuteNonQuery();

			cmd.CommandText = "USE SalesSelltis EXEC spTestTempTable  @sTableName = '" + TempTableName + "'";
			reader = cmd.ExecuteReader();

			lRet = 0;
			if (reader.HasRows)
			{
				while (reader.Read())
				{
					lRet = lRet + 1;
				}
			}
			else
			{
				tempTempTable = "0";
			}
			reader.Close();

			cmd.CommandText = "USE TempDB DROP TABLE [" + TempTableName + "]";
			cmd.ExecuteNonQuery();

			tempTempTable = lRet.ToString();

			sqlConnection1.Close();

			return tempTempTable;
		}

		public object GetFieldValueFromRec(string par_sID, string par_sField, int par_iFormat = clC.SELL_FRIENDLY)
		{


			//PURPOSE:
			//		Retrieves a field value for the given ID and field. 
			//PARAMETERS:
			//		par_sID: ID of record
			//       par_sField: Field name
			//       par_iFormat: Data format
			//RETURNS:
			//		Value in requested format if successful, or "" if not.
			//AUTHOR: RH

			string sProc = "clData::GetFieldValueFromRec";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);

			try
			{
				string sFile = goTR.GetFileFromSUID(par_sID);
				clRowSet rs = new clRowSet(sFile, 3, "GID_ID='" + par_sID + "'", "", par_sField);
				if (rs.Count() > 0)
				{
					return rs.GetFieldVal(par_sField, par_iFormat);
				}

			}
			catch (Exception ex)
			{
			return "";
			}

			return "";


		}

		public object GetLinkValueFromRec(string par_sID, string par_sField, int par_iFormat = clC.SELL_FRIENDLY)
		{


			//PURPOSE:
			//		Retrieves a link value for the given ID and field. 
			//PARAMETERS:
			//		par_sID: ID of record
			//       par_sField: Field name
			//       par_iFormat: Data format
			//RETURNS:
			//		Value in requested format if successful, or "" if not.
			//AUTHOR: RH

			string sProc = "clData::GetLinkValueFromRec";
			goLog.Log(sProc, "Start", (short)clC.SELL_LOGLEVEL_DEBUG, true);
			clArray oArray = new clArray();

			try
			{
				string sFile = goTR.GetFileFromSUID(par_sID);
				clRowSet rs = new clRowSet(sFile, 3, "GID_ID='" + par_sID + "'", "", par_sField);
				if (rs.Count() > 0)
				{
					rs.GetLinkVal(par_sField, ref oArray, false, par_iFormat);
					return oArray;
				}

			}
			catch (Exception ex)
			{
				return "";
			}

			//Return ""


// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return null;
		}

		public string CreateExcelUploadSendJob(string sFile, string sSheet, string sRangeStart, string sRangeEnd, string sScript)
		{

			//PURPOSE:
			//		Creates a send job to upload Excel data
			//PARAMETERS:
			//		sFile: Path and file of Excel file or blank for open one
			//       sSheet: Sheetname or blank for active one
			//       sRangeStart: Range start or blank for active one
			//       sRangeEnd: Range end or blank
			//       sScript: Script name of custom script to call upon upload of data
			//RETURNS:
			//		Page name of md page for monitor data
			//AUTHOR: RH

			string sReportMD = "";
			string sPage = "OTH_EXCEL_UPLOAD_" + goData.GenGuid();
			goTR.StrWrite(ref sReportMD, "START", DateTime.Now.ToString());
			goTR.StrWrite(ref sReportMD, "STATUS", "INITIALIZING"); //INITIALIZING, UPLOADING, COMPLETED, ERROR
			goTR.StrWrite(ref sReportMD, "PROGRESS", "0");
			goMeta.PageWrite(goP.GetMe("ID"), sPage, sReportMD, "Excel Upload progress data");


			clSend oSend = new clSend();
			oSend.AddSendJob("Upload Excel request", goP.GetMe("ID"), sFile + "|$" + sSheet + "|$" + sRangeStart + "|$" + sRangeEnd + "|$" + sPage, "REQUEST", "EXCELUPLOAD", "US", false, true, sScript);

			return sPage;

		}

		public void StartStopwatch()
		{
			sw = new Stopwatch();
			sw.Start();

		}
		public void StopStopwatch()
		{
			sw.Stop();

		}

		public bool WriteSplitTime(string sTitle)
		{

			if (Convert.ToString(goP.GetVar("StopWatch")) == "")
			{
				goP.SetVar("StopWatch", sTitle + "  " + sw.ElapsedMilliseconds);
			}
			else
			{
				goP.SetVar("StopWatch", goP.GetVar("StopWatch").ToString() + "<br/>" + sTitle + "  " + sw.ElapsedMilliseconds);
			}

// INSTANT C# NOTE: Inserted the following 'return' since all code paths must return a value in C#:
			return false;
		}


#region XML Helper Functions

		public System.Xml.XmlDocument ReNameRootNode(string sNewNodeName, System.Xml.XmlDocument xmlDoc)
		{

			//PURPOSE:
			//		Changes the name of the Root node of an XML document
			//PARAMETERS:
			//		newNodeName: Name to change the root node too
			//       xmlDoc: XML document that the root node name is being changed
			//RETURNS:
			//		Returnd the XML doc 
			//AUTHOR: CW

			System.Xml.XmlElement newDocElem;
			newDocElem = xmlDoc.CreateElement(sNewNodeName);

			while (xmlDoc.DocumentElement.HasChildNodes)
			{

				newDocElem.AppendChild(xmlDoc.DocumentElement.ChildNodes[0]);

			}

			xmlDoc.RemoveChild(xmlDoc.DocumentElement);
			xmlDoc.AppendChild(newDocElem);

			return xmlDoc;

		}

		public System.Data.DataSet XMLToDataSet(System.Xml.XmlNode xmlNode)
		{

			//PURPOSE:
			//		Convert XML to a Dataset, can convert a whole doc or a specific node and its children 
			//PARAMETERS:
			//		xmlNode: This can be either a node or the entire document, an xml doc can be seen as one entire node
			//RETURNS:
			//		The dataset the xml has been converted into
			//AUTHOR: CW

			System.Data.DataSet ds = new System.Data.DataSet();
			//VS 06082017 : Changed to allow foreign Characters
			//Dim buf As Byte() = System.Text.ASCIIEncoding.ASCII.GetBytes(xmlNode.OuterXml)
			byte[] buf = System.Text.UnicodeEncoding.Unicode.GetBytes(xmlNode.OuterXml);
			System.IO.MemoryStream ms = new System.IO.MemoryStream(buf);
			ds.ReadXml(ms);

			return ds;

		}

		public System.Xml.XmlDocument DataSetToXML(DataSet ds, bool bRenameRootNode = false, string sNewRootName = "")
		{

			//PURPOSE:
			//		Convert a dataset to xml snd optionslly rename the the root  *note if bRenameRootNode = true but sNewRootName is blank the root will not be renamed
			//PARAMETERS:
			//		ds: the dataset to convert
			//       bRenameRootNode: Boolean indicating whether or not to rename the root
			//       sNewRootName: The new root name
			//RETURNS:
			//		the converted dataset with or without the root renamed
			//AUTHOR: CW

			System.Xml.XmlDocument xmlReturn = new System.Xml.XmlDocument();
			xmlReturn.LoadXml(ds.GetXml());

			if (bRenameRootNode == true && sNewRootName != "")
			{

				return ReNameRootNode(sNewRootName, xmlReturn);

			}
			else
			{

				return xmlReturn;

			}

		}

#endregion

		~clData()
		{
// INSTANT C# NOTE: The base class Finalize method is automatically called from the destructor:
			//base.Finalize();
		}


	}


}
