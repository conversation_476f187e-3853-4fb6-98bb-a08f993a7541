﻿@using System.Data;
@using System.Data.SqlClient;
@using Kendo.Mvc.UI;
@using Selltis.MVC.Models;
@using Selltis.MVC.Controllers;
@using Selltis.BusinessLogic;
@using Selltis.Core;

@{
    if (Selltis.Core.Util.GetSessionValue("CompanyName") == null)
    {
        Response.Redirect("~/Login/Logout");
    }
}

<!DOCTYPE html>
<html>
<head>
    <!-- Meta, title, CSS, favicons, etc. -->
    <meta charset="utf-8">
    <title>Selltis Sales - @Selltis.Core.Util.GetSessionValue("CompanyName")</title>
    <link type="image/x-icon" href="~/Content/Images/Selltis16.ico" rel="shortcut icon" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Font CSS (Via CDN) -->
    <link rel='stylesheet' type='text/css' href='https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700'>

    <!-- Theme CSS -->
    <link href="@Url.Content("~/Content/themes/Selltis/css/theme.css?v=3.1")" rel="stylesheet" type="text/css" />

    <link href="~/Content/themes/Selltis/css/2023/kendo.default-v2.min.css" rel="stylesheet" />


    <script src="https://kit.fontawesome.com/6deb58cc34.js" crossorigin="anonymous"></script>

    <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?libraries=places&key=AIzaSyBeOYq42sBcMy8huOaV6-aaifKoXrhjRvs" async defer></script>

    <style>

        .k-grid a {
            color: #3498db;
            text-decoration: none;
        }

        .k-chip-label, .k-chip-text {
            white-space: pre-wrap;
            text-overflow: ellipsis;
            overflow: hidden;
            -ms-flex: 1 1 auto;
            flex: 1 1 auto;
        }

        .k-chip-solid-base.k-hover, .k-chip-solid-base:hover {
            background-color: #fff;
        }

        .k-chip-solid-base {
            border-color: #428bca !important; /*#c6c6c6;*/
            color: #424242;
            background-color: #fff;
        }

        .k-combobox .k-dropdown-wrap {
            height: 27px;
        }

            .k-combobox .k-dropdown-wrap .k-input {
                height: 27px !important;
            }

        .k-icon.k-clear-value.k-i-close {
            display: none !important;
        }

        .k-clear-value {
            display: none !important;
        }
        /*Specific to Kendo DateTime pickers list..J*/
        .k-picker-wrap.k-state-default {
            height: 27px !important;
        }

        /* .k-grid td.k-selected, .k-grid tr.k-selected > td {
            color: #fff !important;
            background-color: #428bca !important;
            border-color: #428bca !important;
        }*/

        .k-input-inner {
            border: none !important;
            border-radius: 0px 0px !important;
            box-shadow: none !important;
        }


        .k-draghandle.k-selected:hover, .k-ghost-splitbar-horizontal, .k-ghost-splitbar-vertical, .k-list > .k-state-highlight, .k-list > .k-selected, .k-marquee-color, .k-panel > .k-selected, .k-scheduler .k-scheduler-toolbar .k-selected, .k-scheduler .k-today.k-selected, .k-selected, .k-selected:link, .k-selected:visited {
            color: #fff !important;
            background-color: #428bca !important;
            border-color: #428bca !important;
        }
        /* .k-draghandle.k-state-selected:hover, .k-ghost-splitbar-horizontal, .k-ghost-splitbar-vertical, .k-list > .k-state-highlight, .k-list > .k-state-selected, .k-marquee-color, .k-panel > .k-state-selected, .k-scheduler .k-scheduler-toolbar .k-state-selected, .k-scheduler .k-today.k-state-selected, .k-state-selected, .k-state-selected:link, .k-state-selected:visited {
            color: #fff !important;
            background-color: #428bca !important;
            border-color: #428bca !important;
        }*/

        body.sb-l-m #content_wrapper {
            margin-left: 60px !important;
        }

        #content_wrapper {
            padding-top: 60px !important;
        }

        .navbar-branding {
            /*width: 10% !important;*/
            width: 200px !important;
        }

        body.sb-l-m #sidebar_left {
            z-index: 1028 !important;
            overflow: visible !important;
            width: 60px !important;
            height: 100% !important;
            left: 0 !important;
            background-color: #4c4a49 !important;
            margin-top: 10px;
        }

        body.sb-l-m .navbar-branding {
            width: 60px !important;
        }

        .fulldiv {
            height: 97% !important;
            min-height: 90% !important;
            display: block;
            border: none !important;
            padding-bottom: 0px !important;
            overflow-y: scroll;
        }

        .fulldivp {
            height: 100% !important;
            min-height: 100% !important;
            display: block;
            border: none !important;
        }

        .fulldivpscroll {
            height: calc(100%) !important;
            min-height: calc(100%) !important;
            display: block;
            border: none !important;
        }

        .fulldivp2 {
            height: calc(50% - 5px) !important;
            min-height: calc(50% - 5px) !important;
            display: block;
            border: none !important;
        }

        .fulldivp3 {
            height: calc(33.33% - 5px) !important;
            min-height: calc(33.33% - 5px) !important;
            display: block;
            border: none !important;
        }

        .fulldivp4 {
            height: calc(25% - 5px) !important;
            min-height: calc(25% - 5px) !important;
            display: block;
            border: none !important;
        }

        .fulldivp5 {
            height: calc(20% - 5px) !important;
            min-height: calc(20% - 5px) !important;
            display: block;
            border: none !important;
        }

        .fulldivp6 {
            height: calc(16.67% - 5px) !important;
            min-height: calc(16.67% - 5px) !important;
            display: block;
            border: none !important;
        }

        .fulldiv30 {
            /* Firefox */
            height: -moz-calc(100% - 30px) !important;
            /* WebKit */
            height: -webkit-calc(100% - 30px) !important;
            /* Opera */
            height: -o-calc(100% - 30px) !important;
            /* Standard */
            height: calc(100% - 30px) !important;
            display: block;
            border: none !important;
        }

        .fulldiv40 {
            /* Firefox */
            height: -moz-calc(100% - 40px) !important;
            /* WebKit */
            height: -webkit-calc(100% - 40px) !important;
            /* Opera */
            height: -o-calc(100% - 40px) !important;
            /* Standard */
            height: calc(100% - 40px) !important;
            display: block;
            border: none !important;
        }


        .fulldiv35 {
            /* Firefox */
            height: -moz-calc(100% - 35px) !important;
            /* WebKit */
            height: -webkit-calc(100% - 35px) !important;
            /* Opera */
            height: -o-calc(100% - 35px) !important;
            /* Standard */
            height: calc(100% - 35px) !important;
            display: block;
            border: none !important;
        }

        .fulldiv36 {
            /* Firefox */
            height: -moz-calc(100% - 36px) !important;
            /* WebKit */
            height: -webkit-calc(100% - 36px) !important;
            /* Opera */
            height: -o-calc(100% - 36px) !important;
            /* Standard */
            height: calc(100% - 36px) !important;
            display: block;
            border: none !important;
        }

        .fulldiv38 {
            /* Firefox */
            height: -moz-calc(100% - 38px) !important;
            /* WebKit */
            height: -webkit-calc(100% - 38px) !important;
            /* Opera */
            height: -o-calc(100% - 38px) !important;
            /* Standard */
            height: calc(100% - 38px) !important;
            display: block;
            border: none !important;
        }

        .fulldiv72 {
            /* Firefox */
            height: -moz-calc(100% - 72px) !important;
            /* WebKit */
            height: -webkit-calc(100% - 72px) !important;
            /* Opera */
            height: -o-calc(100% - 72px) !important;
            /* Standard */
            height: calc(100% - 72px) !important;
            display: block;
            border: none !important;
        }

        .halfdiv {
            height: 50% !important;
            min-height: 50% !important;
            border: none !important;
        }

        .p15 {
            padding: 18px !important;
        }

        .dropdown-menu > ul > li > a {
            white-space: normal;
        }

        #divAddAllNewMenu ul li {
            cursor: pointer;
        }

            #divAddAllNewMenu ul li ul {
                position: absolute;
                display: none;
            }

            #divAddAllNewMenu ul li:hover ul {
                display: block;
                position: inherit;
            }

        div#spinnertool {
            display: none;
            width: 150px;
            height: 75px;
            position: fixed;
            top: 50%;
            left: 50%;
            margin-left: -75px !important;
            margin-top: -37.5px !important;
            text-align: center;
            margin-left: -50px;
            margin-top: -50px;
            z-index: 9999 !important;
            border-radius: 10px;
            background-color: #EEEEEE;
        }

        .demo-section p {
            margin: 3px 0 20px;
            line-height: 50px;
        }

        /*  .demo-section .k-button {
            width: 250px;
        }*/

        .k-notification {
            border: 0;
        }


        /* Info template */
        .k-notification-info.k-group {
            background: rgba(0%,0%,0%,.7);
            color: #fff;
        }


        /* 2017.3.1026 version updated ..J */
        .k-notification.k-notification-info {
            background: #428BCA !important;
        }

        .k-multiselect-wrap .k-i-loading {
            visibility: hidden;
        }

        .new-mail {
            width: 300px;
            height: 75px;
            /*margin-top: -10px;*/
        }

        ol.timeline-listPop li.timeline-item {
            position: relative;
            padding: 0px 16px 22px 50px;
            top: 20px;
        }

        .timeline-listPop {
            list-style: none;
            padding-left: 0;
        }

        .new-mail h3 {
            font-size: 1em;
            padding: 1px 15px 5px !important;
        }

        ol.timeline-listPop li.timeline-item .timeline-icon {
            opacity: 0.85;
            z-index: 2;
            position: absolute;
            left: 10px;
            top: 3px;
            background: #BBB;
            width: 30px;
            height: 30px;
            line-height: 26px;
            color: #FFF;
            text-align: center;
            border-radius: 26px;
            border: 2px solid #FFF;
        }

        .new-mail img {
            float: left;
            margin: 8px 8px 8px 8px;
        }

        .k-notification-info.k-group {
            background: #428BCA !important;
            color: #fff;
            font-size: 12px !important;
        }

        /*Notification button style*/
        .btnNotif, .btnShowAlwaysNotif {
            /*top: 0;
            right: 0;*/
            position: absolute;
            background-color: transparent;
            border: #428BCA;
        }

        /*Upgraded MVC5.2.3 & Kendo 2017*/
        /*Notification text color*/
        .k-widget.k-notification.k-notification-info {
            color: white !important;
        }

        .scroller-handle {
            right: 0% !important;
        }

        .panel-scroller .scroller-bar {
            width: 5px !important;
        }

        /* fixed the issue in loadview properties -> filter & sort (viewtype-chart) ->  select any link field -> click on ... ->
            open fields drop down   overflow is not working as expected -- RN */
        .k-animation-container.km-popup .k-list-container.k-popup.k-group.k-reset.k-state-border-up,
        .k-animation-container.km-popup .k-list-container.k-popup.k-group.k-reset.k-state-border-down {
            overflow: auto !important;
        }

        /*Task&Automator Tab Css..S1*/
        .TaskNAutomator_Cls {
            cursor: pointer;
            display: list-item;
            text-align: -webkit-match-parent;
            /*margin-left: -15px;*/
            width: 100%;
            min-height: 20px;
            padding: 5px 5px 5px 15px;
            font-size: 12px !important;
        }

            .TaskNAutomator_Cls:hover {
                background-color: #EAEAEA;
            }

        .TaskNAutomator_Anchor_Cls {
            color: #4C4A49;
            text-decoration: none;
        }

            .TaskNAutomator_Anchor_Cls:hover {
                text-decoration: none;
                color: #4C4A49;
            }

            .TaskNAutomator_Anchor_Cls:focus {
                text-decoration: none;
                color: #4C4A49;
            }

        ol.timeline-list_Tasks li.timeline-item {
            position: relative;
            padding: 0px 16px 22px 50px;
            top: 20px;
        }

        .timeline-list_Tasks {
            list-style: none;
            padding-left: 0;
        }

        ol.timeline-list_Tasks li.timeline-item .timeline-icon {
            opacity: 0.85;
            z-index: 2;
            position: absolute;
            left: 10px;
            top: 3px;
            background: #BBB;
            width: 30px;
            height: 30px;
            line-height: 26px;
            color: #FFF;
            text-align: center;
            border-radius: 26px;
            border: 2px solid #FFF;
        }

        .scroller-handle {
            opacity: 1 !important;
        }

        /*SB 10-26-2017 Tckt#1879 Task&Automator dropdown default Scroll bar customization*/
        #divAutoTaskMenu_List::-webkit-scrollbar {
            width: 5px;
        }

        #divAutoTaskMenu_List::-webkit-scrollbar-thumb {
            background-color: #CCCCCC;
            border-radius: 4px;
        }
        /*Task&Automator Tab Css End*/

        .k-grid {
            font-size: 13px !important;
        }

        .k-draghandle.k-state-selected:hover, .k-ghost-splitbar-horizontal, .k-ghost-splitbar-vertical, .k-list > .k-state-highlight, .k-list > .k-state-selected, .k-marquee-color, .k-panel > .k-state-selected, .k-scheduler .k-scheduler-toolbar .k-state-selected, .k-scheduler .k-today.k-state-selected, .k-state-selected, .k-state-selected:link, .k-state-selected:visited {
            color: #fff !important;
            background-color: #428bca !important;
            border-color: #428bca !important;
        }

        .k-tabstrip {
            font-size: 13px !important;
        }

        select {
            font-size: 12px !important;
        }

        .search-container {
            display: flex;
            justify-content: center;
        }
    </style>

    <!-- jQuery -->

    <script src="~/Content/themes/Selltis/scripts/2023/jquery.min.js" type="text/javascript"></script>
    <link href="~/Content/themes/Selltis/css/Custom/Grid.css?v=1.1" rel="stylesheet" type="text/css" />
    <script src="~/Content/themes/Selltis/scripts/2023/kendo.all.min.js" type="text/javascript"></script>
    <script src="~/Content/themes/Selltis/scripts/2023/kendo.aspnetmvc.min.js" type="text/javascript"></script>
    <script src="~/Content/themes/Selltis/scripts/2023/jszip.min.js" type="text/javascript"></script>
    <script src="~/Content/themes/Selltis/scripts/Custom/Grid.js" type="text/javascript"></script>

</head>

<body class="form-inputs-page">
    <!-- Start: Main -->
    <div id="main" class="fulldivp">
        <!-- Start: Header -->
        <header class="navbar navbar-fixed-top navbar-shadow">
            <div class="navbar-branding">
                @if (Util.GetLogoUrl() == "")
                {
                    <a class="navbar-brand" href="#">
                        <img src=@Util.GetCustomLogo() style="z-index: 0;width: 100%;" />
                    </a>
                }
                else
                {
                    <a class="navbar-brand" href="@Util.GetLogoUrl()">
                        <img src=@Util.GetCustomLogo() style="height: 45px; z-index: 0;width: 100%;" />
                    </a>
                }
            </div>
            <span id="toggle_sidemenu_l" class="ad ad-lines" style="float:left;text-align:left;"></span>

            <ul class="nav navbar-nav navbar-right">

                <!--Add new Menu-->
                @if (Util.Show("ADDNEW"))
                {
                    <li class="dropdown menu-merge ">
                        <div class="navbar-btn btn-group" title="Add New">
                            <button data-toggle="dropdown" onclick="GetAddNewMenuHtml()" class="btn btn-sm dropdown-toggle">
                                <span class="fa fa-plus"></span>
                            </button>
                            <div id="divAddAllNewMenu" class="dropdown-menu dropdown-persist w150 animated animated-shorter" role="menu">

                            </div>
                        </div>
                    </li>
                }

                @*@if (Selltis.BusinessLogic.clSettings.GetUseSSO() == "true")
                {
                <li>
                    <div class="navbar-btn btn-group" title="Admin - Login as another user">
                        <button onclick="LoginWithAnotherUser();" class="btn btn-sm hidden-sm hidden-xs">
                            <i class="fa fa-user" aria-hidden="true"></i>
                        </button>
                    </div>
                </li>
                }*@

                <!--Tools Menu-->
                <li class="dropdown menu-merge ">
                    <div class="navbar-btn btn-group">
                        <button data-toggle="dropdown" onclick="GetToolsMenuHtml()" title="Tools" class="btn btn-sm dropdown-toggle hidden-sm hidden-xs">
                            <span class="fa fa-wrench"></span>
                        </button>
                        <div id="divToolsMenu" class="dropdown-menu dropdown-persist w200 animated animated-shorter" role="menu">

                        </div>
                    </div>
                </li>

                <!--Tasks Menu-->
                <!--Tasks&Automators Menu Tabs-->
                <li class="dropdown menu-merge ">
                    <div class="navbar-btn btn-group">
                        <button data-toggle="dropdown" onclick="GetTaskMenuHtml()" title="Tasks & Automators" class="btn btn-sm dropdown-toggle hidden-sm hidden-xs">
                            <span class="fa fa-tasks"></span>
                        </button>
                        <div id="DropDownMenu_TaskAutomators" class="dropdown-menu dropdown-persist w350 animated animated-shorter" role="menu" style="width: 300px;">
                            <div style="background-color: #fafafa; padding: 6px; border: 1px solid #e2e2e2;">
                                <div>
                                    <a id="TaskMenuId" href="#" onclick="javascript: GetTaskMenuHtml();" title="Tasks" class="btn btn-default btn-sm active" aria-expanded="true" style="width:48%;margin-left: 1%;box-shadow: none !important;"><i class="fa fa-tasks"></i> Tasks</a>
                                    <a id="AutomatorsMenuId" href="#" onclick="javascript: GetAutomatorToolbarHtml();" title="Automators" class="btn btn-default btn-sm" aria-expanded="true" style="width:48%;margin-left: 1%;box-shadow: none !important;"><i class="fa fa-list-alt"></i> Automators</a>
                                </div>
                            </div>

                            <!--SB 10-26-2017 Tckt#1879 Task&Automator dropdown-->
                            <div id="divAutoTaskMenu_List" style="max-height:500px !important;overflow-y:auto;">
                                <ol id="divTaskAutomatorMenu" class="timeline-list_Tasks"></ol>
                            </div>
                        </div>
                    </div>
                </li>

                <!--Find-->
                @if (Util.Show("FIND"))
                {
                    <li>
                        <div class="navbar-btn btn-group" title="Find">
                            <button onclick="AutomatorToolBarContent('SiteWorkarea', 'NDBFORM|FIND');" class="btn btn-sm hidden-sm hidden-xs">
                                <i class="fa fa-search" aria-hidden="true"></i>
                            </button>
                        </div>
                    </li>
                }
                <!--Automarkselectedcompleted-->
                @if (Util.Show("MARKCOMPLETED"))
                {
                    <li>
                        <div class="navbar-btn btn-group" title="Mark Selected record as completed">
                            <button onclick="AutomatorToolBarContent('SiteRunScript', 'AutoMarkSelectedCompleted');" class="btn btn-sm hidden-sm hidden-xs">
                                <i class="fa fa-check" aria-hidden="true"></i>
                            </button>
                        </div>
                    </li>
                }
                <!--Alerts-->
                <li class="dropdown menu-merge">
                    <div class="navbar-btn btn-group" title="Alerts">
                        <button data-toggle="dropdown" id="btnAlertForDropDown" class="btn btn-sm dropdown-toggle hidden-sm hidden-xs">
                            <span id="AlertsIcon" class="fa fa-bell-o fs14 va-m"></span>
                            <span id="AlertsCount" class="badge badge-danger"></span>
                        </button>
                        <div class="dropdown-menu dropdown-persist w350 animated animated-shorter" role="menu" id="AlertsDDL">
                            <div class="panel mbn">
                                <div style="padding: 10px 12px !important;">
                                    <span style="margin-left: 5px;"><i id="AlertsIconInPannel" class="fa fa-bell-o"></i></span>
                                    <span style="margin-left: 5px;">Alerts</span>
                                    <button class="btn btn-default light btn-xs pull-right" title="Delete all" type="button" onclick="ALT_DeleteAll()" style="margin-top: -3px;"><span><i class="fa fa-trash-o"></i></span></button>
                                    <button class="btn btn-default light btn-xs pull-right" title="Refresh" type="button" onclick="CheckAlerts()" style="margin-top: -3px; margin-right: 5px;"><span><i class="fa fa-refresh"></i></span></button>
                                </div>
                                <div class="panel-body panel-scroller scroller-navbar scroller-overlay scroller-pn pn">
                                    <ol class="timeline-list"></ol>
                                </div>

                            </div>
                        </div>
                    </div>
                </li>

                <!--History-->
                <li class="dropdown menu-merge">
                    <div class="navbar-btn btn-group" title="History">
                        <button data-toggle="dropdown" id="btnHistoryForDropDown" onclick="GetHistoryHtml()" class="btn btn-sm dropdown-toggle hidden-sm hidden-xs">
                            <span class="fa fa-history fs14 va-m"></span>
                        </button>
                        <div class="dropdown-menu dropdown-persist w350 animated animated-shorter" role="menu" id="HistoryDDL">
                            <div class="panel mbn">
                                <div style="padding: 10px 12px !important;">
                                    <span style="margin-left: 5px;"><i id="HistoryIconInPannel" class="fa fa-history"></i></span>
                                    <span style="margin-left: 5px;">History</span>
                                    <button class="btn btn-default light btn-xs pull-right" title="Delete all" type="button" onclick="DeleteAllHistoryItems()" style="margin-top: -3px;"><span><i class="fa fa-trash-o"></i></span></button>
                                </div>
                                <div class="panel-body panel-scroller scroller-navbar scroller-overlay scroller-pn pn">
                                    <ol id="divHistoryList" style="list-style: none;padding-left: 0;"></ol>
                                </div>

                            </div>
                        </div>
                    </div>
                </li>

                @if (Util.Show("HELP"))
                {
                    <!--Help Menu-->
                    <li class="dropdown menu-merge" style="display:none;">
                        <div class="navbar-btn btn-group" title="Help">
                            <button data-toggle="dropdown" onclick="GetHelpMenuHtml()" class="btn btn-sm dropdown-toggle hidden-sm hidden-xs">
                                <span class="fa fa-question"></span>
                            </button>
                            <div id="divHelpMenu" class="dropdown-menu dropdown-persist w200 animated animated-shorter" role="menu">

                            </div>
                        </div>
                    </li>
                }

                @if (Util.Show("HOME"))
                {
                    <!--Home Button-->
                    <li>
                        <div class="navbar-btn btn-group" title="Go to Home Page">
                            <button onclick="gotoDsk('@Util.GetHomeDesktopId()');" class="btn btn-sm hidden-sm hidden-xs" style="background-color:#70ca63;color:white;">
                                <i class="fa fa-home" aria-hidden="true"></i>
                            </button>
                        </div>
                    </li>
                }

                <!--Logout Section-->
                <li class="dropdown menu-merge">
                    <a href="#" class="dropdown-toggle fw600 p15" data-toggle="dropdown">
                        @if (Selltis.Core.Util.GetSessionValue("userlastname") != null)
                        {
                            <button data-toggle="dropdown" style="padding: 1px; background-color: #3bafda;" class="btn btn-info btn-circle mw30 br64 btn btn-sm dropdown-toggle">
                                <span style="border-color: #3276b1;" id="AlertsCount">@Selltis.Core.Util.GetSessionValue("UserLastName").ToString()</span>
                            </button>
                        }

                        @if (Selltis.Core.Util.GetSessionValue("username") != null)
                        {
                            <span class=" pl15">@Selltis.Core.Util.GetSessionValue("username").ToString()</span>
                        }
                        else
                        {
                            Response.Redirect("/Login/Logout");
                        }
                        <span class="caret caret-tp "></span>
                    </a>
                    <ul class="dropdown-menu list-group dropdown-persist w250" role="menu">
                        <li class="list-group-item" style="display:none;">
                            <a href="#" class="animated animated-short fadeInUp">
                                <span class="fa fa-user"></span>Profile
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a onclick="PersonalOptionsClick()" href="#" class="animated animated-short fadeInUp">
                                <span class="fa fa-gear"></span>Personal Options
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a onclick="TimeZoneClick()" href="#" class="animated animated-short fadeInUp">
                                @if (Selltis.Core.Util.GetSessionValue("TimeZone") != null)
                                {
                                    <span class="fa fa-clock-o"></span><span>TimeZone @Selltis.Core.Util.GetSessionValue("TimeZone").ToString()</span>
                                }
                            </a>
                        </li>
                        <li class="list-group-item">
                            <a href="@Url.Action("LoadLagacyWhatsNew", "WhatsNew")" class="animated animated-short fadeInUp">
                                @if (Selltis.Core.Util.GetSessionValue("AppVersion") != null)
                                {
                                    <span class="fa fa-history"></span><span> What's New </span>
                                }
                            </a>
                        </li>
                        @if (Selltis.BusinessLogic.clSettings.GetUseSSO().ToLower() == "true" && Util.IsUserAdmin() == true)
                        {
                            <li class="list-group-item">
                                <a onclick="Admin_Login_As_AnotherUser()"href="#" class="animated animated-short fadeInUp">
                                    @if (Selltis.Core.Util.GetSessionValue("AppVersion") != null)
                                    {
                                        <span class="fa fa-user"></span><span> Admin - Login as another user </span>
                                    }
                                </a>
                            </li>
                            }

                        <li class="list-group-item">
                            <a href="@Url.Action("Logout","Login")" class="">
                                <span class="fa fa-power-off pr5"></span>Logout (@if (Selltis.Core.Util.GetSessionValue("CompanyName") != null)
                                {
                                    @Selltis.Core.Util.GetSessionValue("CompanyName").ToString()
                                })
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>

            @if (Util.Show("GLOBALSEARCH"))
            {
                @*<div class="nav navbar-nav navbar-right">
                        <div class="navbar-btn btn-group">
                            <select id="DoSearchDesktop1" name="SearchDesktop" style="width:65px;height:25px;font-size:12px;box-sizing:border-box">
                                <option selected>All</option>
                                <option>Customers</option>
                                <option>Activities</option>
                                <option>Contacts</option>
                                <option>Opportunities</option>
                                <option>Quotes</option>
                            </select>
                            <input type="text" id="txtsearch" style="width:375px" placeholder="Search..." onkeypress="DoGlobalSearch(event)" />
                        </div>
                    </div>*@
                <div class="nav navbar-nav navbar-right">
                    <div class="navbar-btn btn-group">
                        <select id="DoSearchDesktop1" name="SearchDesktop" style="width:65px;height:25px;font-size:12px;box-sizing:border-box">
                            
                                @*<option selected>All</option>
                                <option>Activities</option>
                                <option>Companies</option>
                                <option>Contacts</option>
                                <option>Opportunities</option>
                                <option>Quotes</option>*@
                            
                        </select>
                        <input type="text" id="txtsearch" style="width:375px" placeholder="Search..." onkeypress="DoGlobalSearch(event)" />
                    </div>
                </div>
            }

        </header>
        <!-- End: Header -->
        <!-- Start: Sidebar -->
        <aside id="sidebar_left" class="nano nano-light affix" style="min-height:720px;">
            <!-- Start: Sidebar Left Content -->
            <div id="DynamicNodes" class="sidebar-left-content nano-content">
                <!-- Start: Sidebar Menu -->
                @{
                    if (Selltis.Core.Util.GetInstance("data") == null)
                    {
                        Response.Redirect("~/Login/Logout");
                    }
                    SidebarDesktopsMenu desktopsmenu = new SidebarDesktopsMenu();
                }
                @Html.Raw(desktopsmenu.GetMenu1())
            </div>
            <!-- End: Sidebar Left Content -->
        </aside>
        <!-- End: Sidebar -->
        @RenderBody()
        @(Html.Kendo().Notification()
                .Name("notification")
                .Position(p => p.Pinned(true).Top(25).Right(30))
                .Stacking(NotificationStackingSettings.Down)
                .Events(e => e.Show("onShowNotification"))
                .AutoHideAfter(5000).HideOnClick(false).Button(true)
                .Templates(t =>
                {
                    t.Add().Type("info").ClientTemplateID("emailTemplate");
                })
        )
    </div>
    <!-- End: Main -->
    <!-- jQuery -->
    <!-- Select2 Plugin Plugin -->
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/select2.min.js")" type="text/javascript"></script>
    <!-- Summernote Plugin -->
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/summernote.min.js")" type="text/javascript"></script>
    <!-- Datatables -->
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/jquery.dataTables.js")" type="text/javascript"></script>
    <!-- Datatables Tabletools addon -->
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/dataTables.tableTools.min.js")" type="text/javascript"></script>
    <!-- Datatables ColReorder addon -->
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/dataTables.colReorder.min.js")" type="text/javascript"></script>
    <!-- Datatables Bootstrap Modifications  -->
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/dataTables.bootstrap.js")" type="text/javascript"></script>
    <!-- Theme Javascript -->
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/utility.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/demo.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/main.js?v=1.0")" type="text/javascript"></script>

    <script type="text/javascript">

        //for global Search
        function DoGlobalSearch(event) {

            var x = event.which || event.keyCode;

            if (x == 13) {

                var skey = $("#txtsearch").val();

                if (skey == '') {
                    alert('Please enter at least 2 chars to search.');
                    return;
                }
                else if (skey.length < 2) {
                    alert('Please enter at least 2 chars to search.');
                    return;
				}
                 var foo = $("#DoSearchDesktop1 option:selected").text();

                $.ajax({
                    url: '/Common/DoGlobalSearch',
                    data: {
						doSearchText: skey,
                        doSearchType:foo
                    },
					success: function (result) {
						//debugger;
                        //var foo = $("#DoSearchDesktop1 option:selected").text();
                        gotoDsk(DesktopId = result);
                        //if (foo == "" && foo == 'All') {
                        //    gotoDsk(DesktopId = 'DSK_359DCF4C-762A-42E9-5858-AC3E007BB2FE');
                        //}
                        //else if (foo == 'Accounts') {
                        //    gotoDsk(DesktopId = 'DSK_295F1E6E-AFFB-4722-5858-AC400065EBC0');
                        //}
                        //else if (foo == 'Activities') {
                        //    gotoDsk(DesktopId = 'DSK_DDF2F791-9B7E-404D-5858-AC4000678960');
                        //}
                        //else if (foo == 'Contacts') {
                        //    gotoDsk(DesktopId = 'DSK_F56A32D7-C8B3-4093-5858-AC400068B049');
                        //}
                        //else if (foo == 'Opportunities') {
                        //    gotoDsk(DesktopId = 'DSK_33311203-10C5-4B6F-5858-AC400066A9FB');
                        //}
                        //else if (foo == 'Products') {
                        //    gotoDsk(DesktopId = 'DSK_E58C7C15-1CA3-4C12-5858-AC40006834DC');
                        //}
                        //else if (foo == 'Users') {
                        //    gotoDsk(DesktopId = 'DSK_D2EBDF0B-B91C-4ED2-5858-AC40006938A2');
                        //}
                        //else if (foo == 'Quotes') {
                        //    gotoDsk(DesktopId = 'DSK_D52E22B9-8CF3-4795-5858-ADA3007630E7');
                        //}
                        //else if (foo == 'Leads') {
                        //    gotoDsk(DesktopId = 'DSK_30B33E00-BBFB-4F75-5858-ADF00082F891');
                        //}
                        //else {
                        //    gotoDsk(DesktopId = 'DSK_359DCF4C-762A-42E9-5858-AC3E007BB2FE');
                        //}
                    },
                    error: function (result) {
                    }
                })
            }
        }
        function GetQuickSearchMenu() {
            $.ajax({
                url: '/Common/GetQuickSearchMenu',
                success: function (result) {
                    $("#DoSearchDesktop1").html(result);
                },
                error: function (result) {
                }
            })
            }
        //Get html data for AddNew menu when click on Add new button..J
        function GetAddNewMenuHtml() {
            $.ajax({
                url: '/Common/GetAddNewMenuHtml',
                success: function (result) {
                    $("#divAddAllNewMenu").html(result);
                },
                error: function (result) {
                }
            })
        }

        //Get html data for Tasks menu when click on Tasks button..J
        function GetTaskMenuHtml() {
            $.ajax({
                url: '/Common/GetTaskMenuHtml',
                success: function (result) {
                    //Tasks list load..S1
                    $("#divTaskAutomatorMenu").html(result);
                    $('#divTaskAutomatorMenu ul>li').addClass('TaskNAutomator_Cls');
                    $('#divTaskAutomatorMenu ul').css({ 'list-style-type': 'none', 'padding': '0' });
                    $('#divTaskAutomatorMenu ul>li a').addClass('TaskNAutomator_Anchor_Cls');

                    $('#TaskMenuId').css('background-color', '#E0E0E0');
                    $('#AutomatorsMenuId').css('background-color', '#F4F4F4');
                },
                error: function (result) {
                }
            })
        }

        //Get html data for Help menu when click on Help button..J
        function GetHelpMenuHtml() {
            $.ajax({
                url: '/Common/GetHelpMenuHtml',
                success: function (result) {
                    $("#divHelpMenu").html(result);
                },
                error: function (result) {
                }
            })
        }

        //Get html data for Tools menu when click on Tools button..J
        function GetToolsMenuHtml() {
            $.ajax({
                url: '/Common/GetToolsMenuHtml',
                success: function (result) {
                    $("#divToolsMenu").html(result);
                },
                error: function (result) {
                }
            })
        }
        //Login with another User for admin
        function Admin_Login_As_AnotherUser() {
            showProgress();
            $.ajax({
                url: '/Login/Admin_Login_As_AnotherUser',
                success: function (result) {
                    if (result != null) {
                        hideProgress();
                        if (result == 'Err1') {
                            alert('Please select an user record to Login.');
                        }
                        else if (result == 'Err2') {
                            alert('E-mail is blank for the selected user so you cannot login.');
                        }
                        else if (result == 'Err3') {
                            alert('Error in getting user record.');
                        }
                        else {
                            window.location.href = result.redirectToUrl;
                        }
                    }
                },
                error: function (error) {
                    hideProgress();
                }
            })
        }
        //Get Automator tool bar Menu when click on automatortoolbar button..J
        function GetAutomatorToolbarHtml() {
            $.ajax({
                url: '/Common/GetAutomatorToolbarHtml',
                success: function (result) {
                    //Automators list load..S1
                    $("#divTaskAutomatorMenu").html(result);
                    $('#divTaskAutomatorMenu ul>li').addClass('TaskNAutomator_Cls');
                    $('#divTaskAutomatorMenu ul').css({ 'list-style-type': 'none', 'padding': '0' });
                    $('#divTaskAutomatorMenu ul>li a').addClass('TaskNAutomator_Anchor_Cls');

                    $('#AutomatorsMenuId').css('background-color', '#E0E0E0');
                    $('#TaskMenuId').css('background-color', '#F4F4F4');
                },
                error: function (result) {
                }
            })
        }

        function SiteRunScript(script) {
            $.ajax({
                url: '/Common/SiteRunScript',
                cache: false,
                async: true,
                data: { Script: script },
                success: function (data) {
                    if (data == "OPENFILE" || data == 'LOGEMAIL') {
                        window.open("/Pages/diaLoadSend.aspx", "OpenFile", "top=100,left=100,height=150,width=250");
                    }
                },
                error: function (data) {
                }
            })
        }
        // onShowNotification Value.. Dont change*
        var ind = 0;
        jQuery(document).ready(function () {

            "use strict";
            // Init Theme Core
            Core.init();

            CheckAlerts();
            setInterval(CheckAlerts, 300000); // changed the interval time from 30 sec to 5 mins

            //setInterval(SetAlertsCount, 30000);

        });


        $("#btnAlertForDropDown").click(function () {
            CheckAlerts();
        });


        function SetAlertsCount() {
            $.ajax({
                url: '/Common/GetAlertsCount',
                cache: false,
                async: true,
                success: function (data, statusText, xhr) {
                    if (data == 'Logout') {
                        window.location = "/Login/Logout";
                    }

                    if (xhr.readyState == 4 && xhr.status == 200) {
                        if (data.Count > 0) {
                            $('#AlertsCount').show();
                            $('#AlertsCount').html(data.Count);
                            //$('.fa-bell-o').addClass('fa-bell');
                            //$('.fa-bell').removeClass('fa-bell-o');
                            //$('.fa-bell').css({ 'color': '#E05E49', '-webkit-text-stroke-color': '#E05E49' });

                            //S_B Tkt#2174 Change Bell button to red
                            $('#btnAlertForDropDown').css('background-color', '#FA0016');
                            $('.fa-bell-o').addClass('fa-bell');
                            $('.fa-bell').removeClass('fa-bell-o');
                            $('.fa-bell').css({ 'color': '#FFFFFF', '-webkit-text-stroke-color': '#FFFFFF' });
                        }
                    }
                }
            })
        }

        function CheckAlerts() {
            ind = 0;
            var notification = $("#notification").data("kendoNotification");
            var notificationCount = 0;
            $.ajax({
                url: '/Common/CheckAlerts',
                cache: false,
                data: { Event: "", Args: "", Refresh: "NO" },
                async: true,
                success: function (data, statusText, xhr) {
                    if (data == 'Logout') {
                        window.location = "/Login/Logout";
                    }

                    //Showing alerts when Status code = 200.
                    if (xhr.readyState == 4 && xhr.status == 200) {
                        if (data.Count > 0) {
                            $('.timeline-list').show();
                            $('.timeline-list').html(data.AlertsHTML);
                            $('#AlertsCount').show();
                            $('#AlertsCount').html(data.Count);
                            //$('.fa-bell-o').addClass('fa-bell');
                            //$('.fa-bell').removeClass('fa-bell-o');
                            //$('.fa-bell').css({ 'color': '#E05E49', '-webkit-text-stroke-color': '#E05E49' });

                            //S_B Tkt#2174 Change Bell button to red
                            $('#btnAlertForDropDown').css('background-color', '#FA0016');
                            $('.fa-bell-o').addClass('fa-bell');
                            $('.fa-bell').removeClass('fa-bell-o');
                            $('.fa-bell').css({ 'color': '#FFFFFF', '-webkit-text-stroke-color': '#FFFFFF' });

                            if (sessionStorage.getItem('notificationCount') != null) {
                                notificationCount = parseInt(sessionStorage.getItem('notificationCount'));
                            }

                            if (data.Count > notificationCount) {

                                if (data.Count == notificationCount + 1) {
                                    notification.show({
                                        html: data.AlertsLastRowHTML[0]
                                    }, "info");
                                    $('.btnNotif').show();
                                }
                                else if (data.Count > 5) {
                                    notification.show({
                                        html: "<li id='NotifAl' style='cursor:pointer;' class=\"timeline-item\" onclick=\"ShowAlertBox(this);\"><div class=\"timeline-icon bg-info\" style=''><span><i class=\"fa fa-bell-o\"></i></span></div> You have <b>" + data.Count + "</b> new alerts, click here to see all.</li>"
                                    }, "info");
                                    $('.btnNotif').hide();
                                }
                                else {
                                    for (var i = 0; i < data.Count; i++) {
                                        if (data.AlertsLastRowHTML != null) {
                                            notification.show({
                                                html: data.AlertsLastRowHTML[i]
                                            }, "info");
                                        }
                                    }
                                    $('.btnNotif').show();
                                }
                            }
                            sessionStorage.setItem('notificationCount', data.Count);

                            //S_B Tkt#2370 AlertBox height for SAS/Global
                            if (data.Count > 4) {
                                if (data.Count > 10) {
                                    $('.scroller-navbar').css('height', 10 * 53.75);
                                }
                                else {
                                    $('.scroller-navbar').css('height', data.Count * 53.75);
                                }
                            }
                            else {
                                $('.scroller-navbar').css('height', 215);
                            }
                        }
                        else {
                            $('.fa-bell').addClass('fa-bell-o');
                            $('.fa-bell-o').removeClass('fa-bell');
                            $('.fa-bell-o').removeAttr('style');
                            $('#AlertsCount').hide();
                            $('#btnAlertForDropDown').removeAttr('style');
                        }
                        if (data.Count <= 4) {
                            $('.panel-footer').hide();
                        }
                    }
                },
                error: function (data) {
                }
            })
        }

        function GetHistoryHtml() {
            $.ajax({
                url: '/Common/GetHistoryHtml',
                async: false,
                cache: false,
                success: function (result) {
                    $("#divHistoryList").html(result);
                },
                error: function (result) {
                }
            })
        }

        function OnHistoryListItemClick(url, id, Key, type) {
            $.ajax({
                url: '/Common/CheckUrl',
                async: false,
                cache: false,
                data: { Key: Key },
				success: function (result) {
                    if (type == "Desktop") {
                        window.location.href = '/Desktop/LoadDesktop/' + id + '/true/FOLDERID/' + Key;
                    }
					else if (type == "Form") {
						//SB 11202019 Handling Selltis Unusual eroor message fix
						if (url.indexOf('|/') > 0) {
							url = url.substring(0, url.indexOf("|") - 2) + url.substring(url.indexOf("|") + 1)
							url = url.replace('|', '');
						}
						else if (url.indexOf('|') > 0) {
							url = url.substring(0, url.indexOf("|") - 2) + url.substring(url.indexOf("|") + 1)
							url = url.substring(0, url.indexOf("|")) + url.substring(url.indexOf("|") + 7)
							url = url.replace('|', '');
						}
                        window.location.href = url;
					}
                },
                error: function (result) {
                }
            })
        }

        //Delete options for History Start..S1
        function DeleteHistoryItem(Key) {

            $.ajax({
                url: '/Common/DeleteHistoryItem',
                cache: false,
                async: true,
                data: { FormHistoryKey: Key },
                success: function (data) {
                    GetHistoryHtml();
                },
                error: function (request, status, error) {
                }
            });
        }
        function DeleteAllHistoryItems() {
            $.ajax({
                url: '/Common/DeleteAllHistoryItems',
                cache: false,
                async: true,
                success: function (data) {
                    GetHistoryHtml();
                },
                error: function (request, status, error) {
                }
            });
        }
        //Delete options for History End

        //Showing Notification with proper values.. S1
        function onShowNotification(e) {
            if (e.sender.getNotifications().length >= 1) {
                var element = e.element.parent(),
                    eWidth = element.width(),
                    eHeight = element.height(),
                    wWidth = $(window).width(),
                    wHeight = $(window).height(),
                    newTop, newLeft;
                newTop = element[0].style.top.substring(0, 3);
                if (newTop.indexOf('p') >= 0) {
                    newTop = element[0].style.top.substring(0, 2);
                }
                newLeft = Math.floor(wWidth - (eWidth + 110));
                if (ind == 0) {
                    e.element.parent().css({ left: newLeft + 100 });
                }
                else {
                    e.element.parent().css({ left: newLeft + 100 });
                }
                e.element.parent().css({ height: 75 });
                if (parseInt(newTop) > 25)
                    e.element.parent().css({ top: parseInt(newTop) + 3 });
                ind++;
            }
        }

        function ShowAlertBox(e) {
            var notification = e.parentElement.parentElement.parentElement.parentElement;
            $(notification).hide();
            setTimeout(function () {
                $('#btnAlertForDropDown').click();
            }, 1000);

        }

        function OpenNotification(e, target) {
            if (target == 'Notif') {
                $('#' + $($(e.parentElement.children['3'])[0].innerHTML)[0].id).click();
            }
            else {
                var iHt = $(e.parentElement.parentElement.children[2])[0].innerHTML;
                iHt = iHt.substring(iHt.indexOf('<p id=\"') + 7, iHt.indexOf('\" style'));
                $('#' + iHt).click();
            }
        }

        //Clearing notification/alert permanantly.. S1
        function ClearNotification(e, target) {
            var id = '';
            var Page = '';
            if (target == 'Notif') {
                id = $($(e.parentElement.children['3'])[0].innerHTML)[0].id;
                Page = id.substring(id.indexOf('PAGE')).substring(4);
            }
            else {
                id = $(e.parentElement.parentElement.children[2])[0].innerHTML;
                id = id.substring(id.indexOf('<p id=\"') + 7, id.indexOf('\" style'));
                Page = id.substring(id.indexOf('AlertPAGE')).substring(9);
            }

            $.ajax({
                url: '/Common/CheckAlerts',
                cache: false,
                async: true,
                data: { Event: "DELETE", Args: Page },
                success: function (data) {
                    CloseNotification(e);
                    CheckAlerts();
                },
                error: function (data) {
                }
            });
        }

        function CloseNotification(e, target) {
            var notification = e.parentElement.parentElement.parentElement;
            $(notification).hide();
        }

        function ALT_DeleteAll() {
            $.ajax({
                url: '/Common/CheckAlerts',
                cache: false,
                async: true,
                data: { Event: "DELETEALL" },
                success: function (data) {
                    $('.timeline-list').html(data.AlertsHTML);
                    $('#AlertsCount').html('');
                    //hide count div when no alerts..J
                    $('#AlertsCount').hide('');
                    //change icon css in menu button..J
                    $('#AlertsIcon').css({ 'color': '', '-webkit-text-stroke-color': '' });
                    $('#AlertsIcon').removeClass('fa fa-bell-o fs14 va-m');
                    $('#AlertsIcon').addClass('fa fs14 va-m fa-bell-o');

                    $('#btnAlertForDropDown').removeAttr('style');

                    //change icon css in pannel also..J
                    $('#AlertsIconInPannel').removeAttr('style');
                    $('#AlertsIconInPannel').removeClass('fa fa-bell');
                    $('#AlertsIconInPannel').addClass('fa fa-bell-o');
                },
                error: function (data) {
                }
            })
        }

        function alertClick(execute, section, type, page, file) {
            var HistoryKey = '@Selltis.Core.Util.GetSessionValue("LastDesktopHistoryKey")';
            if (HistoryKey != null && HistoryKey != "") {
                HistoryKey = HistoryKey.toString()
            }
            else {
                HistoryKey = "";
            }

            showProgress();
            $.ajax({
                url: '/Common/CheckAlerts',
                cache: false,
                async: true,
                data: { Event: "DELETE", Args: page },
                success: function (data) {
                    switch (type) {
                        case "OPENDESKTOP":
                            window.location.href = '/Desktop/LoadDesktop/' + execute + '/false/FOLDERID';
                            break;
                        case "OPENNEWFORM":
                            var filetype = execute.toString().split("_");
                            if (filetype.length > 1) {
                                type = filetype[1];
                                window.location = "/CreateForm/CreateForm/" + type + "/ID/" + execute + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + HistoryKey;
                            }
                            else {
                                hideProgress();
                            }
                            break;
                        case "OPENRECORD":
                            window.location = "/CreateForm/CreateForm/" + file + "/" + execute + "/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + HistoryKey;
                            break;

                        case "RUNSCRIPT":
                            hideProgress();
                            break;

                        case "OPENURL":
                            hideProgress();
                            break;

                        case "OPENURLEXTERNAL":
                            hideProgress();
                            break;

                        case "OPENNEWNDBFORM":
                            hideProgress();
                            break;

                        default:
                            hideProgress();
                            break;
                    }
                },
                error: function (data) {
                }
            })

        }
        function TasksContent(execute, type) {
            showProgress();

            var HistoryKey = '@Selltis.Core.Util.GetSessionValue("LastDesktopHistoryKey")';
            if (HistoryKey != null && HistoryKey != "" && HistoryKey != undefined) {
                HistoryKey = HistoryKey.toString()
            }
            else {
                HistoryKey = "";
            }

            switch (type) {
                case "OPENDESKTOP":
                    var executeVal = execute.split("|");
                    if (executeVal.length == 3) {
                        window.location.href = '/Desktop/LoadDesktop/' + executeVal[2] + '/false/FOLDERID';
                    }
                    break;
                case "OPENRECORD":
                    window.location = "/CreateForm/CreateForm/" + file + "/" + execute + "/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + HistoryKey;
                    break;
                case "RUNSCRIPT":
                    $.ajax({
                        url: "/Desktop/LoadRunScript",
                        data: { sEvent: execute },
                        type: 'POST',
                        success: function (data) {
                            if (data != null && data != undefined && data != "") {
                                var _HistoryKey = '@Selltis.Core.Util.GetSessionValue("LastDesktopHistoryKey")';

                                if (data.IsNavigate == true) {
                                    var _navigatetype = data.NavigateType;
                                    if (_navigatetype == "FORM") {
                                        if (_HistoryKey == undefined || _HistoryKey == null || _HistoryKey == "")
                                            _HistoryKey = "HISTORYKEY";

                                        window.location.href = "/CreateForm/CreateForm/" + data.FileName + "/" + data.RecordId + "/" + data.Type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + _HistoryKey;
                                    }
                                    else if (_navigatetype == "DESKTOP") {
                                        var _HistoryKey = data.HistoryKey;
                                        if (_HistoryKey == undefined || _HistoryKey == null || _HistoryKey == "") {
                                            _HistoryKey = "HISTORYKEY";
                                        }
                                        window.location.href = "/Desktop/LoadDesktop/" + data.RecordId + "/false/FOLDERID/" + _HistoryKey;
                                    }
                                    else if (_navigatetype == "OPENURLEXTERNAL") {

                                        if (data.ExternalUrlItems != null && data.ExternalUrlItems != undefined) {
                                            var sURL = data.ExternalUrlItems.sFilePath;
                                            var sTitle = data.ExternalUrlItems.sTitle;
                                            var sParams = data.ExternalUrlItems.sParams;

                                            if (sURL.indexOf("mailto:") == 0) {
                                                window.location = sURL;
                                            }
                                            else {
                                                var myWin = window.open(sURL, sTitle, sParams);
                                                if (window.focus) {
                                                    //if EML will error on focus
                                                    try { myWin.focus() }
                                                    catch (Error) { }
                                                }
                                            }
                                            hideProgress();
                                        }
                                    }
                                }
                                else {
                                    if (data.MessageBox != null) {
                                        if (data.MessageBox.MessageBoxDisplay == true) {
                                            DisplayMessageBox(data.MessageBox);
                                            hideProgress();
                                        }
                                    }
                                    else {
                                        var desktopid = '@Selltis.Core.Util.GetSessionValue("DesktopId")';
                                        var folderid = '@Selltis.Core.Util.GetSessionValue("FolderID")';

                                        if (folderid == undefined || folderid == null || folderid == "")
                                            folderid = "FOLDERID";
                                        if (_HistoryKey == undefined || _HistoryKey == null || _HistoryKey == "")
                                            _HistoryKey = "HISTORYKEY";

                                        if (desktopid != null && desktopid != undefined && desktopid != "") {
                                            window.location.href = "/Desktop/LoadDesktop/" + desktopid + "/false/" + folderid + "/" + _HistoryKey;
                                        }
                                        else {
                                            window.location.href = '/Login/LoginSubmit';
                                        }

                                        $("#PNL_MessageBox_Desktop").hide();
                                    }
                                }
                            }
                            else if (data == "") {
                                hideProgress();
                                $("#PNL_MessageBox_Desktop").hide();
                            }
                        },
                        error: function (data) {
                            hideProgress();
                        }
                    })

                    break;
                case "OPENURL":
                    hideProgress();
                    break;
                case "OPENURLEXTERNAL":
                    hideProgress();
                    break;
                case "OPENNDBFORM":
                    var HistoryKey = '@Selltis.Core.Util.GetSessionValue("LastDesktopHistoryKey")';
                    if (HistoryKey != null && HistoryKey != "") {
                        HistoryKey = HistoryKey.toString()
                    }
                    else {
                        HistoryKey = "";
                    }
                    $.ajax({
                        url: "/CreateForm/LoadNDBScript",
                        data: { sEvent: execute },
                        type: 'POST',
                        success: function (data) {
                            if (data != "" && data != null && data != undefined) {
                                window.location = "/CreateForm/CreateForm/" + data + "/ID/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + HistoryKey;
                            }
                            else {
                                hideProgress();
                            }
                        }
                    })
                    break;
                case "OPENCVS":
                    hideProgress();
                    break;
                case "OPENFRF":
                    hideProgress();
                    break;
                default:
                    hideProgress();
                    break;
            }
        }

		function gotoDsk(DesktopId) {
			//debugger;
            if (DesktopId != null && DesktopId != "")
            {
                sessionStorage.removeItem("type");
                window.location.href = '/Desktop/LoadDesktop/' + DesktopId + '/false/FOLDERID';
            }

        }
        function gotoWCB(DesktopId, FolderId) {
            if (DesktopId == null || DesktopId == "")
                DesktopId = "DESKTOPID";
            sessionStorage.removeItem("type");
            window.location.href = '/Desktop/LoadDesktop/' + DesktopId + '/false/' + FolderId;
        }
        function gotoMenuManager() {
            window.location.href = '/Desktop/MenuManager';
        }
        function NavigatefromForm(To) {

            var HistoryKey = '@Selltis.Core.Util.GetSessionValue("LastDesktopHistoryKey")';
            if (HistoryKey != null && HistoryKey != "") {
                HistoryKey = HistoryKey.toString()
            }
            else {
                HistoryKey = "";
            }

            window.location = "/CreateForm/CreateForm/" + To + "/ID/CRU_/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + HistoryKey;
        }

        function onOpenLinkDesktop(desktopid) {
            $.ajax(
              {
                  type: 'POST',
                  url: '/MenuManager/IsDesktopLink',
                  dataType: 'json',
                  data: { DesktopId: desktopid },
                  success: function (result) {
                      if (result.ShowAsPopUp == "1") {
                          window.open("https://" + result.Url, '_blank');
                      }
                      else {
                          window.location = result.Url;
                      }
                  }
              });
        }
        function MainAddNew(file, target, subnode) {
            if (navigator.onLine == false) {
                hideProgress();
                alert('Check your Internet Connection');
                return false;
            }

            //need to clear calander session dates..J
            $.ajax({
                url: "/CreateForm/ClearCalanderDateSessions",
                async: false,
                cache: false,
                success: function (data) {
                    if (navigator.onLine == false) {
                        hideProgress();
                        alert('Check your Internet Connection');
                        return false;
                    }
                },
                error: function (statusCode, errorThrown) {
                }
            })
            showProgress();
            $.ajax({
                url: "/CreateForm/ClearExistingNewSession",
                async: true,
                cache: false,
                data: { File: file, Type: target, IsSubNode: subnode },
                success: function (data) {
                    if (navigator.onLine == false) {
                        hideProgress();
                        alert('Check your Internet Connection');
                        return false;
                    }

                    var HistoryKey = '@Selltis.Core.Util.GetSessionValue("LastDesktopHistoryKey")';
                    var FormHistoryKey = '@Selltis.Core.Util.GetSessionValue("LastFormHistoryKey")';
                    if (HistoryKey != null && HistoryKey != "") {
                        HistoryKey = HistoryKey.toString()
                    }
                    else {
                        HistoryKey = "HISTORYKEY";
                    }

                    if (data == "success") {
                        window.location.href = "/CreateForm/CreateForm/" + file + "/ID/CRU_" + target + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + HistoryKey;// + "/" + FormHistoryKey
                    }
                    else {
                        window.location.href = "/CreateForm/CreateForm/" + file + "/ID/CRU_" + target + "/false/false/KEY/false/MASTERID/" + data + "/FIELD/" + HistoryKey;// + "/" + FormHistoryKey
                    }
                },
                error: function (statusCode, errorThrown) {
                }
            })
        }


        function gotoDesktop(sDesktop, rootnode, foldernode, section, TestLoad) {
            var desktopid = '';
            showProgress();
            if (sDesktop.indexOf(",") > -1) {
                var desktops = sDesktop.split(',');
                var _eachDesktop = desktops[0].split('~');
                if (_eachDesktop.length == 3) {
                    section = _eachDesktop[2];
                }
                desktopid = _eachDesktop[1];
            }
            else {
                if (sDesktop.indexOf("~") > -1) {
                    var _eachDesktop = sDesktop.split('~');
                    if (_eachDesktop.length == 3) {
                        section = _eachDesktop[2];
                    }
                    desktopid = _eachDesktop[1];
                }
                else {
                    desktopid = sDesktop;
                }
            }

            var testload = "LoadDesktop";
            if (TestLoad != undefined && TestLoad != null && TestLoad != "") {
                testload = "TestDesktop1";
            }

            $.ajax({
                url: '/Desktop/ClearPreviousDesktopCoockieData',  //related to history panel refresh issue..J
                async: false,
                cache: false,
                success: function (result) {
                    sessionStorage.removeItem("type");
                    var Folderid = rootnode + '|' + foldernode;
                    if (section == undefined || section == null || section == '') {
                        window.location.href = '/Desktop/' + testload + '/' + desktopid + '/false/' + Folderid + '/HISTORYKEY/' + '';
                    }
                    else {
                        window.location.href = '/Desktop/' + testload + '/' + desktopid + '/false/' + Folderid + '/HISTORYKEY/' + section;
                    }
                },
                error: function (result) {
                }
            });
        }
        function gotoLink(url, popup) {
            if (popup == "1") {
                if (url.toLowerCase().indexOf('http') <= -1) {
                    url = 'http://' + url;
                }
                window.open(url, '_blank');
            }
            else {
                window.location.href = url;
            }
        }
        function ManageDatabaseClick() {
            showProgress();
            sessionStorage.removeItem("LastTable");
            window.location.href = '/ManageDatabase/ManageDatabase';
        }
        function ManageMenuClick() {
            showProgress();
            window.location.href = '/MenuManager/MenuManager';
        }
        function ManageDesktopsClick() {
            showProgress();
            window.location.href = '/ManageDesktops/OpenDesktopManager';
        }
        function UserGroupPermissionsClick() {
            showProgress();
            window.location.href = '/UserGroupPermissions/UserPermissions';
        }
        function WorkGroupOptionsClick() {
            showProgress();
            window.location.href = '/WorkgroupOptions/LoadWorkgroupOptions';
        }
        function BrowseMetadataClick() {
            showProgress();
            window.location.href = '/BrowseMetadata/LoadBrowseMetadata';
        }
        function PersonalOptionsClick() {
            showProgress();
            window.location.href = '/PersonalOptions/PersonalOptions';
        }
        function TimeZoneClick() {
            showProgress();
            window.location.href = '/PersonalOptions/PersonalOptions?Tab=Regional';
        }
        function ManageFormsClick() {
            showProgress();
            sessionStorage.removeItem("LastSelectedForm");
            window.location.href = '/ManageForms/ManageForms';
        }

        function AutomatorToolBarContent(workarea, sevent) {

            if (workarea == "SiteRunScript") {
                RunScripts("RunScript", sevent, undefined, true);
            }
            else if (workarea == "SiteWorkarea") {
                var eventSplit = null;
                if (sevent.indexOf('|') >= 0) {
                    var eventSplit = sevent.split('|');
                    if (eventSplit[0] == "NDBFORM") {
                        showProgress();

                        $.ajax({
                            url: "/CreateForm/LoadNDBScript",
                            data: { sEvent: sevent },
                            type: 'POST',
                            success: function (data) {
                                if (data != "" && data != null && data != undefined) {
                                    var HistoryKey = '@Selltis.Core.Util.GetSessionValue("LastDesktopHistoryKey")';
                                    if (HistoryKey != null && HistoryKey != "") {
                                        HistoryKey = HistoryKey.toString()
                                    }
                                    else {
                                        HistoryKey = "";
                                    }

                                    window.location = "/CreateForm/CreateForm/" + data + "/ID/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + HistoryKey;
                                }
                                else {
                                    hideProgress();
                                }
                            }
                        })
                    }
                }
            }
            else {
            }
        }

        function RunScripts(runscript, event, arg, isfromautomatortoolbar) {
            if (isfromautomatortoolbar == undefined) {
                isfromautomatortoolbar = false;
            }
            showProgress();
            $.ajax({
                url: "/Desktop/DoEvent",
                data: { sRunscript: runscript, sEvent: event, par_s1: arg, IsFromAutomatorToolbar: isfromautomatortoolbar },
                type: 'POST',
                success: function (data) {

                    var _Key = '@Selltis.Core.Util.GetSessionValue("LastDesktopHistoryKey")';

                    if (Key == null || Key == undefined) {
                        hideProgress();
                        alert("This functionality is not supported in the profile pages.");
                    }

                    var _ViewId = sessionStorage.getItem(Key + "_LastGridViewId");


                    if (data != null && data != undefined && data != "") {
                        if (data.ErrorMessage != "") {
                            var errormessage = "ScriptsError";
                            window.location.href = "/ErrorLog/LogError?sErrorLogs=" + errormessage;
                        }
                        else if (data.IsNavigate == true) {
                            //RN #1873 Multiselect functionality added.
                            if (event.indexOf("AutoMarkSelectedCompleted") > -1) {
                                sessionStorage.removeItem("SelectMultipleRecords_" + _ViewId + _Key);
                            }
                            var _navigatetype = data.NavigateType;
                            if (_navigatetype == "FORM") {

                                var HistoryKey = _Key;
                                if (HistoryKey != null && HistoryKey != "") {
                                    HistoryKey = HistoryKey.toString()
                                }
                                else {
                                    HistoryKey = "";
                                }

                                window.location.href = "/CreateForm/CreateForm/" + data.FileName + "/" + data.RecordId + "/" + data.Type + "/false/false/KEY/false/MASTERID/FORMKEY/FIELD/" + HistoryKey;
                            }
                            else if (_navigatetype == "DESKTOP") {
                                //RN #1873 Multiselect functionality added.
                                if (event.indexOf("AutoMarkSelectedCompleted") > -1) {
                                    sessionStorage.removeItem("SelectMultipleRecords_" + _ViewId + _Key);
                                }
                                var HistoryKey = data.HistoryKey;
                                if (HistoryKey == null || HistoryKey == "") {
                                    HistoryKey = "HISTORYKEY";
                                }

                                window.location.href = "/Desktop/LoadDesktop/" + data.RecordId + "/false/FOLDERID/" + HistoryKey;
                            }
                            else if (_navigatetype == "OPENURLEXTERNAL") {
                                //RN #1873 Multiselect functionality added.
                                if (event.indexOf("AutoMarkSelectedCompleted") > -1) {
                                    sessionStorage.removeItem("SelectMultipleRecords_" + _ViewId + _Key);
                                }
                                if (data.ExternalUrlItems != null && data.ExternalUrlItems != undefined) {
                                    var sURL = data.ExternalUrlItems.sFilePath;
                                    var sTitle = data.ExternalUrlItems.sTitle;
                                    var sParams = data.ExternalUrlItems.sParams;

                                    if (sURL.indexOf("mailto:") == 0) {

                                        window.location = sURL;
                                    }
                                    else {
                                        var myWin = window.open(sURL, sTitle, sParams);
                                        if (window.focus) {
                                            //if EML will error on focus
                                            try { myWin.focus() }
                                            catch (Error) { }
                                        }
                                    }
                                    hideProgress();
                                }
                            }
                        }
                        else {

                            //RN #1873 Multiselect functionality added.
                            if (event.indexOf("AutoMarkSelectedCompleted") > -1) {
                                sessionStorage.removeItem("SelectMultipleRecords_" + _ViewId + _Key);
                            }
                            if (data.MessageBox.MessageBoxDisplay == true) {
                                hideProgress();
                                DisplayMessageBox(data.MessageBox);
                            }
                            else {
                                var desktopid = '@Util.GetSessionValue("DesktopId")';
                                var folderid = '@Util.GetSessionValue("FolderID")';
                                if (folderid == undefined || folderid == null || folderid == "")
                                    folderid = "FOLDERID";

                                var HistoryKey = _Key;
                                if (HistoryKey == undefined || HistoryKey == null || HistoryKey == "")
                                    HistoryKey = "HISTORYKEY";

                                if (desktopid != null && desktopid != undefined && desktopid != "") {
                                    window.location.href = "/Desktop/LoadDesktop/" + desktopid + "/true/" + folderid + "/" + HistoryKey;       //once do event has been called the isloadviefwfrom should be true to show the result..j
                                }
                                else {
                                    window.location.href = '/Login/LoginSubmit';
                                }
                                $("#PNL_MessageBox_Desktop").hide();
                            }
                        }
                    }
                    else {
                        hideProgress();
                    }

                },
                error: function (data) {
                    hideProgress();
                }
            })
        }
    </script>
    <script id="emailTemplate" type="text/x-kendo-template">
        <div class="new-mail">
            <!--Added Open,Clear buton on notification popup.. S1-->
            <button class="btnNotif" title="Open" style="top:0;right:30px;" onclick="OpenNotification(this, 'Notif');" style="display:none"><i class="fa fa-folder-open-o" aria-hidden="true"></i></button>
            <button class="btnNotif" title="Clear" style="top:0;right:15px;" onclick="ClearNotification(this, 'Notif');" style="display:none"><i class="fa fa-trash" aria-hidden="true"></i></button>
            <button class="btnShowAlwaysNotif" title="Close" style="top:0;right:0;" onclick="CloseNotification(this, 'Notif');style="display:none""><i class="fa fa-times" aria-hidden="true"></i></button>
            <ol class='timeline-listPop'>#= html #</ol>
        </div>
    </script>

    <script>
        $(document).ready(function () {
            //Handling sidebar menu width.. S1
            var is2Call = false;
            var DefSideBarLeft = localStorage.getItem('SideBar_Menu_Width');
            if (DefSideBarLeft != 200) {
                if ($(window).width() > 1000) {
                    $('#toggle_sidemenu_l').click();
                    is2Call = true;
                }
                else {
                    localStorage.setItem('SideBar_Menu_Width', 200);
                }
            }

            $('#toggle_sidemenu_l').click(function () {

                setTimeout(SideMenuStyle, 1000, is2Call);

            });

            //END Handling sidebar menu width

            var notification = $("#notification").data("kendoNotification");

            $("#showEmailNotification").click(function () {
                notification.show({
                    // title: "New E-mail",
                    html: ""
                    //message: "You have 1 new mail message!"
                }, "info");
            });

            //Collapsing layout menus on its options click.. SB
            //$('.dropdown-menu').click(function (e) {
            //    if (e.target.id != 'TaskMenuId' && e.target.id != 'AutomatorsMenuId' && $(this)[0].id != "AlertsDDL" && $(this)[0].id != "HistoryDDL" && e.target.parentElement.id != 'TaskMenuId' && e.target.parentElement.id != 'AutomatorsMenuId') {
            //        if (e.target.tagName == 'A' || e.target.tagName == 'LI') {
            //            $('body').click();
            //        }
            //    }
            //});
            GetQuickSearchMenu();
        });

        //Handling sidebar menu width.. S1
        function SideMenuStyle(is2Call) {
            //debugger;
            var sidebarLeftWidth = $('#sidebar_left').width();
            if (!is2Call || sidebarLeftWidth != localStorage.getItem('SideBar_Menu_Width')) {
                if (sidebarLeftWidth < 200) {
                    sidebarLeftWidth = 60;
                }
                localStorage.setItem('SideBar_Menu_Width', sidebarLeftWidth);
            }
        }

        var spinnerVisible = false;
        function showProgress() {
            if (!spinnerVisible) {
                $("div#spinnertool").fadeIn("fast");
                spinnerVisible = true;
            }
        };
        function hideProgress() {
            if (spinnerVisible) {
                var spinner = $("div#spinnertool");
                spinner.stop();
                spinner.fadeOut("fast");
                spinnerVisible = false;
            }
        };
    </script>
    <div id="spinnertool" data-keyboard="false" data-backdrop="static" style="z-index:1;">
        <div style="padding: 10%; color: #4C4A49;">
            <i class="fa fa-circle-o-notch fa-spin" style="font-size:25px;"></i><br>
            <span>Loading</span>
        </div>
    </div>

    @RenderSection("scripts", required: false)
</body>
</html>
