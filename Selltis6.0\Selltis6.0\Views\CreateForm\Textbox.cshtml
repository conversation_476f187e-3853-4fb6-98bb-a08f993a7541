﻿@model Selltis.MVC.Models.ControlAttributes
@using Kendo.Mvc.UI;
@using Selltis.BusinessLogic;
@using Selltis.Core;
@{
    clData goData = (clData)Util.GetInstance("data");
    clTransform goTR = (clTransform)Util.GetInstance("tr");
    var sPrefix = Model.field.Replace("NDB_", "").Substring(0, 4);

    string clslabeltoggled = "fg-line";
    string EMLIcon = Model.IconName;
    string onclickdata = "";
    string cursorpointer = "default";
    string ModelValue = "";
    if (!string.IsNullOrEmpty(Model.value))
    {
        ModelValue = System.Text.RegularExpressions.Regex.Replace(Model.value, "<.*?>", String.Empty);
    }
    if (!string.IsNullOrEmpty(ModelValue) || Model.bShowfloatingLable == "1")
    {
        clslabeltoggled = "fg-line fg-toggled";
    }
    if (sPrefix == "EML_")
    {
        //EMLIcon = "fa fa-envelope";
        onclickdata = "redirectToEmailApp()";
        cursorpointer = "pointer";
    }
    else
    {
        EMLIcon = "";
    }

    string Scripts = Model.Scripts;

    //To set max length to TextBox..J
    string sInfo = goData.GetFieldInfo(Model.TableName, Util.GetActualField(Model.field.ToLower().Replace("specialsymbolexist", "#").Replace("specialsymboldollar", "$")));
    string sLen = goTR.ExtractString(sInfo, 5);
    int par_iValid = 4;
    int MaxLength = Convert.ToInt32(goTR.StringToNum(sLen, "", ref par_iValid));

    if (Model.field.ToLower().Replace("specialsymbolexist", "#").Replace("specialsymboldollar", "$").StartsWith("ndb_"))
    {
        MaxLength = 500;
    }

    string sStyle = "min-height: 40px !important;";

    if (Model.IsHeaderControl)
    {
        sStyle = "";
    }
}

@if (goData.IsFieldSystem(Model.field.ToLower().Replace("specialsymbolexist", "#").Replace("specialsymboldollar", "$"), Model.TableName))
{
    <div class="input-group fg-float col-md-12" style="@sStyle">
        @if (!string.IsNullOrEmpty(EMLIcon))
        {
            <span class="input-group-addon" style="cursor:@cursorpointer" onclick="@onclickdata"><i class="@EMLIcon"></i></span>
        }
        else
        {
            <span class="input-group-addon" style="cursor:@cursorpointer" onclick="@onclickdata"></span>
        }

        <div class="@clslabeltoggled">
            @Html.TextBox(Model.field, ModelValue, new { @class = "form-control input-sm", @style = "display:block", @title = Model.title, @onblur = "txtBoxOnBlur(this)", @onchange = "textboxvaluechanged(this,'" + Model.Scripts + "','" + ModelValue + "')", @disabled = "disabled", @tabindex = @Model.ControlTabIndex, AutoComplete = "off" })
            <label id="<EMAIL>" style="color:@Model.lblcolor;display: @Model.display;" class="fg-label">@Html.Raw(HttpUtility.HtmlDecode(@Model.label))</label>
        </div>
    </div>

}
else if (Model.ControlState != null && Model.ControlState.FieldPropertiy.State != null && (Model.ControlState.FieldPropertiy.State == 1 || Model.ControlState.FieldPropertiy.State == 4))
{
    <div class="input-group fg-float col-md-12" style="@sStyle">
        @if (!string.IsNullOrEmpty(EMLIcon))
        {
            <span class="input-group-addon" style="cursor:@cursorpointer" onclick="@onclickdata"><i class="@EMLIcon"></i></span>
        }
        else
        {
            <span class="input-group-addon" style="cursor:@cursorpointer" onclick="@onclickdata"></span>
        }
        <div class="@clslabeltoggled">
            @Html.TextBox(Model.field, ModelValue, new { @class = "form-control input-sm", @style = "display:block", @title = Model.title, @readonly = "readonly", @onblur = "txtBoxOnBlur(this)", @onchange = "textboxvaluechanged(this,'" + Model.Scripts + "','" + ModelValue + "')", @tabindex = @Model.ControlTabIndex, AutoComplete = "off" })
            <label id="<EMAIL>" style="color:@Model.lblcolor;display: @Model.display;" class="fg-label">@Html.Raw(HttpUtility.HtmlDecode(@Model.label))</label>
        </div>
    </div>
}
else if (Model.ControlState != null && Model.ControlState.FieldPropertiy.State != null && (Model.ControlState.FieldPropertiy.State == 2))//Invisible
{
    <div class="input-group fg-float col-md-12" id="<EMAIL>" style="@sStyle;display: @Model.display">
        @if (!string.IsNullOrEmpty(EMLIcon))
        {
            <span class="input-group-addon" style="cursor:@cursorpointer" onclick="@onclickdata"><i class="@EMLIcon"></i></span>
        }
        else
        {
            <span class="input-group-addon" style="cursor:@cursorpointer" onclick="@onclickdata"></span>
        }
        <div class="@clslabeltoggled">
            @Html.TextBox(Model.field, ModelValue, new { @class = "form-control input-sm", @style = "display:none", @title = Model.title, @onblur = "txtBoxOnBlur(this)", @onchange = "textboxvaluechanged(this,'" + Model.Scripts + "','" + ModelValue + "')", @tabindex = @Model.ControlTabIndex, AutoComplete = "off" })
            <label id="<EMAIL>" style="color:@Model.lblcolor;display: @Model.display;" class="fg-label">@Html.Raw(HttpUtility.HtmlDecode(@Model.label))</label>
        </div>
    </div>


}
else
{
    <div class="input-group fg-float col-md-12" style="@sStyle">
        @if (!string.IsNullOrEmpty(EMLIcon))
        {
            <span class="input-group-addon" style="cursor:@cursorpointer" onclick="@onclickdata"><i class="@EMLIcon"></i></span>
        }
        else
        {
            <span class="input-group-addon" style="cursor:@cursorpointer" onclick="@onclickdata"></span>
        }
        <div class="@clslabeltoggled">


            @if (sPrefix == "TXT_")
            {
                @Html.TextBox(Model.field, ModelValue, new { @class = "form-control input-sm", @style = "display:block", @title = Model.title, @onblur = "txtBoxOnBlur(this)", @onchange = "textboxvaluechanged(this,'" + Model.Scripts + "','" + ModelValue + "')", @maxlength = "" + MaxLength + "", @tabindex = @Model.ControlTabIndex, AutoComplete = "off" })
                <label id="<EMAIL>" style="color:@Model.lblcolor;display: @Model.display;" class="fg-label">@Html.Raw(HttpUtility.HtmlDecode(@Model.label))</label>
            }
            else
            {
                @Html.TextBox(Model.field, ModelValue, new { @class = "form-control input-sm", @style = "display:block", @title = Model.title, @onblur = "txtBoxOnBlur(this)", @onchange = "textboxvaluechanged(this,'" + Model.Scripts + "','" + ModelValue + "')", @tabindex = @Model.ControlTabIndex, AutoComplete = "off" })
                <label id="<EMAIL>" style="color:@Model.lblcolor;display: @Model.display;" class="fg-label">@Html.Raw(HttpUtility.HtmlDecode(@Model.label))</label>
            }


            @*@Html.TextBox(Model.field, ModelValue, new { @class = "form-control input-sm", @style = "display:block", @title = Model.title, @onblur = "txtBoxOnBlur(this)", @onchange = "textboxvaluechanged(this)", @maxlength = "" + MaxLength + "" })
                <label style="color:@Model.lblcolor;display: @Model.display;" class="fg-label">@Html.Raw(HttpUtility.HtmlDecode(@Model.label))</label>*@
        </div>
    </div>

}

@if (sPrefix == "EML_")
{
    if (Model.ControlState != null && Model.ControlState.FieldPropertiy.State != null && (Model.ControlState.FieldPropertiy.State != 2))//Invisible
    {
        @*<span class="input-group-addon" onclick="sendMail('@ModelValue')" style="cursor:pointer;"  title="Send email using default e-mail client">
                <i class="fa fa-envelope" style="color: #3498DB; "></i>
            </span>*@
    }
}



@*@(Html.Kendo().TextBox()
    .Name(Model.field)
        .Value(ModelValue)
        .HtmlAttributes(new { @title = Model.title, @onchange = "textboxvaluechanged(this)" })
    )*@



@if (Model.field == "TXT_COMPANYNAME" && Model.TableName == "CO") //TXT_ADDRMAILING
{
    //Below code is to implement autocompalte address from google places
    <script>
        $(document).ready(function () {
            // Wait for Google Maps API to load before initializing Places Autocomplete
            function waitForGoogleMapsPlaces() {
                if (typeof google !== 'undefined' && typeof google.maps !== 'undefined' && typeof google.maps.places !== 'undefined') {
                    initializeGooglePlaces();
                } else {
                    setTimeout(waitForGoogleMapsPlaces, 100);
                }
            }

            function initializeGooglePlaces() {
                google.maps.event.addDomListener(document.getElementById('TXT_COMPANYNAME'), 'keyup', function () {

                    var input = document.getElementById('TXT_COMPANYNAME');
                    var places = new google.maps.places.Autocomplete(input);

                ///***********

                google.maps.event.addListener(places, 'place_changed', function () {
                    var address = '';
                    var route = '';
                    var postcode = '';
                    var locality = '';
                    var state = '';
                    var country = '';
                    var place = places.getPlace();
                    var AddressComponents = place.address_components;
                    //debugger
                    var _name = place.name;
                    var _website = place.website;

                     //debugger
                    for (var i = 0; i < AddressComponents.length; i++) {
                        var componentType = AddressComponents[i].types[0];
                        switch (componentType) {
                            //For Street Number
                            case "street_number": {
                                address = `${AddressComponents[i].long_name} ${address}`;
                                break;
                            }
                            //For Address
                            case "route": {
                                route = AddressComponents[i].short_name;
                                break;
                            }
                            //For PinCode
                            case "postal_code": {
                                postcode = `${AddressComponents[i].long_name}${postcode}`;
                                break;
                            }
                            //For PinCode
                            case "postal_code_prefix": {
                                if (postcode == '') {
                                    postcode = AddressComponents[i].long_name;
                                }
                                break;
                            }
                            //For City
                            case "locality": {
                                locality = AddressComponents[i].long_name;
                                break;
                            }
                            //For City
                            case "administrative_area_level_2": {
                                if (locality == '') {
                                    locality = AddressComponents[i].long_name;
                                }
                                break;
                            }
                            //For State
                            case "administrative_area_level_1": {
                                state = AddressComponents[i].short_name;
                                break;
                            }
                            //For Country
                            case "country": {
                                country = AddressComponents[i].long_name;
                                break;
                            }
                        }
                    }
                    //Set the Textbox values

                    if ($('#TXT_ADDRESSLINE1').length > 0 && $('#TXT_CITY').length > 0 && $('#TXT_STATE').length > 0 && $('#TXT_ZIPCODE').length > 0) {

                        $('#TXT_ADDRESSLINE1').val(address + " " + route);
                        $('#TXT_CITY').val(locality);
                        $('#TXT_STATE').val(state);
                        $('#TXT_ZIPCODE').val(postcode);
                        $('#TXT_COMPANYNAME').val(_name);
                        $('#TXT_WEBSITEURL').val(_website);
                        $('#TXT_ADDRESSLINE1').parent().removeClass('fg-line');
                        $('#TXT_ADDRESSLINE1').parent().addClass('fg-line fg-toggled');
                        $('#TXT_CITY').parent().removeClass('fg-line');
                        $('#TXT_CITY').parent().addClass('fg-line fg-toggled');
                        $('#TXT_STATE').parent().removeClass('fg-line');
                        $('#TXT_STATE').parent().addClass('fg-line fg-toggled');
                        $('#TXT_ZIPCODE').parent().removeClass('fg-line');
                        $('#TXT_ZIPCODE').parent().addClass('fg-line fg-toggled');

                    }
                    else {
                        $('#TXT_COMPANYNAME').val(_name);
                        $('#TXT_WEBSITEURL').val(_website);
                        $('#TXT_CITYMAILING').val(locality);
                        $('#TXT_STATEMAILING').val(state);
                        $('#TXT_ZIPMAILING').val(postcode);
                        $('#TXT_COUNTRYMAILING').val(country);
                        $('#TXT_ADDRMAILING').val(address + " " + route);
                        $('#TXT_ADDRMAILING').parent().removeClass('fg-line');
                        $('#TXT_ADDRMAILING').parent().addClass('fg-line fg-toggled');
                        $('#TXT_CITYMAILING').parent().removeClass('fg-line');
                        $('#TXT_CITYMAILING').parent().addClass('fg-line fg-toggled');
                        $('#TXT_STATEMAILING').parent().removeClass('fg-line');
                        $('#TXT_STATEMAILING').parent().addClass('fg-line fg-toggled');
                        $('#TXT_ZIPMAILING').parent().removeClass('fg-line');
                        $('#TXT_ZIPMAILING').parent().addClass('fg-line fg-toggled');
                        $('#TXT_COUNTRYMAILING').parent().removeClass('fg-line');
                        $('#TXT_COUNTRYMAILING').parent().addClass('fg-line fg-toggled');
                    }




                    //$('#TXT_COMPANYNAME').val(_name);
                    //$('#TXT_WEBSITEURL').val(_website);
                    ////$('#TXT_ADDRESSLINE1').val(address + " " + route);
                    ////$('#TXT_CITY').val(locality);
                    ////$('#TXT_STATE').val(state);
                    ////$('#TXT_ZIP').val(postcode);
                    //$('#TXT_ADDRMAILING').val(address + " " + route);
                    //$('#TXT_CITYMAILING').val(locality);
                    //$('#TXT_STATEMAILING').val(state);
                    //$('#TXT_ZIPMAILING').val(postcode);
                    //$('#TXT_COUNTRYMAILING').val(country);
                    //$('#TXT_ADDRMAILING').parent().removeClass('fg-line');
                    //$('#TXT_ADDRMAILING').parent().addClass('fg-line fg-toggled');
                    //$('#TXT_CITYMAILING').parent().removeClass('fg-line');
                    //$('#TXT_CITYMAILING').parent().addClass('fg-line fg-toggled');
                    //$('#TXT_STATEMAILING').parent().removeClass('fg-line');
                    //$('#TXT_STATEMAILING').parent().addClass('fg-line fg-toggled');
                    //$('#TXT_ZIPMAILING').parent().removeClass('fg-line');
                    //$('#TXT_ZIPMAILING').parent().addClass('fg-line fg-toggled');
                    //$('#TXT_COUNTRYMAILING').parent().removeClass('fg-line');
                    //$('#TXT_COUNTRYMAILING').parent().addClass('fg-line fg-toggled');
              
                });

                ///***********

                });
            }

            waitForGoogleMapsPlaces();
        });

    </script>


}

<style>

    .input-sm {

        padding : 1px 0px 0px 0px !important;
    }

</style>

<script>

    //function textboxvaluechanged(obj, scripts, oldValue) {
    //    var id = obj.id;
    //    var prefix = id.substring(0, 4);
    //    if (prefix == "CUR_") {
    //        //debugger;
    //        var value = $('#' + id).val();
    //        //value = parseFloat(value.replace('$', ''));
    //        //if (isNaN(value))
    //        //    value = 0;
    //        //value = value.toFixed(2);
    //        //var index = value.toString().indexOf('.');
    //        //if (index >= 0) {
    //        //    var substring = value.toString().substring(value.toString().indexOf('.'));
    //        //    if (substring.length < 3) {
    //        //        value = value.toString() + "0";
    //        //    }
    //        //}
    //        //else {
    //        //    value = value.toString() + ".00";
    //        //}
    //        //$('#' + id).val('$' + value);
    //    }


    //    if (scripts != undefined && scripts != "") {
    //        RunScript(scripts);
    //    }


    //}

    function textboxvaluechanged(obj, scripts, oldValue) {
        var id = obj.id;
        var prefix = id.substring(0, 4);


        if (prefix == "CUR_" || prefix == "SR__") {
            let strRepalce = document.getElementById(id).value.replace(/[^\d.-]/g, '');
            document.getElementById(id).value = strRepalce;
            var value = $('#' + id).val();

            if (isNaN(value)) {

                return false;
            }
            else {
                if (value.match("^[1-9][\.\d]*(,\d+)?$")) {

                    return value;
                } else {

                    return false;
                }
            }


        }

        if (prefix == "SI__" || prefix == "LI__" || prefix == "INT_") {
            let strRepalce = document.getElementById(id).value.replace(/[^\d.-]/g, '');
            document.getElementById(id).value = strRepalce;
            var value = $('#' + id).val();
            if (isNaN(value)) {

                return false;
            }
            else {

                var validNumber = new RegExp(/^(0|[1-9]\d*)$/);
                if (validNumber.test(value)) {
                    return value;
                } else {
                    $('#' + id).val("0");
                    return false;
                }
            }

        }

        if (scripts != undefined && scripts != "") {
            RunScript(scripts);
        }


    }


    $("input[type='text']").focus(function (e) {

        var clsName = $(this)[0].className;
        var id = $(this)[0].id;
        var value = $(id).val();

        var prefix = id.substring(0, 4);
        if (prefix == "CUR_" || prefix == "SR__") {


        }
        if (prefix == "SI__" || prefix == "LI__" || prefix == "INT_") {

            document.getElementById(id).type = "number";


        }

        if (id.indexOf("url_") == -1) {
            if (clsName.indexOf('k-input') > -1) {
                $($(this).parent().parent().parent().parent()).removeClass('fg-toggledDTE fg-toggledDTEcontrol');
                $($(this).parent().parent().parent().parent()).addClass('fg-toggledDTE fg-toggledDTEcontrol');
                $($(this).parent()).css("boder", "red");
            }
            else {
                $($(this).parent()).removeClass('fg-toggled fg-toggledcontrol');
                $($(this).parent()).addClass('fg-toggled fg-toggledcontrol');
                $($(this).parent()).css("boder", "red");
            }
        }
    });
    //$("input[type='text']").focus(function (e) {
    //    $($(this).parent()).addClass('fg-toggled');
    //    $($(this).parent()).addClass('fg-toggledcontrol');
    //});

    $("input[type='text']").focus(function (e) {
        var clsName = $(this)[0].className;
        var id = $(this)[0].id;
        if (id.indexOf("url_") == -1) {
            if (clsName.indexOf('k-input') > -1) {
                $($(this).parent().parent().parent().parent()).removeClass('fg-toggledDTE fg-toggledDTEcontrol');
                $($(this).parent().parent().parent().parent()).addClass('fg-toggledDTE fg-toggledDTEcontrol');
                $($(this).parent()).css("boder", "red");
            }
            else {
                $($(this).parent()).removeClass('fg-toggled fg-toggledcontrol');
                $($(this).parent()).addClass('fg-toggled fg-toggledcontrol');
                $($(this).parent()).css("boder", "red");
            }
        }
    });
    $(".k-input").blur(function (e) {
        var value = $(this).val();
        $($(this).parent().parent().parent().parent()).removeClass('fg-toggledDTEcontrol');
        value = value.trim();
        if (value == null || value == '' || value == undefined) {
            $($(this).parent().parent().parent().parent()).removeClass('fg-toggledDTE');
            $(this).val('');
        }
    });

    function redirectToEmailApp() {
   
        var email = document.getElementById("EML_EMAIL").value;
        if (email != '' || email != null) {
            var mailtoURL = "mailto:" + email;
            window.location.href = mailtoURL;
        }
       
    }
</script>