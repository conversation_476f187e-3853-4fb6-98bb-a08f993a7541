﻿@model Selltis.MVC.Models.CredentialsModel
@{
    ViewBag.Title = "Selltis - Login";
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link type="image/x-icon" href="~/Content/Images/Selltis16.ico" rel="shortcut icon" />

    <title>Selltis - Login</title>
    <!--<link rel="shortcut icon" href="/img/ico/favicon.png" />-->
    <!-- CSS -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" type="text/css" />
    <link href="@Url.Content("~/Content/themes/Selltis/css/bootstrap.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Url.Content("~/Content/themes/Selltis/css/jasny-bootstrap.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Url.Content("~/Content/themes/Selltis/css/font-awesome.css")" rel="stylesheet" type="text/css" />
    <link href="@Url.Content("~/Content/themes/Selltis/css/daterangepicker-bs3.css")" rel="stylesheet" type="text/css" />
    <link href="@Url.Content("~/Content/themes/Selltis/css/datepicker.css")" rel="stylesheet" type="text/css" />
    <link href="@Url.Content("~/Content/themes/Selltis/css/select2.css")" rel="stylesheet" type="text/css" />
    <link href="@Url.Content("~/Content/themes/Selltis/css/select2-bootstrap.css")" rel="stylesheet" type="text/css" />
    <link href="@Url.Content("~/Content/themes/Selltis/css/uniform.default.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Url.Content("~/Content/themes/Selltis/css/jquery.dataTables.min.css")" rel="stylesheet" type="text/css" />
    <link href="@Url.Content("~/Content/themes/Selltis/css/ark.css")" rel="stylesheet" type="text/css" />

    @*<link href="http://netdna.bootstrapcdn.com/font-awesome/4.0.3/css/font-awesome.css" rel="stylesheet" />
        <link href="http://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.0/css/jquery.dataTables.min.css" rel="stylesheet" type="text/css" />*@

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
            <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
            <script src="https://oss.maxcdn.com/libs/respond.js/1.3.0/respond.min.js"></script>
        <![endif]-->

    <style type="text/css">
        /*body.cover {
            background: transparent url("Content/themes/Selltis/images/background.jpg") repeat fixed 0 0 / cover;
        }*/

        body {
            background-color: #ffffff;
            color: #666666;
            font-family: "Open Sans",Arial,sans-serif;
            font-size: 13px;
            line-height: 1.5;
        }

        .login-wrap {
            margin: 0 auto;
            padding-top: 70px;
            width: 350px;
        }

            .login-wrap .brand {
                color: #fff;
                display: block;
                font-size: 22px;
                margin: 0 auto;
                text-align: center;
                text-decoration: none;
                text-shadow: 1px 1px 3px rgba(150, 150, 150, 0.7);
            }

            .login-wrap .panel {
                background-color: #ffffff;
                border-color: rgba(255, 255, 255, 0.2);
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                margin-top: 10px;
                padding: 15px 20px;
            }

        .panel {
            border-radius: 2px;
        }

            .panel .panel-heading {
                border-top-left-radius: 1px;
                border-top-right-radius: 1px;
            }

        .panel-heading {
            border-bottom: 1px solid transparent;
            border-top-left-radius: 3px;
            border-top-right-radius: 3px;
            padding: 10px 15px;
        }

        .panel-body {
            padding: 15px;
        }

        .login-wrap .panel .panel-title {
            font-weight: normal;
        }

        .panel-title {
            color: inherit;
            font-size: 16px;
            margin-bottom: 0;
            margin-top: 0;
        }

        .form-control {
            border: 1px solid #eeeeee;
            border-radius: 2px;
            box-shadow: none;
        }

        .form-control {
            background-color: #fff;
            background-image: none;
            color: #555;
            display: block;
            font-size: 14px;
            /*height: 34px;*/
            line-height: 1.42857;
            padding: 6px 12px;
            transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
            width: 90%;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .btn.btn-primary {
            background-color: #5b98cc;
            color: #fff;
        }

        .btn {
            border: medium none;
            border-radius: 2px;
            font-size: inherit;
            position: relative;
            margin-top: 10px;
        }

        .btn {
            -moz-user-select: none;
            background-image: none;
            cursor: pointer;
            display: inline-block;
            font-weight: normal;
            line-height: 1.42857;
            margin-bottom: 0;
            padding: 6px 12px;
            text-align: center;
            vertical-align: middle;
            white-space: nowrap;
        }

        a {
            color: #428bca;
        }

        a {
            color: #428bca;
            text-decoration: none;
        }

        /*required field style*/
        /*input:required {
            box-shadow: 4px 4px 20px rgba(200, 0, 0, 0.85);
        }*/

        /*input:required:focus {
            border: 1px solid blue;
            outline: none;

        }

        input:required:hover {
            opacity: 1;
        }*/
        /*.field:valid {
            border-color: black;
        }

        .field:invalid {
            border-color: blue;
        }*/
        div#spinnertool {
            display: none;
            width: 200px;
            height: 125px;
            position: fixed;
            top: 50%;
            left: 50%;
            margin-left: -100px !important;
            margin-top: -62.5px !important;
            /*background: url(spinner.gif) no-repeat center #fff;*/
            text-align: center;
            /*padding: 10px;*/
            border: 2px solid #428bca;
            margin-left: -50px;
            margin-top: -50px;
            z-index: 9999;
            /*overflow: auto;*/
            /*font-weight: 800;*/
            /*color: #428bca;*/
            border-radius: 10px;
            background-color: #4C4A49;
        }
    </style>
</head>

<body class="cover" onkeyup="checkSubmit(event)">
    <div class="login-wrap">
        <span class="brand">
            <img src="@Url.Content("~/Content/themes/Selltis/images/logo.png")" alt="logo.png" />
        </span>
        <div class="panel">
            <div class="panel-heading">
                <h3 class="panel-title">Sign In</h3>
            </div>
            <div class="panel-body">
                <label style="color:red">@ViewBag.Error</label>
                <form action="/Login/LoginSubmit" method="post">
                    <div id="divLogin">

                        @if (ViewBag.SSO == false || ViewBag.SSO == null)
                        {
                            <div class="form-group">
                                @Html.HiddenFor(m => m.TimeZoneOffset)
                                @Html.TextBoxFor(m => m.UserName, new { @class = "form-control", id = "exampleInputEmail1", @placeholder = "Enter Username", autofocus = "autofocus" })
                                @*@Html.TextBoxFor(m => m.UserName, new { @class="form-control", @type="email", id="exampleInputEmail1", @placeholder="Enter email"})*@
                                @*<input type="email" class="form-control" id="exampleInputEmail1" placeholder="Enter email">*@
                            </div>
                            <div class="form-group">
                                @Html.PasswordFor(m => m.Password, new { @class = "form-control", id = "exampleInputPassword1", @placeholder = "Password" })
                                @*<input type="password" class="form-control" id="exampleInputPassword1" placeholder="Password">*@
                            </div>
                            <input type="hidden" id="isChangePwdEnableBK" value="@ViewBag.isChangePwdEnable" />
                            <div id="divpwdChang" style="display:none">
                                <div id="divNewPwd" class="form-group">
                                    @Html.PasswordFor(m => m.NewPassword, new { @class = "form-control", id = "exampleInputChngNewPwd", @placeholder = "New Password" })
                                </div>
                                <div id="divConfmPwd" class="form-group">
                                    @Html.PasswordFor(m => m.ConfrmPassword, new { @class = "form-control", id = "exampleInputChngConfmPwd", @placeholder = "Confirm new Password" })
                                </div>
                            </div>
                            <div class="row">
                                <br />
                                <div style="margin-left:15px;">
                                    <button type="submit" class="btn btn-primary" style=" margin-top:-4px;">Login</button>
                                    <button id="btnCancel" type="button" class="btn btn-primary" style=" margin-top -4px; visibility hidden">Cancel</button>
                                    @*<label id="lblPwdPolicy" style="color: blue; cursor: pointer; text-decoration: underline; font-size: 10px;">Password Policy</label>*@
                                    <span id="lblPwdPolicy" style="color: black; cursor: pointer; font-size: 13px; margin-left:40px; visibility:hidden">Password Policy</span>
                                </div>
                            </div>
                            <div class="row" id="divPwdChangLBL">
                                <br />
                                <div class="col-md-6">
                                    @*<label id="lblChangePwd" style="color:blue; margin-left:15px; cursor:pointer; text-decoration:underline; font-size:10px;">Change Password</label>*@
                                    <p id="lblChangePwd" style="color: #428bca; cursor: pointer; font-size:13px;">Change Password</p>
                                </div>
                            </div>
                        }
                        else if (ViewBag.SSO == true)
                        {
                            if (!Request.IsAuthenticated)
                            {
                                <!-- If the user is not authenticated, display the sign-in button -->
                                <a href="@Url.Action("SignIn", "Login")" style="text-decoration: none;">
                                    <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="300px" height="50px" viewBox="0 0 3278 522" class="SignInButton">
                                    <style type="text/css">
                                        .fil0:hover {
                                            fill: #4B4B4B;
                                        }

                                        .fnt0 {
                                            font-size: 260px;
                                            font-family: 'Segoe UI Semibold', 'Segoe UI';
                                            text-decoration: none;
                                        }
</style>
                                    <rect class="fil0" x="2" y="2" width="3174" height="517" fill="black" />
                                    <rect x="150" y="129" width="122" height="122" fill="#F35325" />
                                    <rect x="284" y="129" width="122" height="122" fill="#81BC06" />
                                    <rect x="150" y="263" width="122" height="122" fill="#05A6F0" />
                                    <rect x="284" y="263" width="122" height="122" fill="#FFBA08" />
                                    <text x="470" y="357" fill="white" class="fnt0">Sign in with Microsoft</text>
                                </svg>
                                </a>
                                @*<div><a href="@Url.Action("SignIn", "Login")">Click here </a> to Sign-In with your Microsoft Account</div>*@
                            }
                            else
                            {
                                //If the user is authenticated with Microsoft but doesn't have valid login in Selltis, display the sign-out button
                                <a href="@Url.Action("SignOut", "Login")" style="text-decoration: none;">
                                    <svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="300px" height="50px" viewBox="0 0 3278 522" class="SignInButton">
                                    <style type="text/css">
                                        .fil0:hover {
                                            fill: #4B4B4B;
                                        }

                                        .fnt0 {
                                            font-size: 230px;
                                            font-family: 'Segoe UI Semibold', 'Segoe UI';
                                            text-decoration: none;
                                        }
</style>
                                    <rect class="fil0" x="2" y="2" width="3174" height="517" fill="black" />
                                    <rect x="150" y="129" width="122" height="122" fill="#F35325" />
                                    <rect x="284" y="129" width="122" height="122" fill="#81BC06" />
                                    <rect x="150" y="263" width="122" height="122" fill="#05A6F0" />
                                    <rect x="284" y="263" width="122" height="122" fill="#FFBA08" />
                                    <text x="470" y="357" fill="white" class="fnt0">Sign out from Microsoft</text>
                                </svg>
                                </a>
                                @*<div><a href="@Url.Action("SignOut", "Login")">Click here </a> to Sign-Out from your Microsoft Account</div>*@
                            }
                        }

                        @*<a href="forgot.html" style="margin-left:60px;" >Change Password</a><br />*@
                        <!--Don't have an account yet? <a href="register.html">Sign Up!</a>-->

                        <div>
                            <p id="lblMsg" style="color:red">@ViewBag.ErrorText</p>
                        </div>

                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JS -->
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/jquery.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/jquery-ui.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/jquery.uniform.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/bootstrap.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/jasny-bootstrap.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/jquery.autosize.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/moment.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/daterangepicker.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/bootstrap-datepicker.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/dropzone.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/jquery.easypiechart.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/jquery.flot.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/jquery.flot.pie.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/jquery.flot.stack.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/jquery.flot.resize.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/select2.min.js")" type="text/javascript"></script>
    <script src="@Url.Content("~/Content/themes/Selltis/scripts/fullcalendar.min.js")" type="text/javascript"></script>
    @*<script src="https://google-code-prettify.googlecode.com/svn/loader/prettify.js"></script>
        <script src="http://cdnjs.cloudflare.com/ajax/libs/datatables/1.10.0/js/jquery.dataTables.min.js"></script>
        <script src="@Url.Content("~/Content/themes/Selltis/scripts/ark.min.js")" type="text/javascript"></script>*@
    @*<script>
            //(function (i, s, o, g, r, a, m) {
            //    i['GoogleAnalyticsObject'] = r; i[r] = i[r] || function () {
            //        (i[r].q = i[r].q || []).push(arguments)
            //    }, i[r].l = 1 * new Date(); a = s.createElement(o),
            //        m = s.getElementsByTagName(o)[0]; a.async = 1; a.src = g; m.parentNode.insertBefore(a, m)
            //})(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

            //ga('create', 'UA-972204-19', 'around25.com');
            //ga('send', 'pageview');

        </script>*@
    <script>
        $(document).ready(function () {
            //window.history.forward(-1);
            var timezoneval = GetTimeZoneOffset();
            $("#TimeZoneOffset").val(timezoneval);
            //alert(timezoneval);
            //return;
            sessionStorage.clear();
            localStorage.setItem('SideBar_Menu_Width', 200);    //setting sidebar menu width deault..S1
        });
        function checkSubmit(e) {
            if (e && e.keyCode == 13) {
                document.forms[0].submit();
                //alert("SS");
            }
    }

    @*function SigninWithMicrosoft() {
            $.ajax({
                url: '@Url.Action("SignIn", "Login")',
                type: 'POST',
                data: {},
                crossDomain: true,
                dataType: 'jsonp',
                success: function (data) {
                    alert(data);
                }
            });
        }*@

        $('#lblChangePwd').click(function () {
            var lbltxt = $('#lblChangePwd').html();
            $('#divpwdChang').show();
            $('#btnCancel').css('visibility', 'visible');
            $('#lblPwdPolicy').css('visibility', 'visible');
            $('#divPwdChangLBL').hide();
            $('#exampleInputEmail1').val("");
            $('#exampleInputPassword1').val("");
            PwdEnable("true");
        });

        $('#btnCancel').click(function () {
            $('#divpwdChang').hide();
            $('#btnCancel').css('visibility', 'hidden');
            $('#lblPwdPolicy').css('visibility', 'hidden');
            $('#divPwdChangLBL').show();
            $('#lblMsg').html("");
            PwdEnable("false");
        });
        function PwdEnable(isChngePwd) {
            $.ajax({
                url: '@Url.Action("isChangePwdEnable", "Login")',
                type: 'GET',
                data: { 'isChngePwd': isChngePwd },
                success: function (data) {

                }
            });
        }

        $('#lblPwdPolicy').click(function () {
            $.ajax({
                url: '@Url.Action("Passwordpolicy", "Login")',
                type: 'GET',
                data: { 'e': 'policy' },
                success: function (data) {
                    $('#lblMsg').html(data);
                }
            });
        });

        $(document).ready(function () {
            $('#lblChangePwd').show();
            //$('#lblMsg').show();
            var xLblVal = $('#lblMsg').html();
            var isChangePwdEnableBK_Val = $('#isChangePwdEnableBK').val();

            if (xLblVal != "") {
                $('#divpwdChang').show();
                $('#btnCancel').css('visibility', 'visible');
                $('#lblPwdPolicy').css('visibility', 'visible');
                $('#divPwdChangLBL').hide();
                //$('#lblChangePwd').html("Cancel");
            }
            else if (isChangePwdEnableBK_Val == "true") {
                $('#divpwdChang').show();
                $('#btnCancel').css('visibility', 'visible');
                $('#lblPwdPolicy').css('visibility', 'visible');
                $('#lblChangePwd').hide();
                $('#lblMsg').hide();
                //$('#divPwdChangLBL').hide();
                //$('#lblChangePwd').html("Cancel");
            }
            else {
                $('#divpwdChang').hide();
                $('#btnCancel').css('visibility', 'hidden');
                $('#lblPwdPolicy').css('visibility', 'hidden');
                $('#divPwdChangLBL').show();
                //$('#lblChangePwd').html("Change Password");
            }


        });
        function GetTimeZoneOffset() {
            try {
                var d = new Date()
                return d.getTimezoneOffset()
            } catch (e) {
                alert(e.message);
                return 0;
            }

        }
    </script>
    <script>
        var spinnerVisible = false;
        function showProgress() {
            if (!spinnerVisible) {
                $("div#spinnertool").fadeIn("fast");
                spinnerVisible = true;
            }
        };
        function hideProgress() {
            if (spinnerVisible) {
                var spinner = $("div#spinnertool");
                spinner.stop();
                spinner.fadeOut("fast");
                spinnerVisible = false;
            }
        };
    </script>
    <div id="spinnertool" data-keyboard="false" data-backdrop="static">
        <div style="background-color: #428bca;color: #fff;padding: 3px;text-align: left;border-radius: 5px;">
            <span style="font-size: 13px;">Please wait..</span>
        </div>
        <div style="padding: 10%;color: #fff;">
            <i class="fa fa-circle-o-notch fa-spin" style="font-size:25px;"></i><br>
            <span>Loading</span>
        </div>
    </div>
</body>
</html>
