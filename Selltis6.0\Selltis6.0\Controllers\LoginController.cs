﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Selltis.MVC.Models;
using System.IO;
using Selltis.Core;
using Kendo.Mvc.UI;
using System.Data;
using Kendo.Mvc.Extensions;
using System.Web.Security;
using Selltis.BusinessLogic;
using System.Web.Routing;
using Selltis.MVC.Controllers;
using Microsoft.VisualBasic;
using System.Configuration;
using Microsoft.Owin.Security.Cookies;
using Microsoft.Owin.Security;
using Microsoft.Owin.Security.OpenIdConnect;

namespace Selltis.MVC.Controllers
{

    public class LoginController : Controller
    {
        // GET: Login
        private clDefaults goDef;
        private clMetaData goMeta;
        private clProject goP;
        private clTransform goTR;
        private clData goData;
        bool bSSOEnabled = false; //get this from site settings   

        ScriptManager scriptManager = new ScriptManager();

        public LoginController()
        {
            try
            {
                if (Util.GetSessionValue("SiteSettings") == null)
                {
                    clSettings.LoadSiteSettings();
                }

                string sUseSSO = clSettings.GetUseSSO();

                if (!string.IsNullOrEmpty(sUseSSO))
                {
                    bSSOEnabled = Convert.ToBoolean(sUseSSO);
                }
            }
            catch
            {
            }

        }

        public ActionResult Index()
        {

            return View();

        }

        public ActionResult Login()
        {

            goP = (clProject)Util.GetInstance("p");

            //Open last url last opened desktop in the same browser but in different tabs..J
            if (goP != null)
            {
                //Open last desktop in the same browser..J
                if (HttpContext.Request.Cookies["RefreshFilter"].Value != null)
                {
                    return Redirect(HttpContext.Request.Cookies["RefreshFilter"].Value.ToString());
                }
                else
                {
                    //temp pwd change
                    ViewBag.isChangePwdEnable = (Util.GetSessionValue("changePwdEnabled") == null) ? "false" : Util.GetSessionValue("changePwdEnabled").ToString();
                    if (bSSOEnabled)
                    {
                        ViewBag.SSO = true;
                        return View("SSOLogin");
                    }
                    else
                    {
                        return View();
                    }
                }
            }
            else
            {
                ViewBag.isChangePwdEnable = (Util.GetSessionValue("changePwdEnabled") == null) ? "false" : Util.GetSessionValue("changePwdEnabled").ToString();
                if (bSSOEnabled)
                {
                    ViewBag.SSO = true;

                    var AuthFailedError = Request.QueryString["errormessage"];

                    if (AuthFailedError != null)
                    {
                        //SSO Login Failed
                        ViewBag.Error = AuthFailedError;

                        return View("SSOLogin");
                    }
                    else if (!string.IsNullOrEmpty(ViewBag.Error))
                    {
                        //Selltis Login failed                       
                        return View("SSOLogin");
                    }

                    if (Request.IsAuthenticated)
                    {
                        var userClaims = User.Identity as System.Security.Claims.ClaimsIdentity;
                        string sUserEmail = userClaims?.FindFirst("preferred_username")?.Value;
                        return LoginWithMicrosoftAccount(sUserEmail);
                        //var _usergroups = userClaims?.FindAll("groups");

                        //if (_usergroups != null)
                        //{
                        //    //get the AAD User group Id from site settings
                        //    string sSuperUserGroupID = Convert.ToString(((DataTable)Util.GetSessionValue("SiteSettings")).Rows[0]["SuperUserGroupID"]);
                        //    var _usergroup = _usergroups.ToList<System.Security.Claims.Claim>().Find(a => a.Value == sSuperUserGroupID);

                        //    if (_usergroup != null)
                        //    {
                        //        //login with 'SYSTEM' LOGIN
                        //        return LoginWithMicrosoftAccount("<EMAIL>");
                        //    }

                        //    //get the AAD User group Id from site settings
                        //    string sAADUserGroupID = Convert.ToString(((DataTable)Util.GetSessionValue("SiteSettings")).Rows[0]["AADUserGroupID"]);
                        //    _usergroup = _usergroups.ToList<System.Security.Claims.Claim>().Find(a => a.Value == sAADUserGroupID);

                        //    if (_usergroup != null)
                        //    {
                        //        return LoginWithMicrosoftAccount(sUserEmail);
                        //    }
                        //    else
                        //    {
                        //        //User's Group Id is not matched with the group id configured in site settings
                        //        ViewBag.Error = "Error: User group is not valid. Please contact Selltis administrator.";
                        //        return View();
                        //    }
                        //}
                        //else
                        //{
                        //    //User's Group Id is not matched with the group id configured in site settings
                        //    ViewBag.Error = "Error: User group is not valid. Please contact Selltis administrator.";
                        //    return View();
                        //}
                    }

                    else
                    {
                        return View("SSOLogin");
                    }
                }
                else
                {
                    ViewBag.SSO = false;

                    return View();

                }

            }
            //return View();
        }

        public ActionResult LoginSubmit()
        {
            return View("NoneWorkArea");
        }
        //public ActionResult AdminLoginWithAnotheruser()
        //{
        //    goTR = (clTransform)Util.GetInstance("tr");
        //    string sDesktopID = Convert.ToString(Util.GetSessionValue("DesktopId"));
        //    string sID = "";
        //    string Key = "";

        //    ScriptManager _scriptManager = new ScriptManager();
        //    Models.clCreateForm Model = new Models.clCreateForm();

        //    ClUI goUI = new ClUI();

        //    if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
        //    {
        //        Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
        //    }

        //    if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
        //        sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();

        //    string sFileName = Strings.UCase(goTR.GetFileFromSUID(sID));
        //    object par_doCallingObject = Model.gsForm;

        //    if (sFileName != "US")
        //    {
        //        //goUI - Not Implemented
        //        goUI.NewWorkareaMessage("Please select a User record to Login.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
        //        return null;
        //    }

        //    clRowSet doUS = new clRowSet("US", clC.SELL_READONLY, "GID_ID=" + sID, "", "EML_EMAIL");
        //    if (doUS.GetFirst() == 1)
        //    {
        //        string sEmail = Convert.ToString(doUS.GetFieldVal("EML_EMAIL"));
        //        if (!string.IsNullOrEmpty(sEmail))
        //        {
        //            Session.Clear();
        //            Session.RemoveAll();
        //            Session.Abandon();
        //            FormsAuthentication.SignOut();
        //            Response.Cookies.Clear();

        //            //To restrict calling loaddesktop in refresh.If not it adds same item in hisotry panel..J
        //            if (HttpContext.Request.Cookies["RefreshFilter"] != null)
        //            {
        //                HttpContext.Request.Cookies["RefreshFilter"].Value = null;
        //            }

        //            //Util.SetSessionValue("Admin_Login_With_AnotherEmail", sEmail);

        //            return RedirectToAction("LoginWithMicrosoftAccount", "Login", new
        //            {
        //                sUserEmail = sEmail
        //            });
        //            //return sEmail;
        //        }
        //        else
        //        {
        //            goUI.NewWorkareaMessage(" You cannot lagin as selected user as email id is blank for this user.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
        //            return null;
        //        }

        //    }
        //    else
        //    {
        //        goUI.NewWorkareaMessage("Error in getting user record.", 0, "Selltis", "", "", "", "", "", "", "", ref par_doCallingObject);
        //        return null;
        //    }



        //}
        public JsonResult Admin_Login_As_AnotherUser()
        {
            goTR = (clTransform)Util.GetInstance("tr");

            string sID = "";
            string Key = "";

            if (Util.GetSessionValue("LastDesktopHistoryKey") != null)
            {
                Key = Util.GetSessionValue("LastDesktopHistoryKey").ToString();
            }

            if (Util.GetSessionValue(Key + "_" + "SelectedRecordID") != null)
                sID = Util.GetSessionValue(Key + "_" + "SelectedRecordID").ToString();

            string sFileName = Strings.UCase(goTR.GetFileFromSUID(sID));

            if (sFileName != "US")
            {
                return Json("Err1", JsonRequestBehavior.AllowGet);
            }

            clRowSet doUS = new clRowSet("US", clC.SELL_READONLY, "GID_ID=" + sID, "", "EML_EMAIL");
            if (doUS.GetFirst() == 1)
            {
                string sEmail = Convert.ToString(doUS.GetFieldVal("EML_EMAIL"));
                if (!string.IsNullOrEmpty(sEmail))
                {
                    Session.Clear();
                    Session.RemoveAll();
                    Session.Abandon();
                    FormsAuthentication.SignOut();
                    Response.Cookies.Clear();

                    if (HttpContext.Request.Cookies["RefreshFilter"] != null)
                    {
                        HttpContext.Request.Cookies["RefreshFilter"].Value = null;
                    }

                    return Json(new { redirectToUrl = Url.Action("LoginWithMicrosoftAccount", "Login", new { sUserEmail = sEmail }) }, JsonRequestBehavior.AllowGet);

                }
                else
                {
                    return Json("Err2", JsonRequestBehavior.AllowGet);
                }

            }
            else
            {
                return Json("Err3", JsonRequestBehavior.AllowGet);
            }



        }
        public ActionResult LoginWithMicrosoftAccount(string sUserEmail)
        {
            try
            {

                Util.ClearSessionValue("SiteSettings");

                string sCheckSiteSettings = clSettings.LoadSiteSettings();

                if (string.IsNullOrEmpty(sCheckSiteSettings) == false)
                {
                    ViewBag.Error = sCheckSiteSettings;//"Site settings for the site " + System.Web.HttpContext.Current.Request.Url.Host + " is not found";
                    ViewBag.SSO = true;
                    return View("SSOLogin");
                }

                bool isChangePasswordEnabled = Convert.ToBoolean(Util.GetSessionValue("changePwdEnabled"));
                clMembershipData oMP = new clMembershipData();

                clSelltisMembershipProviderNew _memberShip = new clSelltisMembershipProviderNew();
                bool sb = false;

                if (!string.IsNullOrEmpty(sUserEmail))
                {
                    string susername = "";
                    sb = _memberShip.ValidateUser_with_EmailId(sUserEmail, ref susername);
                    Util.SetSessionValue("username", susername);
                    Util.SetSessionValue("UserEmail", sUserEmail);
                }

                if (sb)
                {
                    // SetTimeZoneOffset(cred.TimeZoneOffset);
                    Util.SetSessionValue("LOGINRUNMODE", "MAIN");
                    return LoadDesktop();
                }
                else
                {
                    if (Util.GetSessionValue("bIsRedirectToAgreement") != null)
                    {
                        Util.ClearSessionValue("bIsRedirectToAgreement");
                        Util.SetSessionValue("LOGINRUNMODE", "MAIN");
                        return RedirectToAction("Agreement", "UserAgreement");
                    }

                    //  GetAuthenticationMessage(cred, oMP, isChangePasswordEnabled);
                    //temp pwd change                        
                    ViewBag.isChangePwdEnable = (Util.GetSessionValue("changePwdEnabled") == null) ? "false" : Util.GetSessionValue("changePwdEnabled").ToString();

                    if (bSSOEnabled)
                    {
                        //SignOut();
                        ViewBag.Error = "A valid login does not exist with the email '" + sUserEmail + "' in Selltis, so please contact Selltis administrator.";
                    }
                    ViewBag.SSO = true;
                    return View("SSOLogin");
                }
                //}
            }
            catch (Exception ex)
            {
                clCache.WriteLog(ex.Message + Environment.NewLine + ex.StackTrace);
                ViewBag.Error = ex.Message;
                ViewBag.SSO = true;
                return View("SSOLogin");
            }
        }

        [Selltis.MVC.RouteConfig.RefreshDetectFilter]
        [HttpPost]
        public ActionResult LoginSubmit(CredentialsModel cred)
        {
            try
            {

                Util.ClearSessionValue("SiteSettings");

                string sCheckSiteSettings = clSettings.LoadSiteSettings();

                if (string.IsNullOrEmpty(sCheckSiteSettings) == false)
                {
                    ViewBag.Error = sCheckSiteSettings;//"Site settings for the site " + System.Web.HttpContext.Current.Request.Url.Host + " is not found";
                    if (bSSOEnabled)
                    {
                        ViewBag.SSO = true;
                        return View("SSOLogin");
                    }
                    else
                    {
                        return View("Login");
                    }
                }

                bool isChangePasswordEnabled = Convert.ToBoolean(Util.GetSessionValue("changePwdEnabled"));
                clMembershipData oMP = new clMembershipData();


                if (!string.IsNullOrEmpty(cred.UserName))
                {
                    Util.SetSessionValue("username", cred.UserName);
                }


                if (isChangePasswordEnabled)
                {
                    return ChangePassword(cred, oMP, isChangePasswordEnabled);
                }
                else
                {
                    clSelltisMembershipProviderNew _memberShip = new clSelltisMembershipProviderNew();
                    bool sb = false;

                    if (!string.IsNullOrEmpty(cred.Email))
                    {
                        string susername = "";
                        sb = _memberShip.ValidateUser_with_EmailId(cred.Email, ref susername);
                        Util.SetSessionValue("username", susername);
                    }
                    else
                    {
                        sb = _memberShip.ValidateUser(cred.UserName, cred.Password);
                    }

                    //bool sb = _memberShip.ValidateUser(cred.UserName, cred.Password);
                    // bool sb = Membership.ValidateUser(cred.UserName, cred.Password);

                    if (sb)
                    {
                        SetTimeZoneOffset(cred.TimeZoneOffset);
                        Util.SetSessionValue("LOGINRUNMODE", "MAIN");
                        return LoadDesktop();
                    }
                    else
                    {

                        if (Util.GetSessionValue("bIsRedirectToAgreement") != null)
                        {
                            Util.ClearSessionValue("bIsRedirectToAgreement");
                            Util.SetSessionValue("LOGINRUNMODE", "MAIN");
                            return RedirectToAction("Agreement", "UserAgreement");
                        }

                        GetAuthenticationMessage(cred, oMP, isChangePasswordEnabled);
                        //temp pwd change                        
                        ViewBag.isChangePwdEnable = (Util.GetSessionValue("changePwdEnabled") == null) ? "false" : Util.GetSessionValue("changePwdEnabled").ToString();
                        if (bSSOEnabled)
                        {
                            ViewBag.SSO = true;
                            return View("SSOLogin");
                        }
                        else
                        {
                            return View("Login");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                clCache.WriteLog(ex.Message + Environment.NewLine + ex.StackTrace);
                ViewBag.Error = ex.Message;
                if (bSSOEnabled)
                {
                    ViewBag.SSO = true;
                    return View("SSOLogin");
                }
                else
                {
                    return View("Login");
                }
            }
        }

        //16042019 tckt #2770: Added two factor authentication..J
        [Selltis.MVC.RouteConfig.RefreshDetectFilter]
        [HttpPost]
        public ActionResult SubmitTwoFactorAuthCode(TwoFactorAuth twoFactorAuth)
        {
            goP = (clProject)Util.GetInstance("p");
            string UserID = goP.GetMe("ID");
            string AuthCode = "";
            string Message = "";

            clSelltisMembershipProviderNew _memberShip = new clSelltisMembershipProviderNew();
            bool sb = _memberShip.SubmitTwoFactorAuth(twoFactorAuth.AuthCode, UserID);

            //clRowSet _rowset = new clRowSet("US", 3, "GID_ID='" + UserID + "'", "", "TXT_Auth_Code, DTT_Auth_Verified_Date", 1);

            //AuthCode = Convert.ToString(_rowset.GetFieldVal("TXT_Auth_Code"));
            if (sb)
            {
                //Keep it in coockie for remember..J
                if (twoFactorAuth.RememberMe)
                {
                    Response.Cookies["AuthCode"].Expires = DateTime.Now.AddDays(30);
                }
                else
                {
                    Response.Cookies["AuthCode"].Expires = DateTime.Now.AddDays(-1);
                }

                Response.Cookies["AuthCode"].Value = AuthCode.Trim();

                return LoadDesktop("true");
            }
            else
            {
                TwoFactorAuth _twoFactorAuth = new TwoFactorAuth();
                Message = "Authentication code you entered is wrong";

                _twoFactorAuth.Message = Message;

                return View("TwoFactorAuth", _twoFactorAuth);
            }
        }

        public ActionResult TwoFactorAuth(string Message)
        {
            TwoFactorAuth _twoFactorAuth = new TwoFactorAuth()
            {
                Message = Message
            };

            return View(_twoFactorAuth);
        }

        private bool LoadSiteSettings()
        {
            string sHostName = Util.GetHostName();
            string myXMLfile = Server.MapPath("~/App_Data/SiteSettings/" + sHostName + "_SiteSettings.xml");

            if (!string.IsNullOrEmpty(myXMLfile))
            {
                DataSet ds = new DataSet();
                var fsReadXml = new FileStream(myXMLfile, FileMode.Open, FileAccess.Read);
                ds.ReadXml(fsReadXml);
                fsReadXml.Close();
                fsReadXml.Dispose();

                if (ds.Tables[0] != null)
                {
                    if (string.IsNullOrEmpty(ds.Tables[0].Rows[0]["ConnectionString"].ToString()) || string.IsNullOrEmpty(ds.Tables[0].Rows[0]["Logo"].ToString())
                    || string.IsNullOrEmpty(ds.Tables[0].Rows[0]["AttachmentsPath"].ToString()) || string.IsNullOrEmpty(ds.Tables[0].Rows[0]["AttachmentsTempPath"].ToString())
                    || string.IsNullOrEmpty(ds.Tables[0].Rows[0]["AttachmentsMaxFolderSize"].ToString()) || string.IsNullOrEmpty(ds.Tables[0].Rows[0]["AttachmentMaxFileSize"].ToString()))
                    {
                        return false;
                    }

                    Util.SetSessionValue("SiteSettings", ds.Tables[0]);
                    return true;
                }
                else
                {
                    return false;
                }

            }
            else
            {
                return false;
            }
        }

        private ActionResult ChangePassword(CredentialsModel cred, clMembershipData oMP, bool _isChangePwdEnabled)
        {
            if (!string.IsNullOrEmpty(cred.NewPassword))
            {

                if (cred.NewPassword != cred.ConfrmPassword)
                {
                    ViewBag.ErrorText = "The new password values do not match.";
                    if (bSSOEnabled)
                    {
                        ViewBag.SSO = true;
                        return View("SSOLogin");
                    }
                    else
                    {
                        return View("Login");
                    }
                }

                if (oMP.PasswordMeetsRequirements(cred.NewPassword, cred.Password, cred.UserName) == false)
                {
                    ViewBag.ErrorText = "Your new password does not meet the minimum requirements." + Constants.vbCrLf + Constants.vbCrLf + oMP.GetPasswordPolicy();
                    if (bSSOEnabled)
                    {
                        ViewBag.SSO = true;
                        return View("SSOLogin");
                    }
                    else
                    {
                        return View("Login");
                    }
                }


                if (oMP.ChangePassword(cred.UserName, cred.Password, cred.NewPassword) == true)
                {
                    clSelltisMembershipProviderNew _memberShip = new clSelltisMembershipProviderNew();
                    bool sb = _memberShip.ValidateUser(cred.UserName, cred.NewPassword); //cred.password changed to cred.NewPassword
                    if (sb)
                    {
                        return LoadDesktop();
                    }
                    else
                    {
                        //temp pwd change                        
                        if (Util.GetSessionValue("bIsRedirectToAgreement") != null)
                        {
                            Util.ClearSessionValue("bIsRedirectToAgreement");
                            return RedirectToAction("Agreement", "UserAgreement");
                        }

                        GetAuthenticationMessage(cred, oMP, _isChangePwdEnabled);
                        if (bSSOEnabled)
                        {
                            ViewBag.SSO = true;
                            return View("SSOLogin");
                        }
                        else
                        {
                            return View("Login");
                        }
                    }
                }
                else
                {
                    GetAuthenticationMessage(cred, oMP, _isChangePwdEnabled);
                    if (bSSOEnabled)
                    {
                        ViewBag.SSO = true;
                        return View("SSOLogin");
                    }
                    else
                    {
                        return View("Login");
                    }
                }
            }
            else
            {
                ViewBag.ErrorText = "New password should not be blank" + Constants.vbCrLf + Constants.vbCrLf + oMP.GetPasswordPolicy();
                if (bSSOEnabled)
                {
                    ViewBag.SSO = true;
                    return View("SSOLogin");
                }
                else
                {
                    return View("Login");
                }
            }
        }

        public ActionResult LoadDesktop(string IsFromTwoFactorAuth = "")
        {
            string Workarea = string.Empty;
            string LblHeader = string.Empty;
            string Desktopid = string.Empty;
            string Folderid = string.Empty;
            string FirstName = string.Empty;
            string LastName = string.Empty;

            //if (!string.IsNullOrEmpty(sUserName))
            //{
            //    Util.SetVar("username", sUserName);
            //}


            clInit Init = new clInit();

            goMeta = (clMetaData)Util.GetInstance("meta");
            goTR = (clTransform)Util.GetInstance("tr");
            goP = (clProject)Util.GetInstance("p");

            //16042019 tckt #2770: Added two factor authentication..J
            if (string.IsNullOrEmpty(IsFromTwoFactorAuth))
            {
                string Message = "";
                bool IsTwoFactAuthEnabled = false;
                clSelltisMembershipProviderNew _memberShip = new clSelltisMembershipProviderNew();
                _memberShip.TwoFactorAuthentication(ref IsTwoFactAuthEnabled, ref Message);

                if (IsTwoFactAuthEnabled)
                {
                    //If it is remembered upto 30days, then no need of 2-step authentication..
                    if (Request.Cookies["AuthCode"] == null)
                    {
                        return RedirectToAction("TwoFactorAuth", "Login", new { Message = Message });
                    }
                }
            }

            LoadPersonalOptSession();

            DataSet ds = new DataSet();
            ds = SidebarDesktopsMenu.GetMenuDataSet();

            string POP = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "", true);
            string OTH = goMeta.PageRead(goP.GetUserTID(), "OTH_INITIAL_STATES", "", true);

            string gsCompany = goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "COMPANYNAME");

            Workarea = GetWorkareaArgs();

            Util.SetSessionValue("DefaultDesktopId", Workarea);

            object doCallingObject = null;
            object par_oReturn = null;
            bool par_bRunNext = false;
            string par_sSections = null;

            if (!scriptManager.RunScript("DatabaseAfterLogin", ref doCallingObject, ref par_oReturn, ref par_bRunNext, ref par_sSections))
            {
            }

            string[] Sys_User = goP.GetUserName().Split(',');

            if (Sys_User[0] != null && string.IsNullOrEmpty(Sys_User[0].Trim()) == false)
            {
                FirstName = Sys_User[0].Trim()[0].ToString().ToUpper();
            }
            if (Sys_User[1] != null && string.IsNullOrEmpty(Sys_User[1].Trim()) == false)
            {
                LastName = Sys_User[1].Trim()[0].ToString().ToUpper();
            }

            Util.SetSessionValue("userlastname", FirstName + LastName);
            Util.SetSessionValue("lastname", Sys_User[1].Trim().ToString().ToUpper() + " ." + FirstName);
            Util.SetSessionValue("CompanyName", gsCompany);

            //show application versoin..J
            string AppVersion = System.Web.Configuration.WebConfigurationManager.AppSettings["AppVersion"];
            if (AppVersion != null)
            {
                Util.SetSessionValue("AppVersion", AppVersion);
            }
            else
            {
                AppVersion = "";
                Util.SetSessionValue("AppVersion", AppVersion);
            }

            //show timezone and it takes to make regional changes in personal options when we click on it..J
            string gsTimeZone = goTR.UTC_GetTimeZoneOffsetLabel();
            Util.SetSessionValue("TimeZone", gsTimeZone);


            SetMetaVisibleFlag();
            ///Chart/Dashboard
            //return RedirectToAction("Dashboard", "Chart");

            //tkt #1881 last login time does not registering in metadata.
            System.Data.SqlClient.SqlConnection par_oConnection = null;
            int par_iValid = 3;
            string par_sDelim = "|";

            string sDateTime = goTR.DateTimeToSysString(goTR.UTC_ServerToUTC(DateTime.Now), ref par_iValid, ref par_sDelim);
            goMeta.LineWrite(goP.GetUserTID(), "OTH_POINTERS", "LASTLOGIN_UTC_" + goP.GetProduct(), sDateTime, ref par_oConnection, "", "XX");
            //Always also log this line for querying last login under any product
            goMeta.LineWrite(goP.GetUserTID(), "OTH_POINTERS", "LASTLOGIN_UTC_XX", sDateTime, ref par_oConnection, "", "XX");

            switch (Workarea.ToUpper())
            {
                case "NONE":
                    return CheckTimeZone("noneworkarea");
                //return View("NoneWorkArea");
                case "LASTDESKTOP":
                    string lastDesktopData = goTR.StrRead(OTH, "LASTDESKTOP", "");
                    string[] data = lastDesktopData.Split('|');
                    if (data.Length > 1)
                    {
                        if (!string.IsNullOrEmpty(data[1]))
                        {
                            Util.ClearSessionValue("changePwdEnabled");
                            return CheckTimeZone("LoadDesktop", data[1], Folderid);
                            //return RedirectToAction("LoadDesktop", "Desktop", new { DesktopId = data[1], IsLoadFromSession = "false", FolderId = Folderid });
                        }
                        else
                        {
                            Util.ClearSessionValue("changePwdEnabled");
                            return CheckTimeZone("noneworkarea");
                            //return View("NoneWorkArea");
                        }
                    }
                    else
                    {
                        Util.ClearSessionValue("changePwdEnabled");
                        return CheckTimeZone("noneworkarea");
                        //return View("NoneWorkArea");
                    }
                default:
                    Util.ClearSessionValue("changePwdEnabled");
                    return CheckTimeZone("LoadDesktop", Workarea, Folderid);
                    //return RedirectToAction("LoadDesktop", "Desktop", new { DesktopId = Workarea, IsLoadFromSession = "false", FolderId = Folderid });
            }
        }

        [Selltis.MVC.RouteConfig.RefreshDetectFilter]
        private ActionResult CheckTimeZone(string ReturnActionResult, string DesktopId = "", string Folderid = "")
        {

            //Get desktop metadata of non shared desktop..
            //string DesktopMetaData = goMeta.PageRead("GLOBAL", DesktopId, "", true);
            string DesktopMetaData = goMeta.PageRead(goP.GetUserTID(), DesktopId, "", true);

            //Check shared value either '0' or '1', For global desktops shared value is '1' ..
            string isShared = "";
            if (!string.IsNullOrEmpty(DesktopMetaData))
            {
                isShared = goTR.StrRead(DesktopMetaData, "SHARED", "0");
            }
            else
            {
                isShared = "1";
            }

            //Get section either GLOBAL or USERID.. 
            string section = "";
            if (isShared == "0")
            {
                section = goP.GetUserTID();
            }
            else
            {
                section = "GLOBAL";
            }


            //need folder id to getrid out of error page..
            if (Folderid == "")
            {
                Folderid = "FOLDERID";
            }


            if (Util.GetSessionValue("RedirectToPage") != null)
            {
                if (!string.IsNullOrEmpty(Util.GetSessionValue("RedirectToPage").ToString()))
                {


                    if (ReturnActionResult.ToLower() == "noneworkarea")
                    {
                        Util.SetSessionValue("TimeZoneSuccessURL", "/Login/NoneWorkArea");
                    }
                    else
                    {
                        //System.Web.HttpContext.Current.Session["TimeZoneSuccessURL"] = "/Desktop/LoadDesktop/" + DesktopId + "/false/" + Folderid;
                        Util.SetSessionValue("TimeZoneSuccessURL", "/Desktop/LoadDesktop/" + DesktopId + "/false/" + Folderid + "/HISTORYKEY/" + section);
                    }


                    string s = Util.GetSessionValue("RedirectToPage").ToString();
                    string[] _params = s.Split('?');
                    string[] _values = _params[1].Split('&');

                    return RedirectToAction("LoadTimeZone", "Login", new { Locale = _values[0].Replace("locale=", ""), Offset = _values[1].Replace("offset=", "") });
                }
                else
                {



                    if (ReturnActionResult.ToLower() == "noneworkarea")
                        return View("NoneWorkArea");
                    else
                        return RedirectToAction("LoadDesktop", "Desktop", new { DesktopId = DesktopId, IsLoadFromSession = "false", FolderId = Folderid, Section = section });
                }
            }
            else
            {
                if (ReturnActionResult.ToLower() == "noneworkarea")
                    return View("NoneWorkArea");
                else
                    return RedirectToAction("LoadDesktop", "Desktop", new { DesktopId = DesktopId, IsLoadFromSession = "false", FolderId = Folderid, Section = section });
            }
        }
        private void GetAuthenticationMessage(CredentialsModel cred, clMembershipData oMP, bool _isChangePwdEnabled)
        {
            string _errorInfo = "";
            switch (oMP.GetAuthenticationMessage(cred.UserName, cred.Password))
            {

                case "1":
                    _errorInfo = "Your temporary password has expired.  Please contact your Selltis Administrator to obtain a valid password.";
                    break;
                case "2":
                    _errorInfo = "Your temporary password must be changed.  Please enter a new password.";
                    //ViewBag.ShowChangePwd = true;
                    //ShowChangePassword();

                    //temp pwd change                        
                    Util.SetSessionValue("changePwdEnabled", "true");
                    break;
                case "3":
                    _errorInfo = "Your user login has been deactivated.  Please contact your Selltis Administrator.";
                    break;
                case "4":
                    _errorInfo = "Your user login is inactive. If you suspect that this is in error, contact your system administrator or call Selltis.";
                    break;
                case "5":
                    _errorInfo = "Your new password does not meet the minimum requirements" + Constants.vbCrLf + Constants.vbCrLf + oMP.GetPasswordPolicy();
                    break;
                case "6":
                    _errorInfo = "Your password change attempt was not successful. Please try again.";
                    break;
                case "7":
                    _errorInfo = "Your password has expired. Please create a new one.";
                    //ViewBag.ShowChangePwd = true;
                    //ShowChangePassword();

                    //temp pwd change                        
                    Util.SetSessionValue("changePwdEnabled", "true");

                    break;
                default:
                    _errorInfo = "Your login attempt was not successful. Please try again.";
                    break;
            }

            //HttpContext.Current.Session("LASTLOGINFAILUREREASON")

            if (Util.GetSessionValue("LASTLOGINFAILUREREASON") != null && string.IsNullOrEmpty(Util.GetSessionValue("LASTLOGINFAILUREREASON").ToString()) == false)
            {
                _errorInfo = Util.GetSessionValue("LASTLOGINFAILUREREASON").ToString();
            }

            if (!_isChangePwdEnabled)
                ViewBag.Error = _errorInfo;
            else
                ViewBag.ErrorText = _errorInfo;
        }
        public void isChangePwdEnable(string isChngePwd)
        {
            Util.SetSessionValue("changePwdEnabled", isChngePwd);

        }
        public void SetMetaVisibleFlag()
        {
            string MetaVisible = "";
            goP = (clProject)Util.GetInstance("p");
            if (!goP.IsUserAdmin() & goP.IsUserAuthor() < 2)
                MetaVisible = "false";
            else
                MetaVisible = "true";
            Util.SetSessionValue("MetaVisible", MetaVisible);
        }

        public JsonResult Passwordpolicy(string e)
        {
            clMembershipData oMP = new clMembershipData();
            string strPwd = "";
            switch (e)
            {
                case "validate":
                    //string newP = Request.QueryString("n");
                    //string oldP = Request.QueryString("o");
                    //string user = Request.QueryString("u");
                    //Response.Write(oMP.PasswordMeetsRequirements(newP, oldP, user));
                    break;

                case "policy":
                    strPwd = oMP.GetPasswordPolicy();
                    break;

                default:
                    strPwd = "";
                    break;
            }

            return Json(strPwd, JsonRequestBehavior.AllowGet);
        }

        public void LoadPersonalOptSession()
        {
            goDef = (clDefaults)Util.GetInstance("def");
            goMeta = (clMetaData)Util.GetInstance("meta");
            goP = (clProject)Util.GetInstance("p");

            string DefaultValues = goDef.GetPersonalOptionDefs();
            string sVals = goMeta.PageRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", DefaultValues, false);
            Util.SetSessionValue("PersonalOptions", sVals);
        }

        public ActionResult Logout()
        {
            Session.Clear();
            Session.RemoveAll();
            Session.Abandon();
            FormsAuthentication.SignOut();
            Response.Cookies.Clear();

            //To restrict calling loaddesktop in refresh.If not it adds same item in hisotry panel..J
            if (HttpContext.Request.Cookies["RefreshFilter"] != null)
            {
                HttpContext.Request.Cookies["RefreshFilter"].Value = null;
            }

            if (bSSOEnabled)
            {
                return Redirect(Url.Action("SignOut", "Login"));
            }
            else
            {
                return RedirectToAction("Login", "Login");
            }

        }

        public string GetWorkareaArgs()
        {
            goMeta = (clMetaData)Util.GetInstance("meta");
            goP = (clProject)Util.GetInstance("p");
            string Desktop = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "STARTUPDESKTOP", goMeta.LineRead("GLOBAL", "WOP_WORKGROUP_OPTIONS", "STARTUPDESKTOP", "LASTOPEN", true), true);
            switch (Strings.UCase(Desktop))
            {
                case "NONE":
                    return "NONE";
                case "LASTOPEN":
                    return "LASTDESKTOP";
                default:
                    return Desktop;
            }
        }

        private void SetTimeZoneOffset(string TimeZoneOffset)
        {
            string s = null;
            int iTimeZoneOffset = 0;
            //try
            //{
            //dynamic d = new System.DateTime();
            //dynamic s = d.getTimezoneOffset();

            s = TimeZoneOffset;
            if (string.IsNullOrEmpty(s))
            {
                iTimeZoneOffset = 0;
            }
            else
            {
                iTimeZoneOffset = Convert.ToInt32(s);
            }
            //change the sign of the value (pos to neg or vice versa): JavaScript returns offsets west of GMT as positive values
            iTimeZoneOffset = iTimeZoneOffset - iTimeZoneOffset - iTimeZoneOffset;
            Util.SetSessionValue("TimeZoneOffset", iTimeZoneOffset.ToString());
            Util.SetSessionValue("LoginIsFromUI", "1");

            //}
            //catch (Exception ex)
            //{
            //}
        }
        private void Initialize()
        {
            goP = (clProject)Util.GetInstance("p");
            goTR = (clTransform)Util.GetInstance("tr");
            goMeta = (clMetaData)Util.GetInstance("meta");
            goData = (clData)Util.GetInstance("data");
        }
        public ActionResult NoneWorkArea()
        {

            return View();

        }

        [Selltis.MVC.RouteConfig.RefreshDetectFilter]
        public ActionResult LoadTimeZone(string Locale, string Offset)
        {
            Initialize();
            TimeZoneInformation _ReturnObj = OnOpen(Locale, Offset);
            if (_ReturnObj == null)
            {
                _ReturnObj = new TimeZoneInformation();
            }
            return View(_ReturnObj);
        }
        public JsonResult LoadTimeZonesCombo()
        {
            goTR = (clTransform)Util.GetInstance("tr");
            List<TimeZonesInfo> oList = new List<TimeZonesInfo>();
            clTable t = default(clTable);
            t = goTR.UTC_GetTimeZonesTable();

            oList.Add(new TimeZonesInfo
            {
                TimeZoneLabel = "(Make selection)",
                TimeZoneName = "<%NONE%>"
            });
            for (int i = 1; i <= t.Count(); i++)
            {
                oList.Add(new TimeZonesInfo
                {
                    TimeZoneLabel = t.GetVal("Text", i).ToString(),
                    TimeZoneName = t.GetVal("Value", i).ToString()
                });
            }
            return Json(oList, JsonRequestBehavior.AllowGet);
        }

        protected TimeZoneInformation OnOpen(string Locale, string Offset)
        {
            TimeZoneInformation _ReturnObj = new TimeZoneInformation();

            clTable t = default(clTable);
            int i = 0;
            //ListItem li = default(ListItem);
            string sTZLocale = null;
            string sClientOffset = null;
            int iClientOffset = 0;
            PublicDomain.TzTimeZone zone = default(PublicDomain.TzTimeZone);
            string sOldTimeZone = null;
            TimeSpan tsOldTimeZoneOffset = default(TimeSpan);
            TimeSpan tsClientOffset = default(TimeSpan);
            string sPlus = null;
            string sOldPlus = null;
            string sOldTimeZoneLabel = null;
            string sText = "";
            string sPromptText = null;
            int par_iValid = 4;

            sTZLocale = HttpUtility.UrlDecode(Locale);
            sClientOffset = HttpUtility.UrlDecode(Offset);
            if (string.IsNullOrEmpty(sClientOffset))
            {
                //No client offset information is available
                iClientOffset = 0;
            }
            else
            {
                if (goTR != null)
                {
                    iClientOffset = Convert.ToInt32(goTR.StringToNum(sClientOffset, "", ref par_iValid));
                }
            }

            tsClientOffset = new TimeSpan(0, iClientOffset, 0);
            if (Strings.Left(tsClientOffset.Hours.ToString(), 1) == "-")
            {
                sPlus = "";
            }
            else
            {
                sPlus = "+";
            }

            if (goTR != null)
            {
                sPromptText = "Please select your time zone. Your PC's time zone offset is GMT" + sPlus + tsClientOffset.Hours.ToString() + ":" + goTR.Pad(tsClientOffset.Minutes.ToString(), 2, "0", "L") + "." + Constants.vbCrLf + Constants.vbCrLf;
            }
            sPromptText += "Note that your PC's current offset may be different from the offset shown in the list below due to the daylight savings adjustment." + Constants.vbCrLf;

            if (goMeta != null)
            {
                sOldTimeZone = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "TZLOCALE", "", true);
            }

            if (string.IsNullOrEmpty(sOldTimeZone))
            {
                sText = sPromptText;
                _ReturnObj.LBL_TimeZone = new HtmlString(sText.Replace(Constants.vbCrLf, "<br/>"));
            }
            else
            {
                zone = PublicDomain.TzTimeZone.GetTimeZone(sOldTimeZone);
                if (zone == null)
                {
                    sText = sPromptText;
                    _ReturnObj.LBL_TimeZone = new HtmlString(sText.Replace(Constants.vbCrLf, "<br/>"));
                }
                else
                {
                    tsOldTimeZoneOffset = goTR.UTC_GetUtcOffset(goTR.NowLocal(), zone);
                    //*** MI 10/1/07 changed from Now to goTr.NowLocal().
                    if (Strings.Left(tsOldTimeZoneOffset.Hours.ToString(), 1) == "-")
                    {
                        sOldPlus = "";
                    }
                    else
                    {
                        sOldPlus = "+";
                    }
                    sOldTimeZoneLabel = goTR.UTC_GetTimeZoneLabel(sOldTimeZone);
                    sText = "Your PC's time zone offset (GMT" + sPlus + tsClientOffset.Hours.ToString() + ":" + goTR.Pad(tsClientOffset.Minutes.ToString(), 2, "0", "L") + ") is different from the current offset (GMT" + sOldPlus + tsOldTimeZoneOffset.Hours.ToString() + ":" + goTR.Pad(tsOldTimeZoneOffset.Minutes.ToString(), 2, "0", "L") + ") of your Selltis time zone." + Constants.vbCrLf + Constants.vbCrLf;
                    // & sOldTimeZoneLabel & ". " & vbCrLf
                    sText += "Note that your PC's current offset may be different from the offset shown in the list below due to the daylight savings adjustment." + Constants.vbCrLf + Constants.vbCrLf;
                    //sText &= "Your PC is set to GMT" & sPlus & tsClientOffset.Hours.ToString & ":" & goTR.Pad(tsClientOffset.Minutes.ToString, 2, "0", "L") & " hours " & vbCrLf
                    //sText &= "but the last Selltis time zone you selected, '" & sOldTimeZoneLabel & "', currently has an offset of GMT" & sOldPlus & tsOldTimeZoneOffset.Hours.ToString & ":" & goTR.Pad(tsOldTimeZoneOffset.Minutes.ToString, 2, "0", "L") & " hours." & vbCrLf
                    sText += "If you continue seeing this page, you may want to adjust your PC's time zone by double-clicking your clock and clicking the Time Zone tab." + Constants.vbCrLf + Constants.vbCrLf;
                    sText += "Please select the time zone you are in:";
                    _ReturnObj.LBL_TimeZone = new HtmlString(sText.Replace(Constants.vbCrLf, "<br/>"));
                }
            }

            //Combo Values

            t = goTR.UTC_GetTimeZonesTable();

            if (t == null)
            {
                //goErr.SetError(35000, sProc, "Error retrieving a list of time zones (t as returned from gotr.UTC_GetTimeZones(t) is Nothing).");
            }
            else
            {
                if (string.IsNullOrEmpty(sTZLocale))
                {
                    //Get the locale from POP/WOP MD.
                    if (goMeta != null)
                        sTZLocale = goMeta.LineRead(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "TZLOCALE", "", false);
                }

                List<TimeZonesInfo> oList = new List<TimeZonesInfo>();

                oList.Add(new TimeZonesInfo
                {
                    TimeZoneLabel = "(Make selection)",
                    TimeZoneName = "<%NONE%>"
                });
                for (int j = 1; j <= t.Count(); j++)
                {
                    oList.Add(new TimeZonesInfo
                    {
                        TimeZoneLabel = t.GetVal("Text", j).ToString(),
                        TimeZoneName = t.GetVal("Value", j).ToString()
                    });
                }
                _ReturnObj.TimeZonesList = oList;
                //li = new ListItem();
                //li.Value = "<%NONE%>";
                //li.Text = "(Make selection)";
                //this.CMB_TimeZone.Items.Add(li);

                for (i = 1; i <= t.Count(); i++)
                {
                    //li = new ListItem();
                    //li.Value = t.GetVal("Value", i).ToString;
                    //li.Text = t.GetVal("Text", i).ToString;
                    //this.CMB_TimeZone.Items.Add(li);
                }
                if (string.IsNullOrEmpty(sTZLocale))
                {
                    _ReturnObj.SelectedTimeZone = "<%NONE%>";
                }
                else
                {
                    _ReturnObj.SelectedTimeZone = sTZLocale;
                }
            }
            return _ReturnObj;
        }
        public JsonResult Btn_OK_Click(string SelectedTimeZone)
        {
            Initialize();
            System.Data.SqlClient.SqlConnection oConnection = null;
            if (SelectedTimeZone == "<%NONE%>")
            {
                // MessageBox("Please select a time zone.");
                return Json(MessageBox("Please select a time zone."), JsonRequestBehavior.AllowGet);
            }

            //Evaluate the time zone and set it
            string s = SelectedTimeZone;
            if (string.IsNullOrEmpty(s))
            {
                goTR.oUserTimeZone = null;
            }
            else
            {
                goTR.oUserTimeZone = PublicDomain.TzTimeZone.GetTimeZone(s);
            }
            if (goTR.oUserTimeZone == null)
            {
                //Invalid zone
                //goErr.SetError(35000, sProc, "The time zone '" + s + "' is invalid.");
                return Json("", JsonRequestBehavior.AllowGet);
            }

            goMeta.LineDelete(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "TZLOCALE", "", true);
            //Persist the selection
            goMeta.LineWrite(goP.GetUserTID(), "POP_PERSONAL_OPTIONS", "TZLOCALE", s, ref oConnection, "", "XX");

            //Close();
            Util.ClearSessionValue("RedirectToPage");
            return Json("success", JsonRequestBehavior.AllowGet);
        }

        private ManageFormsMessageBox MessageBox(string par_sMessage, string par_sPurpose = "", string par_sTitle = "Selltis", int par_iType = clC.SELL_MB_OK, string par_sButton1Label = "", string par_sButton2Label = "", string par_sButton3Label = "")
        {
            ManageFormsMessageBox _MessageBox = new ManageFormsMessageBox();
            string sMessage = par_sMessage;
            string sTitle = par_sTitle;
            string sMessageBoxPurpose = null;
            sMessageBoxPurpose = par_sPurpose;
            sMessage = HttpUtility.HtmlEncode(sMessage);
            sMessage = goTR.Replace(sMessage, Constants.vbCrLf, "<BR>");
            sMessage = goTR.Replace(sMessage, Constants.vbTab, "&nbsp;&nbsp;&nbsp;&nbsp;");
            sTitle = HttpUtility.HtmlEncode(sTitle);


            _MessageBox.LBL_MsgBoxMessageText = sMessage;
            _MessageBox.LBL_MsgBoxTitleText = sTitle;
            switch (par_iType)
            {
                case clC.SELL_MB_YESNO:
                    if (string.IsNullOrEmpty(par_sButton1Label))
                    {
                        _MessageBox.BTN_MsgBox1Text = "  Yes  ";
                    }
                    else
                    {
                        _MessageBox.BTN_MsgBox1Text = par_sButton1Label;
                    }
                    if (string.IsNullOrEmpty(par_sButton2Label))
                    {
                        _MessageBox.BTN_MsgBox2Text = "  No ";
                    }
                    else
                    {
                        _MessageBox.BTN_MsgBox2Text = par_sButton2Label;
                    }
                    _MessageBox.BTN_MsgBox1Visible = true;
                    _MessageBox.BTN_MsgBox2Visible = true;
                    _MessageBox.BTN_MsgBox3Visible = false;
                    _MessageBox.BTN_MsgBox1Focus = true;
                    break;
                case clC.SELL_MB_YESNOCANCEL:
                    if (string.IsNullOrEmpty(par_sButton1Label))
                    {
                        _MessageBox.BTN_MsgBox1Text = "  Yes  ";
                    }
                    else
                    {
                        _MessageBox.BTN_MsgBox1Text = par_sButton1Label;
                    }
                    if (string.IsNullOrEmpty(par_sButton2Label))
                    {
                        _MessageBox.BTN_MsgBox2Text = "  No ";
                    }
                    else
                    {
                        _MessageBox.BTN_MsgBox2Text = par_sButton2Label;
                    }
                    if (string.IsNullOrEmpty(par_sButton3Label))
                    {
                        _MessageBox.BTN_MsgBox3Text = "Cancel";
                    }
                    else
                    {
                        _MessageBox.BTN_MsgBox3Text = par_sButton3Label;
                    }
                    _MessageBox.BTN_MsgBox1Visible = true;
                    _MessageBox.BTN_MsgBox2Visible = true;
                    _MessageBox.BTN_MsgBox3Visible = true;
                    _MessageBox.BTN_MsgBox1Focus = true;
                    break;
                case clC.SELL_MB_OK:
                    if (string.IsNullOrEmpty(par_sButton1Label))
                    {
                        _MessageBox.BTN_MsgBox1Text = "  OK  ";
                        //MessTranslate
                    }
                    else
                    {
                        _MessageBox.BTN_MsgBox1Text = par_sButton1Label;
                        //HttpUtility.HtmlEncode(par_sButton1Label)
                    }
                    _MessageBox.BTN_MsgBox1Visible = true;
                    _MessageBox.BTN_MsgBox2Visible = false;
                    _MessageBox.BTN_MsgBox3Visible = false;
                    _MessageBox.BTN_MsgBox1Focus = true;
                    break;
                default:
                    if (string.IsNullOrEmpty(par_sButton1Label))
                    {
                        _MessageBox.BTN_MsgBox1Text = "  Yes  ";
                    }
                    else
                    {
                        _MessageBox.BTN_MsgBox1Text = par_sButton1Label;
                    }
                    if (string.IsNullOrEmpty(par_sButton2Label))
                    {
                        _MessageBox.BTN_MsgBox2Text = "  No  ";
                        //MessTranslate
                    }
                    else
                    {
                        _MessageBox.BTN_MsgBox2Text = par_sButton2Label;
                        //HttpUtility.HtmlEncode(par_sButton2Label)
                    }
                    _MessageBox.BTN_MsgBox1Visible = true;
                    _MessageBox.BTN_MsgBox2Visible = true;
                    _MessageBox.BTN_MsgBox3Visible = false;
                    _MessageBox.BTN_MsgBox1Focus = true;
                    break;
            }
            _MessageBox.PNL_MessageBoxVisible = true;
            return _MessageBox;
        }

        public void SignIn()
        {
            if (!Request.IsAuthenticated)
            {
                HttpContext.GetOwinContext().Authentication.Challenge(
               new AuthenticationProperties { RedirectUri = "/" },
               OpenIdConnectAuthenticationDefaults.AuthenticationType);
            }
        }

        /// <summary>
        /// Send an OpenID Connect sign-out request.
        /// </summary>
        public void SignOut()
        {
            HttpContext.GetOwinContext().Authentication.SignOut(
                    OpenIdConnectAuthenticationDefaults.AuthenticationType,
                    CookieAuthenticationDefaults.AuthenticationType);

        }

    }
}