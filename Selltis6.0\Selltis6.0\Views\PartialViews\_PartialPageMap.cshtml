@model Selltis.Core.Map
@using Kendo.Mvc.UI;
@using Selltis.Core;


<style>
    .k-state-selected a {
        color: #fff;
    }

    .k-grid-header .k-header {
        overflow: hidden;
        /*border-style: solid;*/
        border-style: inset;
        /*border-width: 0 0 1px 1px;*/
        border-right-color: #C5C5C5 !important;
        border-right-width: 1px !important;
        text-overflow: ellipsis;
    }

    .k-header {
        /*border-color:#999999 !important;
        border-color: lightgray !important;*/
        background-color: #ffffff !important;
        background-image: none !important;
        height: 15px !important;
        /*font-weight: 600 !important;
        font-size: 13px !important;*/
        border-width: 0 0 0px 0px !important;
        color: #666 !important;
    }

    .k-grid-header .k-header .k-link {
        background-color: #ffffff !important;
        background-image: none !important;
        font-weight: bold !important;
        font-size: 8pt !important;
        border-width: 0 0 0px 0px !important;
        color: #666 !important;
    }

    header table, .k-grid-content table, .k-grid-footer table {
        table-layout: fixed;
    }

    .k-grid table {
        width: 100px;
        margin: 0;
        border-collapse: separate;
        border-spacing: 0;
        empty-cells: show;
        border-width: 0;
        outline: 0;
    }


    .k-grid tbody tr td {
        cursor: pointer;
    }

    .k-grid tbody tr, .k-grid tbody tr td {
        height: 17px !important;
        border-width: 0px 0px 1px 0px;
        font-size: 11px;
        font-family: "Open Sans", Helvetica, Arial, sans-serif;
        line-height: 1.6em;
        /*font-weight: 400;*/ /*issue in report - All,Avg.. not bold */
    }

    .k-grid-content {
        height: -webkit-calc(100% - 20px) !important;
        height: -moz-calc(100% - 20px) !important;
        height: calc(100% - 20px) !important;
        height: -o-calc(100% - 100px) !important;
        display: block;
        /*padding: 5px;
        width: 99%;*/
        padding: 0px;
        width: 100%;
    }
</style>

<div id="<EMAIL>(" ", "").Replace("-", "")" class="<EMAIL>(" ", "").Replace("-", "")" style="width:100%; height:100%">
    <div id="<EMAIL>(" ", "").Replace("-", "")"
         style="position: absolute; top: 20px; left: 35%; z-index: 5; background-color: #FFFFB0; padding: 5px; border: 1px solid #999; text-align: center; line-height: 30px; display:none;">
        <span id="<EMAIL>(" ", "").Replace("-", "")" style="display:block; width: 100%; font-weight: bold;">Map Options not set / improperly set. Please set these options in View properties > Map.</span>
    </div>
    <div id="<EMAIL>(" ", "").Replace("-", "")"
         style="position: absolute; top: 20px; left: 35%; z-index: 5; background-color: #FFFFB0; padding: 5px; border: 1px solid #999; text-align: center; line-height: 30px; display:none;">
        <span id="<EMAIL>(" ", "").Replace("-", "")" style="display:block; width: 100%; font-weight: bold;">No records to display.</span>
    </div>
    @*<div role='row' id="<EMAIL>(" ", "").Replace("-", "")" style="padding:5px; font-weight: normal; width: 100%; background-color:#F5F5F5;"> No records to display.</div>*@
    <div id="<EMAIL>(" ", "")" tabindex="0" style="height:100%; width: 100%"></div>
</div>

<input type="hidden" id="<EMAIL>(" ", "")" value="@Model.DependencyViewIds" />
<input type="hidden" id="<EMAIL>(" ", "")" value="@Model.DependencyViewIdsOnFocus" />
<input type="hidden" id="<EMAIL>(" ", "")" value="@Model.ParentViewId" />
<input type="hidden" id="<EMAIL>(" ", "")" value="" />
<input type="hidden" id="<EMAIL>(" ", "")" value="map" />
<input type="hidden" id="<EMAIL>(" ", "")" value="@Model.ShowLines_Markers" />

<script>

    $(document).ready(function () {

        //Start Map processing intitalizing here - wait for Google Maps API to load
        function waitForGoogleMaps() {
            if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
                <EMAIL>(" ", "").Replace("-", "") ();
            } else {
                setTimeout(waitForGoogleMaps, 100);
            }
        }
        waitForGoogleMaps();

        // Set View Properties
        var <EMAIL>(" ", "").Replace("-", "") = '@Model.ViewKey.Replace(" ", "")';

        if (isLoadFromSession == true || isLoadFromSession == 'True') {
            var lastSelViewId = sessionStorage.getItem('@Model.Key' + "_" +"lastViewid");
            if (lastSelViewId != null && lastSelViewId != undefined) {
                if ('@Model.ViewId.Replace(" ","")' == lastSelViewId) {
                    $(".panel-heading.active").removeClass('ViewHeaderOn');
                    $(".panel-heading.active").parent().removeClass('z-depthstyle');
                    $("#" + '@Model.TableName' + '@Model.ViewId.Replace(" ","")').removeClass('ViewHeaderOff').addClass('ViewHeaderOn');
                    $("#" + 'view' + '@Model.ViewId.Replace(" ","")').addClass('z-depthstyle');
                    sessionStorage.removeItem('@Model.Key' + "_" +"lastViewid");
                    sessionStorage.setItem('@Model.Key' + "_" +"lastViewid", '@Model.ViewId.Replace(" ", "")');  //to find issue with session viewid in metadatasave and prop link
                    sessionStorage.setItem('@Model.Key' + "_" +"CurrentViewId", '@Model.ViewId.Replace(" ", "")');
                }
            }
        }
        else if ('@Model.IsTabView' == 'False') {
            if ('@Model.OrderIndex' == 1) {
                $(".panel-heading.active:first").removeClass('ViewHeaderOff');
                $(".panel-heading.active:first").addClass('ViewHeaderOn');
                $('.panel-heading.active:first').parent().addClass('z-depthstyle');
                sessionStorage.setItem('@Model.Key' + "_" + "lastViewid", '@Model.ViewId.Replace(" ", "")');
                sessionStorage.setItem('@Model.Key' + "_" + "CurrentViewId", '@Model.ViewId.Replace(" ", "")');
            }
        }

        if ('@Model.IsTabView' == 'True') {
            if ('@Model.IsActive' == 'True') {
                $('#hdnActive').val('@Model.ViewId.Replace(" ","")');
                $('#hdnActiveTab').val($('#hdnActiveTab').val() + "\n" + '@Model.ViewId.Replace(" ","")');
            }
        }

        if ('@Model.IsMasterView' == 'True') {
            sessionStorage.setItem('@Model.Key' + "_" + "ViewId", '@Model.ViewId');
        }
        else {
        }

        if (<EMAIL>(" ", "").Replace("-", "") == mastergridid) {
            $('#view' + mastergridid).addClass("z-depthstyle");
        }

    });

    // ----------------------------------------------------------------------------------------------------------------------------------------------------------------
    // ---- Resize Event
    // ----------------------------------------------------------------------------------------------------------------------------------------------------------------
    var $<EMAIL>(" ", "").Replace("-", "") = $("#<EMAIL>(" ", "").Replace("-", "")");
    var <EMAIL>(" ", "").Replace("-", "") = $("#<EMAIL>(" ", "").Replace("-", "")").css('height');
    var <EMAIL>(" ", "").Replace("-", "") = $("#<EMAIL>(" ", "").Replace("-", "")").css('width');
    function <EMAIL>(" ", "").Replace("-", "") ()
    {
        //debugger
        if (($("#<EMAIL>(" ", "").Replace("-", "")").css('height') != <EMAIL>(" ", "").Replace("-", "")) ||
            ($("#<EMAIL>(" ", "").Replace("-", "")").css('width') != <EMAIL>(" ", "").Replace("-", "")))
        {
            //alert($<EMAIL>(" ", "").Replace("-", "") .css('width'));
            //alert($<EMAIL>(" ", "").Replace("-", "") .css('height'));
            <EMAIL>(" ", "").Replace("-", "") = $<EMAIL>(" ", "").Replace("-", "") .css('height');
            <EMAIL>(" ", "").Replace("-", "") = $<EMAIL>(" ", "").Replace("-", "") .css('width');

            google.maps.event.trigger(<EMAIL>(" ", "").Replace("-", ""), "resize");
        }

        setTimeout(<EMAIL>(" ", "").Replace("-", ""), 500);
    }
    <EMAIL>(" ", "").Replace("-", "") ();
    // ----------------------------------------------------------------------------------------------------------------------------------------------------------------
    // ----------------------------------------------------------------------------------------------------------------------------------------------------------------


    var <EMAIL>(" ", "").Replace("-", "");

    function <EMAIL>(" ", "").Replace("-", "") () {
        //debugger;
        <EMAIL>(" ", "").Replace("-", "") ();
    }

    function <EMAIL>(" ", "").Replace("-", "") () {

        var <EMAIL>(" ", "").Replace("-", "") = {
            center: new google.maps.LatLng(41.850033, -97.6500523),
            zoom: 4,
            mapTypeId: google.maps.MapTypeId.ROADMAP
        };
        <EMAIL>(" ", "").Replace("-", "") = new google.maps.Map(document.getElementById("<EMAIL>(" ", "")"), <EMAIL>(" ", "").Replace("-", ""));

        //Check if Map options have been set
        var MapOptionLabel = document.getElementById('<EMAIL>(" ", "").Replace("-", "")');
        if ('@Model.MapLatitudeField' == "" || '@Model.MapLongitudeField' == ""){
            //debugger;
            MapOptionLabel.style.display = 'block';
            $("#viewdatacount" + '@Model.ViewId.Replace(" ", "")').text('0');
        }
        else{
            <EMAIL>(" ", "").Replace("-", "") ();
            <EMAIL>(" ", "").Replace("-", "") ();
            MapOptionLabel.style.display = 'none';
        }

        /**  Display Regions  **/
        <EMAIL>(" ", "").Replace("-", "") ();

    };

    var <EMAIL>(" ", "").Replace("-", "");
    var <EMAIL>(" ", "").Replace("-", "");

    function <EMAIL>(" ", "").Replace("-", "") () {

        /**********************************
        Display Regions
        **********************************/
        //var xmlString;

        $.ajax({
            url: '/Map/MapReadRegionData',
            //contentType: "application/json; charset=utf-8",
            type: "GET",
            data: {
                ViewKey: '@Model.ViewId.Replace(" ","")',
                Key: '@Model.Key'
            },
            async: false,
            dataType: "json",
            success: function (data) {
                <EMAIL>(" ", "").Replace("-", "") (data);
            },
            error: function (request, status, error) {
                debugger;
                if (request.responseText.length > 0 || request.responseText != null || request.responseText != undefined) {
                    //alert(request.responseText);
                }
            }
        })

        //<EMAIL>(" ", "").Replace("-", "") ();
    }

    function <EMAIL>(" ", "").Replace("-", "") (xmlString)
    {
        //debugger;
        var arr = new Array();
        var bounds = new google.maps.LatLngBounds();
        var polygons = [];

        var xml = xmlParse(xmlString);
        var Territory = xml.getElementsByTagName("Territory");
        // alert(subdivision.length);
        var infoWindow = new google.maps.InfoWindow();
        for (var i = 0; i < Territory.length; i++) {
            arr = [];
            var coordinates = xml.documentElement.getElementsByTagName("Territory")[i].getElementsByTagName("coord");
            var type = xml.documentElement.getElementsByTagName("Territory")[i].getAttribute("GeoType");
            var color = xml.documentElement.getElementsByTagName("Territory")[i].getAttribute("Color");
            switch (type.toUpperCase()) {
                case "POLYGON":
                    for (var j = 0; j < coordinates.length; j++) {
                        arr.push(new google.maps.LatLng(
                              parseFloat(coordinates[j].getAttribute("lat")),
                              parseFloat(coordinates[j].getAttribute("lng"))
                        ));
                        bounds.extend(arr[arr.length - 1])
                    }
                    var gon = new google.maps.Polygon({
                        paths: arr,
                        strokeOpacity: 0.8,
                        strokeWeight: 2,
                        fillOpacity: 0.35,
                    });
                    break;

                case "CIRCLE":
                    var latlng = new google.maps.LatLng(
                          parseFloat(coordinates[0].getAttribute("lat")),
                          parseFloat(coordinates[0].getAttribute("lng"))
                    );
                    var gon = new google.maps.Circle({
                        radius: parseFloat(xml.documentElement.getElementsByTagName("Territory")[i].getAttribute("Radius")),
                        center: latlng,
                        strokeOpacity: 0.8,
                        strokeWeight: 2,
                        fillOpacity: 0.35
                    });
                    bounds.union(gon.getBounds());
                    break;

                case "RECTANGLE":
                    var gon = new google.maps.Rectangle({
                        strokeOpacity: 0.8,
                        strokeWeight: 2,
                        fillOpacity: 0.35,
                        bounds: {
                            north: parseFloat(coordinates[1].getAttribute("lat")),
                            south: parseFloat(coordinates[0].getAttribute("lat")),
                            east: parseFloat(coordinates[1].getAttribute("lng")),
                            west: parseFloat(coordinates[0].getAttribute("lng"))
                        }
                    });
                    break;

                default:
                    alert('implement a storage-method for ' + shape.type)
            }
            polygons.push(gon);
            polygons[polygons.length - 1].setMap(<EMAIL>(" ", "").Replace("-", ""));
            //if (xml.documentElement.getElementsByTagName("Territory")[i].getAttribute("ID") == RecordID) {
            //    gon.set('strokeColor', color);//'#00FF00');
            //    gon.set('fillColor', color);//'#00FF00');
            //    gon.set('draggable', true);
            //    gon.set('editable', true);
            //}
            //else {
            //    gon.set('strokeColor', color);//'#FF0000');
            //    gon.set('fillColor', color);//'#00FF00');
            //}

            gon.set('strokeColor', color);//'#FF0000');
            gon.set('fillColor', color);//'#00FF00');

            var infowindow = new google.maps.InfoWindow();
            <EMAIL>(" ", "").Replace("-", "") (gon
                , <EMAIL>(" ", "").Replace("-", "")
                , infowindow
                , xml.documentElement.getElementsByTagName("Territory")[i].getAttribute("Name")
                )

        }

    }

    var _mapselectedRecordId ;
    function <EMAIL>(" ", "").Replace("-", "") () {
        // debugger;
        $.ajax({
            url: '/Map/MapReadMarkerData',
            contentType: "application/json; charset=utf-8",
            type: "GET",
            data: {
                ViewKey: '@Model.ViewId.Replace(" ","")',
                IsActive: true,
                MasterSelID: '@Model.MasterSelID',
                Key : '@Model.Key'
            },
            async: false,
            dataType: "json",
            success: function (data) {
                <EMAIL>(" ", "").Replace("-", "") = data;
                <EMAIL>(" ", "").Replace("-", "") ();

                var hidShowLines = $('#<EMAIL>(" ", "")').val();
                <EMAIL>(" ", "").Replace("-", "") (hidShowLines);

            },
            error: function (request, status, error) {
                // debugger;
                if (request.responseText.length > 0 || request.responseText != null || request.responseText != undefined) {
                    alert(request.responseText);
                }
            }
        })

    }

    function <EMAIL>(" ", "").Replace("-", "") (){
        var markers = jQuery.parseJSON(<EMAIL>(" ", "").Replace("-", ""));
        var infoWindow = new google.maps.InfoWindow();
        $.ajax({
            cache: false,
            async: false,
            url: '/Map/GetLastSelectedRecordID',
            dataType: "json",
            type: "GET",
            contentType: 'application/json',
            data: { ViewKey: '@Model.ViewId.Replace(" ", "")', Key: Key },
            success: function (result) {
                _mapselectedRecordId=result;
            },
            error: function (result) {
            }
        });
    }
    var currentMarker;
    function <EMAIL>(" ", "").Replace("-", "") (hidShowLines) {
        var markers = jQuery.parseJSON(<EMAIL>(" ", "").Replace("-", ""));
        <EMAIL>(" ", "").Replace("-", "") = markers;

        //var NoMarks =  document.getElementById('<EMAIL>(" ", "").Replace("-", "")');
        var NoMarks = document.getElementById('<EMAIL>(" ", "").Replace("-", "")');
        if (<EMAIL>(" ", "").Replace("-", "") == "") {
            NoMarks.style.display = 'block';
            return;
        }
        if (<EMAIL>(" ", "").Replace("-", "") == "[]") {
            NoMarks.style.display = 'block';
            return;
        }
        NoMarks.style.display = 'none';

        var mapLatitudeField = '@Model.MapLatitudeField.Replace("%", "_")';
        var mapLongitudeField = '@Model.MapLongitudeField.Replace("%", "_")';
        var mapIconField = '@Model.MapIconField.Replace("%", "_")';
        
        var mapOptions = {
            center: new google.maps.LatLng(41.850033, -97.6500523),
            zoom: 4,
            mapTypeId: google.maps.MapTypeId.ROADMAP
        };

        <EMAIL>(" ", "").Replace("-", "") = new google.maps.Map(document.getElementById("<EMAIL>(" ", "")"), mapOptions);

        var goodLat = 0;
        var goodLong = 0;
        var goodFound = false;
        var infoWindow = new google.maps.InfoWindow();
        var lat_lng = new Array();
        var latlngbounds = new google.maps.LatLngBounds();

        var polyline = null;


        for (i = 0; i < markers.length; i++) {

            var data = markers[i]
            //Show point only if not 0 - default value for Numbers
            if (data[mapLatitudeField] != 0 && data[mapLongitudeField]!= 0){
                
                var IconUrl = data[mapIconField];
                if (IconUrl == "" || IconUrl == null || IconUrl == undefined) {
                    IconUrl = "http://maps.google.com/mapfiles/ms/icons/red-dot.png";
                }

                if(!goodFound){
                    goodLat = data[mapLatitudeField];
                    goodLong = data[mapLongitudeField];
                }
                var myLatlng = new google.maps.LatLng(data[mapLatitudeField], data[mapLongitudeField]);

                lat_lng.push(myLatlng);
                
                if (hidShowLines == "0" ) //Show markers or markers
                {
                    if(data['GID_ID']==_mapselectedRecordId){
                        var marker = new google.maps.Marker({
                            position: myLatlng,
                            map: <EMAIL>(" ", "").Replace("-", ""),
                            icon: {
                                url: "http://maps.google.com/mapfiles/ms/icons/blue-dot.png"
                            },
                            optimized: false,
                            zIndex:99999999
                        })

                        currentMarker = marker;
                    }
                    else{
                        var marker = new google.maps.Marker({
                            position: myLatlng,
                            map: <EMAIL>(" ", "").Replace("-", ""),
                            icon: {
                                //url: "http://maps.google.com/mapfiles/ms/icons/red-dot.png"
                                url: IconUrl
                            }
                        });
                    }

                    //latlngbounds.extend(marker.position);
                    (function (marker, data) {
                        google.maps.event.addListener(marker, "click", function (e) {
                            //var sHTML = buildInfoWindowHTML(data);
                            if (currentMarker != null) {
                                currentMarker.setIcon(IconUrl);
                                currentMarker = null;
                            };
                            currentMarker = marker;
                            marker.setIcon('http://maps.google.com/mapfiles/ms/icons/blue-dot.png');

                            var sHTML = <EMAIL>(" ", "").Replace("-", "") (data);
                            infoWindow.setContent('<div>' + '<table>' + sHTML + '<table>' + '</div>');
                            infoWindow.open(<EMAIL>(" ", "").Replace("-", ""), marker);
                        });
                    })(marker, data);
                }

                else if (hidShowLines == "1") //Show lines with numbers
                {
                    if(data['GID_ID']==_mapselectedRecordId){
                        var marker = new google.maps.Marker({
                            position: myLatlng,
                            map: <EMAIL>(" ", "").Replace("-", ""),
                            label: (i+1).toString(),
                            optimized: false,
                            zIndex:99999999
                        })

                        currentMarker = marker;
                    }
                    else{
                        var marker = new google.maps.Marker({
                            position: myLatlng,
                            map: <EMAIL>(" ", "").Replace("-", ""),
                            label: (i+1).toString()
                        });
                    }

                    //latlngbounds.extend(marker.position);
                    (function (marker, data) {
                        google.maps.event.addListener(marker, "click", function (e) {
                            var sHTML = <EMAIL>(" ", "").Replace("-", "") (data);
                            infoWindow.setContent('<div>' + '<table>' + sHTML + '<table>' + '</div>');
                            infoWindow.open(<EMAIL>(" ", "").Replace("-", ""), marker);
                        });
                    })(marker, data);                    
                }

                latlngbounds.extend(myLatlng);
            }
        }
        //map.fitBounds();
        if(goodFound){
            mapOptions = {
                center: new google.maps.LatLng(goodLat, goodLong),
                zoom: 6,
                mapTypeId: google.maps.MapTypeId.ROADMAP
            }
            <EMAIL>(" ", "").Replace("-", "") = new google.maps.Map(document.getElementById("<EMAIL>(" ", "")"), mapOptions);
        }

        if (hidShowLines == "2") //lines with arrows
        {
            polyline = new google.maps.Polyline({
                path: lat_lng,
                strokeColor: "#00bfff",
                strokeOpacity: 1.0,
                strokeWeight: 2,
                icons: [{
                    icon: {
                        path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                        strokeColor: '#0000ff',
                        fillColor: '#0000ff',
                        fillOpacity: 0.05
                    },
                    repeat: '100px',
                    path: []
                }]
            });

            polyline.setMap(<EMAIL>(" ", "").Replace("-", ""));
        }
        else if (hidShowLines == "1") // lines with numbers
        {
            polyline = new google.maps.Polyline({
                path: lat_lng,
                strokeColor: "#00bfff",
                strokeOpacity: 1.0,
                strokeWeight: 2
            });

            polyline.setMap(<EMAIL>(" ", "").Replace("-", ""));
        }
        //To fit the map according to their markers postions..J
        var map = <EMAIL>(" ", "").Replace("-", "");
        map.fitBounds(latlngbounds);     
    }



    function xmlParse(str) {
        if (typeof ActiveXObject != 'undefined' && typeof GetObject != 'undefined') {
            var doc = new ActiveXObject('Microsoft.XMLDOM');
            doc.loadXML(str);
            return doc;
        }

        if (typeof DOMParser != 'undefined') {
            return (new DOMParser()).parseFromString(str, 'text/xml');
        }

        return createElement('div', null);
    }

    function <EMAIL>(" ", "").Replace("-", "") (marker, map, infowindow, html) {
        marker.addListener('click', function (event) {
            //debugger;
            if (event) {
                point = event.latLng;
            }
            infowindow.setContent(html);
            infowindow.setPosition(point);
            infowindow.open(map, this);

            var DELAY = 700;
            timer = setTimeout(function () {
            }, DELAY);
        });
    }

    function <EMAIL>(" ", "").Replace("-", "") (data1) {
        debugger;
        $('#DefaultViewId').val('@Model.ViewId.Replace(" ", "")');

        var c = ""
        var Cols = @Html.Raw(Json.Encode(Model.Columns));
        var data;
        $.ajax({
            url: '/Map/MapGetSelectedmarkerData',
            contentType: "application/json; charset=utf-8",
            type: "GET",
            data: {
                ViewKey: '@Model.ViewId.Replace(" ","")',
                IsActive: true,
                MasterSelID: '@Model.MasterSelID',
                SelectedID: data1['GID_ID'],
                Key: '@Model.Key'
            },
            async: false,
            dataType: "json",
            success: function (data2) {
                //alert(data);
                //debugger;
                data = data2;
                data = jQuery.parseJSON(data)[0];
            },
            error: function (request, status, error) {
                debugger;
                if (request.responseText.length > 0 || request.responseText != null || request.responseText != undefined) {
                    alert(request.responseText);
                }
            }
        })

        if (data == "" || data == "[]"){
            return;
        }

        // Add SYS_Name GID_ID as link on the top
        if ('@Model.GroupedData' == 'True' && '@Model.GroupLinkFile' != '')
        {
            if (data1["SYS_NAME"].indexOf("#$%^&*") > -1) {
                c='<tr><td style="padding: 0px; vertical-align: top; font-weight: bold" colspan="3" hyperlink="hyperlink"><a href="/CreateForm/CreateForm/@Model.GroupLinkFile/' + data1['SYS_NAME'].split("#$%^&*")[1] + '/TYPE/false/false/@Model.ViewId/false/MASTERID/FORMKEY/FIELD' + '"> ' + data1["SYS_NAME"].split("#$%^&*")[0] + '</a></td></tr>';
            }
            else{
                c='<tr><td style="padding: 0px; vertical-align: top; font-weight: bold" colspan="3" hyperlink="hyperlink"><a href="/CreateForm/CreateForm/@Model.GroupLinkFile/' + data1['GID_ID'] + '/TYPE/false/false/@Model.ViewId/false/MASTERID/FORMKEY/FIELD' + '"> ' + data1["SYS_NAME"] + '</a></td></tr>';
            }
        }
        else
        {
            if (data1["SYS_NAME"].indexOf("#$%^&*") > -1) {
                c='<tr><td style="padding: 0px; vertical-align: top; font-weight: bold" colspan="2" hyperlink="hyperlink"><a href="/CreateForm/CreateForm/@Model.File/' + data1['SYS_NAME'].split("#$%^&*")[1] + '/TYPE/false/false/@Model.ViewId/false/MASTERID/FORMKEY/FIELD' + '"> ' + data1["SYS_NAME"].split("#$%^&*")[0] + '</a></td></tr>';
            }
            else{
                c='<tr><td style="padding: 0px; vertical-align: top; font-weight: bold" colspan="2" hyperlink="hyperlink"><a href="/CreateForm/CreateForm/@Model.File/' + data1['GID_ID'] + '/TYPE/false/false/@Model.ViewId/false/MASTERID/FORMKEY/FIELD' + '"> ' + data1["SYS_NAME"] + '</a></td></tr>';
            }
        }


        debugger
        //loop each column and add to table
        for (i = 0; i < Cols.length; i++) {
            var dataFieldName = Cols[i].Name;
            var dataFieldValue;
            dataFieldName = dataFieldName.replace(",","");
            dataFieldName = dataFieldName.replace("-","");
            dataFieldName = dataFieldName.replace(" ","");
            //GID_ID SYS_NAME already added above loop
            if (Cols[i].Name != "GID_ID" && Cols[i].Name != "SYS_NAME" && Cols[i].Name != "OpenLink" && Cols[i].Name != "CheckBoxColumn")
            {
                dataFieldValue = data[dataFieldName];
                if (dataFieldValue == null)
                {
                    dataFieldValue = "";
                }
                //Add label
                c = c + '<tr><td style="width:100px;padding: 0px; vertical-align: top; font-weight: bold;">' + Cols[i].Title + ':</td>';
                //c = c + '<td style="max-width:200px;padding-left: 5px;">';

                if ('@Model.GroupedData' != 'True')
                {
                    c = c + '<td style="max-width:200px;padding-left: 5px;">';
                    if (Cols[i]["IsIcon"]) {
                        c = c + '<img src="/Content/Images/' + dataFieldValue + '" alt="' + dataFieldValue + '" />';
                    }
                    else if (Cols[i]["IsLink"]) {
                        var value = dataFieldValue.split("#$%^&*");
                        if (value.length > 1){
                            var labels = value[0].split("\n");
                            var IDs = value[1].split("\n");
                            //Loop all rows in link
                            for (j = 0; j < labels.length; j++) {
                                c = c + '<a href="/CreateForm/CreateForm/' + dataFieldName.split("_")[2] + '/' + IDs[j] + '/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD' + '">' + labels[j] + '</a>';
                            }
                        }
                        else
                        {
                            var labels = value[0].split("\n");
                            //Loop all rows in link
                            for (j = 0; j < labels.length; j++) {
                                c = c + labels[j];
                            }
                        }
                    }
                    else {
                        c = c + dataFieldValue;
                    }
                    c = c + '</td>';
                }
                else
                {
                    //c = c + '<td style="max-width:200px;padding-left: 5px;">';
                    var bookMark = false;
                    var Calcs = "";
                    if (Cols[i]["IsTotal"]) {
                        bookMark = true;
                        Calcs = Calcs + 'Total:</br>';
                        //c = c + 'Total:</br>';
                    }
                    if (Cols[i]["IsAverage"]) {
                        bookMark = true;
                        Calcs = Calcs + 'Average:</br>';
                        //c = c + 'Average:</br>';
                    }
                    if (Cols[i]["IsMinimum"]) {
                        bookMark = true;
                        Calcs = Calcs + 'Minimum:</br>';
                        //c = c + 'Minimum:</br>';
                    }
                    if (Cols[i]["IsMaximum"]) {
                        bookMark = true;
                        Calcs = Calcs + 'Maximum:</br>';
                        //c = c + 'Maximum:</br>';
                    }
                    if (Cols[i]["IsMedian"]) {
                        bookMark = true;
                        Calcs = Calcs + 'Median:</br>';
                        //c = c + 'Median:</br>';
                    }
                    if (Cols[i]["IsPercent"]) {
                        bookMark = true;
                        Calcs = Calcs + 'Percent:</br>';
                        //c = c + 'Percent:</br>';
                    }
                    //c = c + dataFieldValue;
                    //c = c + '</td>';

                    //c = c + '<td style="max-width:200px;padding-left: 5px;">';

                    if (bookMark == true) {
                        c = c + '<td style="max-width:200px;padding-left: 5px;">';
                        c = c + Calcs;
                        c = c + '</td>';
                        c = c + '<td style="max-width:200px;padding-left: 5px;">';
                    }
                    else
                    {
                        c = c + '<td Colspan="2" style="max-width:200px;padding-left: 5px;">';
                    }

                    //if (bookMark == true) {
                    //    c = c + dataFieldValue;
                    //}

                    c = c + dataFieldValue;

                    c = c + '</td>';
                }

                //c = c + '</td>';
                c = c + '</tr>';

            }
        }
        var DELAY = 700;
        timer = setTimeout(function () {
            <EMAIL>(" ", "").Replace("-", "") (data["GID_ID"]);
        }, DELAY);
        return c;
    }

    @*function buildInfoWindowHTML(data) {
        debugger;
        var c = ""
        var Cols = @Html.Raw(Json.Encode(Model.Columns));
        // Add SYS_Name GID_ID as link on the top
        if (data["SYS_NAME"].includes("#$%^&*")) {
            c='<tr><td style="padding: 0px; vertical-align: top; font-weight: bold" colspan="2" hyperlink="hyperlink"><a href="/CreateForm/CreateForm/@Model.File/' + data['SYS_NAME'].split("#$%^&*")[1] + '/TYPE/false/false/@Model.ViewId/false/MASTERID/FORMKEY/FIELD' + '"> ' + data["SYS_NAME"].split("#$%^&*")[0] + '</a></td></tr>';
        }
        else{
            c='<tr><td style="padding: 0px; vertical-align: top; font-weight: bold" colspan="2" hyperlink="hyperlink"><a href="/CreateForm/CreateForm/@Model.File/' + data['GID_ID'] + '/TYPE/false/false/@Model.ViewId/false/MASTERID/FORMKEY/FIELD' + '"> ' + data["SYS_NAME"] + '</a></td></tr>';
        }
        //loop each column and add to table
        for (i = 0; i < Cols.length; i++) {
            var dataFieldName = Cols[i].Name;
            dataFieldName = dataFieldName.replace(",","");
            dataFieldName = dataFieldName.replace("-","");
            dataFieldName = dataFieldName.replace(" ","");
            //GID_ID SYS_NAME already added above loop
            if (Cols[i].Name != "GID_ID" && Cols[i].Name != "SYS_NAME" && Cols[i].Name != "OpenLink" && Cols[i].Name != "CheckBoxColumn")
            {
                if (data[dataFieldName] != null)
                {
                    //Add label
                    c = c + '<tr><td style="width:100px;padding: 0px; vertical-align: top; font-weight: bold;">' + Cols[i].Title + ':</td>';
                    c = c + '<td style="max-width:200px;padding-left: 5px;">';

                    if (Cols[i]["IsIcon"]) {
                        c = c + '<img src="/Content/Images/' + data[dataFieldName] + '" alt="' + data[dataFieldName] + '" />';
                    }
                    else if (Cols[i]["IsLink"]) {
                        var value = data[dataFieldName].split("#$%^&*");
                        var labels = value[0].split("\n");
                        var IDs = value[1].split("\n");
                        //Loop all rows in link
                        for (j = 0; j < labels.length; j++) {
                            c = c + '<a href="/CreateForm/CreateForm/' + dataFieldName.split("_")[2] + '/' + IDs[j] + '/TYPE/false/false/KEY/false/MASTERID/FORMKEY/FIELD' + '">' + labels[j] + '</a>';
                        }
                    }
                    else {
                        c = c + data[dataFieldName];
                    }

                    c = c + '</td>';
                    c = c + '</tr>';
                }
            }
        }
        var DELAY = 700;
        timer = setTimeout(function () {
            <EMAIL>(" ", "").Replace("-", "") (data["GID_ID"]);
        }, DELAY);
        return c;
    }*@

    function <EMAIL>(" ", "").Replace("-", "") (SelRecordID, viewid) {

        //get selected gid and put it onto client session to change its css style(change its color to greay when select another grid)..

        var gid_id = SelRecordID;

        GridRowColorPersistance(gid_id, viewid);
        if (gid_id != undefined)
            sessionStorage.setItem('@Model.Key' + "_" + "LastSelectedRow", gid_id);
        //get selected row index to persists its selection when comming from editform..
        var SelectedRowIndex = 1

        //persists selected record id to be selected after visit the same page(i.e., comming from editform)..J
        var viewkey = '@Model.ViewKey';
        $.ajax({

            url: '/Desktop/SaveSelectedRecordID',
            type: 'GET',
            async: false,
            cache: false,
            data: { RecordId: gid_id, SelectedRowIndex: SelectedRowIndex, ViewKey: viewkey, Key: '@Model.Key' },
            success: function (data) {

            },
            error: function (data) {
                debugger;
            }

        })

        selectedDiv2( viewid);
        $('#DefaultViewId').val('@Model.ViewId.Replace(" ", "")');
        sessionStorage.setItem('@Model.Key' + "_" + "LastGridViewId", '@Model.ViewId.Replace(" ", "")');
        //Using in create link functionality..
        sessionStorage.setItem('@Model.Key' + "_" + "CRL_RecordId", gid_id);
    }


    function selectedDiv2(viewid) {
        //debugger;
        var msg = "";
        if (msg.length == 0) {
            msg = "view" + viewid;
        }

        msg = msg.replace("grid", "view").replace("_active_cell", "");
        msg = $("#" + msg).find(".panel-heading.active");
        //$(".panel-heading.active").css({ "background": "", "color": "" });
        //$(".panel-heading.active").css({ "background": "", "color": "", "border-bottom": "" });
        $(".panel-heading.active").removeClass('ViewHeaderOn');
        $(".panel-heading.active").addClass('ViewHeaderOff');


        //msg.css({ "background": "#428bca", "color": "white" });//old
        // msg.css({ "background": "#CDCDCD", "color": "#666666", "border-bottom": "2px solid #3498DB" });
        msg.removeClass('ViewHeaderOff');
        msg.addClass('ViewHeaderOn');
        sessionStorage.setItem('@Model.Key' + "_" + "lastViewid", viewid);
        sessionStorage.setItem('@Model.Key' + "_" + "LastGridViewId", viewid);
        sessionStorage.setItem('@Model.Key' + "_" + "CurrentViewId", viewid);

    }

    function GridRowColorPersistance(gid_id, viewid) {
        RowColorPersistance(viewid);
    }

    function <EMAIL>(" ", "").Replace("-", "") (RecordID)
    {
        //debugger;
        //get selected gid and put it onto client session to change its css style(change its color to greay when select another grid)..
        var _ViewID = '@Model.ViewId.Replace(" ", "")';
        //var grid = $("#grid" + _ViewID).data("kendoGrid");
        //var selectedItem = grid.dataItem(grid.select());
        //var gid_id = selectedItem.GID_ID;
        var gid_id = RecordID;
        var view_id = '@Model.ViewId.Replace(" ", "")';

        var lastViewID = sessionStorage.getItem('@Model.Key' + "_" + "LastGridViewId");
        var lastGID_ID = sessionStorage.getItem('@Model.Key' + "_" + "LastSelectedRow");
        if (gid_id != lastGID_ID || lastViewID != _ViewID) {
            $('.panel-heading.active').parent().removeClass('z-depthstyle');
            $('#view' + _ViewID).addClass('z-depthstyle');
            //To persist the selected record id to be selected after comming from edit form..J
            <EMAIL>(" ", "").Replace("-", "") (gid_id, _ViewID);
            //using in print funcitonality..
            sessionStorage.setItem('@Model.Key' + "_" + "RecordId", gid_id);

            //Load child views..
            if (gid_id != "") {
                IsMasterViewRowSelected = true;
                $('#view' + '@Model.ViewId.Replace(" ", "")').trigger('click');
                $('#view' + '@Model.ViewId.Replace(" ", "")').focus();



                if ('@Model.DependencyViewIds' != "" && '@Model.DependencyViewIds' != null) {  @*&& '@Model.IsParent.ToString().ToLower()' == "true"*@
                    @*<EMAIL>(" ", "").Replace("-", "") (gid_id, _ViewID, 'yes');*@
                    OnLoadDependecies('@Model.ViewId.Replace(" ", "")', gid_id, '@Model.TableName', true);
                }
                <EMAIL>(" ", "").Replace("-", "") ();
            }

        }
        clicks = 0; //after action performed, reset counter
    }

    function <EMAIL>(" ", "").Replace("-", "") (gid_id, senderViewId, _rowclick, _dependencyviewidsonfocus) {
        debugger;
        var arr = viewsWith_IsTabView.split(',');
        var _selrecid = gid_id;

        //clear all views selrecids before refreshing the dependecies
        //for (var i = 0; i < arr.length; i++) {
        //    var viewInfo = arr[i].split("|");
        //    var viewId = viewInfo[0].replace(" ", "");
        //    $("#hidSelRecId_" + viewId).val("");
        //}

        $("#hidSelRecId_" + senderViewId).val(gid_id);

        var dependenceviewids;

        if (_rowclick == 'yes' && _dependencyviewidsonfocus == 'yes') {
            dependenceviewids = $("#hidDepViewsOnFocus_" + senderViewId).val();
        }
        else if (_rowclick == 'yes' || (_rowclick == 'no' && _dependencyviewidsonfocus == 'yes')) {
            dependenceviewids = $("#hidDepViews_" + senderViewId).val() + "," + $("#hidDepViewsOnFocus_" + senderViewId).val();
        }
        else {
            dependenceviewids = $("#hidDepViews_" + senderViewId).val();
        }

        var lsViewIds = dependenceviewids.split(",");

        for (var i = 0; i < lsViewIds.length; i++) {
            var isValidView = true;

            if (TabViewIds.indexOf(lsViewIds[i]) >= 0) {
                var arr = TabViewIds.split(',');
                for (var k = 0; k < arr.length; k++) {
                    if (arr[k] == lsViewIds[i]) {
                        var activeTabId = $('#hdnActive').val();
                        if (lsViewIds[i] != activeTabId) {
                            isValidView = false;
                            break;
                        }
                    }
                }
            }

            var viewId = lsViewIds[i].replace(" ", "");
            if (viewId == senderViewId) {
                continue;
            }

            if (isValidView) {

                var Parent_SelectedIds = "";
                if (_rowclick == "yes") {

                    Parent_SelectedIds = GetMasterViewSelectedIds(lsViewIds[i], viewids_With_ParentIds, gid_id, senderViewId);
                }
                else
                    Parent_SelectedIds = GetMasterViewSelectedIds(lsViewIds[i], viewids_With_ParentIds);

                _selrecid = Parent_SelectedIds;
                // gid_id = Parent_SelectedIds;

                var viewtype = $("#hidViewType_" + viewId.replace(" ", "")).val();

                if (viewtype == null || viewtype == undefined || viewtype == "undefined") {
                    var viewIds_and_viewtypes = $("#hidViewIds_and_Type").val();
                    var ids = viewIds_and_viewtypes.split("|");

                    for (var j = 0; j < ids.length; j++) {
                        var types = ids[j].split(",");
                        if (types[0].replace(" ", "") == viewId.replace(" ", "")) {
                            viewtype = types[1];
                            break;
                        }
                    }

                }

                var grid_report_object = null;

                if (viewtype == "list") {
                    grid_report_object = $("#grid" + viewId.replace(" ", "")).data("kendoGrid");
                }
                else if (viewtype == "report") {
                    grid_report_object = $("#grid" + viewId.replace(" ", "")).data("kendoTreeList");
                }
                else if (viewtype == "calendar") {
                    grid_report_object = $("#calender" + viewId.replace(" ", "")).data("kendoScheduler");
                }

                if (grid_report_object) {
                    //view already rendered so call the read action
                    if (viewtype == "list") {
                        refreshGrid(viewId, _selrecid, _rowclick);
                        continue;
                    }
                    else if (viewtype == "report") {
                        reloadReport(viewId, _selrecid, _rowclick);
                        continue;
                    }
                    else if (viewtype == "calendar") {
                        refreshCalendar(viewId, _selrecid, _rowclick);
                        continue;
                    }
                    else if (viewtype == "map") {
                        reloadMap(viewId, _selrecid, _rowclick);
                        continue;
                    }
                }
                else {
                    //view is not loaded yet so call the 'reloadview'
                    if (viewtype == "list") {
                        reloadGrid(viewId, _selrecid, _rowclick);
                        continue;
                    }
                    else if (viewtype == "report") {
                        reloadReport(viewId, _selrecid, _rowclick);
                        continue;
                    }
                    else if (viewtype == "calendar") {
                        reloadCalendar(viewId, _selrecid, _rowclick);
                        continue;
                    }
                    else if (viewtype == "map") {
                        reloadMap(viewId, _selrecid, _rowclick);
                        //google.maps.event.trigger(<EMAIL>(" ", "").Replace("-", ""), "resize");
                        continue;
                    }
                }

                var chart = $("#Chart" + viewId.replace(" ", "")).data("kendoChart");

                if (viewtype == "chart" || viewtype == "CHART") {
                    var desktopid = '@Selltis.Core.Util.GetSessionValue("DesktopId")';
                    var section = "GLOBAL";
                    //var index = i + 1;
                    var masterViewSelRecord = gid_id;

                    if (desktopid != null) {
                        $.ajax({
                            url: '/Desktop/LoadLinkedChart',
                            cache: false,
                            dataType: "json",
                            type: "GET",
                            async: false,
                            contentType: 'application/json; charset=utf-8',
                            data: { ViewKey: viewId, DesktopId: desktopid, Section: section, MasterViewSelRecord: masterViewSelRecord, IsTabView: false, Key: '@Model.Key' },
                            success: function (data) {
                                if (data != 'failure') {
                                    $('#viewbody' + viewId.replace('/ /g', '')).html(data);
                                    ResizeChartInsplitter(viewId);
                                }
                            },
                            error: function (data) {
                                alert(responseText);
                            }
                        })
                    }
                }
            }
        }
    }

    function OnLoadDependecies(_ViewID, gid_id, TableName, rowclick, runDependenciesOnFocus) {

        if (runDependenciesOnFocus == undefined) {
            runDependenciesOnFocus = false;
        }

        showProgress();
        $.ajax({
            url: '/Desktop/SetSelectedRecordIdAndLoadDataSets',
            cache: false,
            type: "GET",
            data: { ViewKey: _ViewID, SelectedRecordId: gid_id, FileName: TableName, Key: Key },
            success: function (data) {

                $("#hidSelRecId_" + _ViewID).val(gid_id);

                if (data != null && data != "" && data != '') {

                    LoadDependencyViewsV2(data, _ViewID, "", rowclick, runDependenciesOnFocus);
                    MakeChildDependencyViewsOnFocus_AsEmpty(_ViewID);

                    hideProgress();
                }
                hideProgress();
            },
            error: function (data) {
                hideProgress();
            }
        })
    }


    function <EMAIL>(" ", "").Replace("-", "") () {
        $.ajax({
            url: '/Desktop/GetSortedFieldControl',
            type: "GET",
            data: { datafield: "", ViewKey: '@Model.ViewId.Replace(" ","")', Key: '@Model.Key' },
            success: function (data) {
                if (data.FilterText != null) {

                    $("#QuickFilterBar").html("");
                    $("#QuickFilterBar").html(data.FilterText);

                    SetDocumentHeight();

                    if ($("#QuickSelectCONDITION").length >= 1) {
                        //document.getElementById("QuickSelectCONDITION").focus();
                    }
                    var myval = '@Model.ViewId.Replace(" ", "")';
                    var _tooltip = data.ToolTip;
                    $("#" + data.TableName + myval).attr("title", _tooltip)

                }
            }
        })

    }

    function <EMAIL>(" ", "").Replace("-", "") () {
        $("#viewdatacount" + '@Model.ViewId.Replace(" ", "")').text(<EMAIL>(" ", "").Replace("-", "") .length.toString());
    }

</script>
